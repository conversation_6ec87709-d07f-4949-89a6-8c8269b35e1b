@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      'Inter',
      system-ui,
      -apple-system,
      sans-serif;
  }
}

/* Custom styles for auth components */
@layer components {
  /* Auth form container */
  .auth-container {
    @apply bg-card dark:bg-card mx-auto max-w-sm space-y-6 rounded-xl p-6 shadow-lg;
  }

  /* Auth headings */
  .auth-heading {
    @apply text-foreground text-2xl font-bold tracking-tight;
  }

  .auth-subheading {
    @apply text-muted-foreground text-sm;
  }

  /* Auth labels */
  .auth-label {
    @apply text-foreground text-sm font-medium;
  }

  /* Auth inputs */
  .auth-input {
    @apply border-input bg-background text-foreground w-full rounded-md border px-3 py-2 text-sm;
    transition: border-color 0.15s ease-in-out;
  }

  .auth-input:focus {
    @apply border-primary ring-primary/20 ring-2 outline-none;
  }

  /* Auth buttons */
  .auth-button-primary {
    @apply w-full rounded-md bg-[#3b82f6] px-4 py-2 text-sm font-medium text-white;
    transition: background-color 0.15s ease-in-out;
  }

  .auth-button-primary:hover:not(:disabled) {
    @apply bg-[#2563eb];
  }

  .auth-button-primary:disabled {
    @apply cursor-not-allowed opacity-50;
  }

  /* Google button */
  .auth-button-google {
    @apply border-input bg-background text-foreground hover:bg-accent hover:text-accent-foreground w-full rounded-md border px-4 py-2 text-sm font-medium;
    transition: all 0.15s ease-in-out;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  /* Auth separator */
  .auth-separator {
    @apply text-muted-foreground text-xs uppercase;
  }

  /* Error message */
  .auth-error {
    @apply bg-destructive/10 dark:bg-destructive/20 text-destructive rounded-md p-3 text-sm;
  }

  /* Success message */
  .auth-success {
    @apply rounded-md bg-green-50 p-3 text-sm text-green-600 dark:bg-green-950 dark:text-green-400;
  }
}

/* Animation for expanding commentary boxes */
@keyframes expand {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 200px;
  }
}

@keyframes expand-inside {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 150px;
  }
}

.animate-expand {
  animation: expand 0.3s ease-out forwards;
}

.animate-expand-inside {
  animation: expand-inside 0.4s ease-out forwards;
}

/* Custom styles for rendered content */
@layer base {
  /* Paragraph spacing in rendered content */
  .rendered-content p + p,
  [data-rendered-content] p + p {
    @apply mt-4;
  }

  /* Heading styles for rendered content */
  .rendered-content h1,
  [data-rendered-content] h1 {
    @apply text-foreground mt-8 mb-4 text-2xl leading-tight font-bold;
  }

  .rendered-content h2,
  [data-rendered-content] h2 {
    @apply text-foreground mt-7 mb-3 text-xl leading-tight font-bold;
  }

  .rendered-content h3,
  [data-rendered-content] h3 {
    @apply text-foreground mt-6 mb-3 text-lg leading-tight font-bold;
  }

  .rendered-content h4,
  [data-rendered-content] h4 {
    @apply text-foreground mt-5 mb-2 text-base leading-tight font-bold;
  }

  .rendered-content h5,
  [data-rendered-content] h5 {
    @apply text-foreground mt-4 mb-2 text-sm leading-tight font-bold;
  }

  .rendered-content h6,
  [data-rendered-content] h6 {
    @apply text-foreground mt-3 mb-2 text-xs leading-tight font-bold;
  }

  /* Remove top margin from first heading */
  .rendered-content h1:first-child,
  .rendered-content h2:first-child,
  .rendered-content h3:first-child,
  .rendered-content h4:first-child,
  .rendered-content h5:first-child,
  .rendered-content h6:first-child,
  [data-rendered-content] h1:first-child,
  [data-rendered-content] h2:first-child,
  [data-rendered-content] h3:first-child,
  [data-rendered-content] h4:first-child,
  [data-rendered-content] h5:first-child,
  [data-rendered-content] h6:first-child {
    @apply mt-0;
  }

  /* Ensure proper spacing between headings and following elements */
  .rendered-content h1 + p,
  .rendered-content h2 + p,
  .rendered-content h3 + p,
  .rendered-content h4 + p,
  .rendered-content h5 + p,
  .rendered-content h6 + p,
  [data-rendered-content] h1 + p,
  [data-rendered-content] h2 + p,
  [data-rendered-content] h3 + p,
  [data-rendered-content] h4 + p,
  [data-rendered-content] h5 + p,
  [data-rendered-content] h6 + p {
    @apply mt-0;
  }

  /* Small text styling for references and citations */
  .rendered-content small,
  [data-rendered-content] small {
    @apply text-muted-foreground text-xs leading-relaxed;
  }

  /* Add spacing for small text when used as block elements */
  .rendered-content p small,
  [data-rendered-content] p small {
    @apply mt-3 block;
  }

  /* List styles for rendered content */
  .rendered-content ul,
  .rendered-content ol,
  [data-rendered-content] ul,
  [data-rendered-content] ol {
    @apply my-4 ml-6 space-y-2;
  }

  .rendered-content li,
  [data-rendered-content] li {
    @apply text-foreground leading-relaxed;
    position: relative;
  }

  /* Unordered list styling */
  .rendered-content ul,
  [data-rendered-content] ul {
    list-style-type: disc;
  }

  .rendered-content ul ul,
  [data-rendered-content] ul ul {
    list-style-type: circle;
    @apply mt-2 mb-2;
  }

  .rendered-content ul ul ul,
  [data-rendered-content] ul ul ul {
    list-style-type: square;
  }

  /* Ordered list styling */
  .rendered-content ol,
  [data-rendered-content] ol {
    list-style-type: decimal;
  }

  /* Support for different ordered list types */
  .rendered-content ol[type='1'],
  [data-rendered-content] ol[type='1'] {
    list-style-type: decimal;
  }

  .rendered-content ol[type='a'],
  [data-rendered-content] ol[type='a'] {
    list-style-type: lower-alpha;
  }

  .rendered-content ol[type='A'],
  [data-rendered-content] ol[type='A'] {
    list-style-type: upper-alpha;
  }

  .rendered-content ol[type='i'],
  [data-rendered-content] ol[type='i'] {
    list-style-type: lower-roman;
  }

  .rendered-content ol[type='I'],
  [data-rendered-content] ol[type='I'] {
    list-style-type: upper-roman;
  }

  /* Nested list styling */
  .rendered-content li > ul,
  .rendered-content li > ol,
  [data-rendered-content] li > ul,
  [data-rendered-content] li > ol {
    @apply mt-2 mb-0;
  }

  /* List item content spacing */
  .rendered-content li p,
  [data-rendered-content] li p {
    @apply mb-2;
  }

  .rendered-content li p:last-child,
  [data-rendered-content] li p:last-child {
    @apply mb-0;
  }

  /* Remove top margin from first list after heading */
  .rendered-content h1 + ul,
  .rendered-content h1 + ol,
  .rendered-content h2 + ul,
  .rendered-content h2 + ol,
  .rendered-content h3 + ul,
  .rendered-content h3 + ol,
  .rendered-content h4 + ul,
  .rendered-content h4 + ol,
  .rendered-content h5 + ul,
  .rendered-content h5 + ol,
  .rendered-content h6 + ul,
  .rendered-content h6 + ol,
  [data-rendered-content] h1 + ul,
  [data-rendered-content] h1 + ol,
  [data-rendered-content] h2 + ul,
  [data-rendered-content] h2 + ol,
  [data-rendered-content] h3 + ul,
  [data-rendered-content] h3 + ol,
  [data-rendered-content] h4 + ul,
  [data-rendered-content] h4 + ol,
  [data-rendered-content] h5 + ul,
  [data-rendered-content] h5 + ol,
  [data-rendered-content] h6 + ul,
  [data-rendered-content] h6 + ol {
    @apply mt-2;
  }

  /* Table styles for rendered content */
  .rendered-content table,
  [data-rendered-content] table {
    @apply border-border bg-background mx-auto my-4 w-auto border-collapse overflow-hidden rounded-lg border;
  }

  .rendered-content th,
  .rendered-content td,
  [data-rendered-content] th,
  [data-rendered-content] td {
    @apply border-border border px-4 py-2 text-left;
  }

  .rendered-content th,
  [data-rendered-content] th {
    @apply bg-muted text-foreground font-semibold;
  }

  .rendered-content td,
  [data-rendered-content] td {
    @apply text-foreground;
  }

  /* Alternate row styling for better readability */
  .rendered-content tbody tr:nth-child(even),
  [data-rendered-content] tbody tr:nth-child(even) {
    @apply bg-muted/30;
  }

  /* Pre-formatted text blocks */
  .rendered-content pre,
  [data-rendered-content] pre {
    @apply bg-muted my-4 overflow-x-auto rounded-lg p-4 font-mono text-sm;
  }

  /* Code inline */
  .rendered-content code:not(pre code),
  [data-rendered-content] code:not(pre code) {
    @apply bg-muted rounded px-1.5 py-0.5 font-mono text-sm;
  }

  /* Blockquote styling */
  .rendered-content blockquote,
  [data-rendered-content] blockquote {
    @apply border-muted-foreground/30 bg-muted/30 my-4 rounded-r-lg border-l-4 py-3 pr-3 pl-4;
    @apply text-foreground/90 italic;
  }

  /* Nested blockquotes */
  .rendered-content blockquote blockquote,
  [data-rendered-content] blockquote blockquote {
    @apply mt-3 mb-0;
  }

  /* Blockquote paragraph spacing */
  .rendered-content blockquote p:last-child,
  [data-rendered-content] blockquote p:last-child {
    @apply mb-0;
  }

  .rendered-content blockquote p:first-child,
  [data-rendered-content] blockquote p:first-child {
    @apply mt-0;
  }

  /* Blockquote with citations or attributions */
  .rendered-content blockquote cite,
  [data-rendered-content] blockquote cite {
    @apply text-muted-foreground mt-2 block text-sm not-italic;
  }

  /* Matrix brackets - subtle and clean */
  .rendered-content .katex-display .katex .delimsizing,
  .rendered-content .katex-display .katex .delim-size1,
  .rendered-content .katex-display .katex .delim-size2,
  .rendered-content .katex-display .katex .delim-size3,
  .rendered-content .katex-display .katex .delim-size4,
  [data-rendered-content] .katex-display .katex .delimsizing,
  [data-rendered-content] .katex-display .katex .delim-size1,
  [data-rendered-content] .katex-display .katex .delim-size2,
  [data-rendered-content] .katex-display .katex .delim-size3,
  [data-rendered-content] .katex-display .katex .delim-size4 {
    color: var(--foreground) !important;
    opacity: 1 !important;
    font-weight: 400 !important;
    font-size: 1em !important;
  }

  /* Matrix bracket containers */
  .rendered-content .katex-display .katex .mopen,
  .rendered-content .katex-display .katex .mclose,
  [data-rendered-content] .katex-display .katex .mopen,
  [data-rendered-content] .katex-display .katex .mclose {
    color: var(--foreground) !important;
    font-weight: 400 !important;
    font-size: 1em !important;
  }

  /* Matrix container - clean and centered */
  .rendered-content .katex-display,
  [data-rendered-content] .katex-display {
    text-align: center !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Remove all spacing from KaTeX root elements */
  .rendered-content .katex,
  [data-rendered-content] .katex {
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Matrix table layout - properly centered with generous spacing */
  .rendered-content .katex-display .katex .mtable,
  [data-rendered-content] .katex-display .katex .mtable {
    display: inline-table !important;
    vertical-align: middle !important;
    margin: 0 !important;
    border-spacing: 0.4em 0.5em !important;
  }

  /* Matrix rows with generous spacing */
  .rendered-content .katex-display .katex .mtr,
  [data-rendered-content] .katex-display .katex .mtr {
    display: table-row !important;
    line-height: 2 !important;
  }

  /* Add significant spacing between rows */
  .rendered-content .katex-display .katex .mtr + .mtr,
  [data-rendered-content] .katex-display .katex .mtr + .mtr {
    border-top: 0.8em solid transparent !important;
  }

  /* Matrix cells - consistent spacing for fractions */
  .rendered-content .katex-display .katex .mtd,
  [data-rendered-content] .katex-display .katex .mtd {
    display: table-cell !important;
    padding: 1.2em 1.5em !important;
    text-align: center !important;
    vertical-align: middle !important;
    min-width: 5em !important;
    height: 4em !important;
    box-sizing: border-box !important;
    position: relative !important;
  }

  /* Force all matrix content to align vertically */
  .rendered-content .katex-display .katex .mtd *,
  [data-rendered-content] .katex-display .katex .mtd * {
    vertical-align: middle !important;
  }

  /* Matrix elements - center all content */
  .rendered-content .katex-display .katex .mtd > .mord,
  [data-rendered-content] .katex-display .katex .mtd > .mord {
    display: inline-block !important;
    vertical-align: middle !important;
  }

  /* Make integers larger and center them with fractions */
  .rendered-content .katex-display .katex .mtd .mord:not(.mfrac),
  [data-rendered-content] .katex-display .katex .mtd .mord:not(.mfrac) {
    font-size: 1.3em !important;
    vertical-align: middle !important;
    line-height: 1 !important;
    display: inline-block !important;
    transform: translateY(-0.1em) !important;
  }

  /* Ensure fractions are properly aligned */
  .rendered-content .katex-display .katex .mtd .mfrac,
  [data-rendered-content] .katex-display .katex .mtd .mfrac {
    vertical-align: middle !important;
    display: inline-block !important;
  }

  /* Fraction styling within matrices - consistent sizing */
  .rendered-content .katex-display .katex .mfrac,
  [data-rendered-content] .katex-display .katex .mfrac {
    font-size: 0.9em !important;
    vertical-align: baseline !important;
  }

  /* Ensure fraction numerator and denominator have consistent spacing */
  .rendered-content .katex-display .katex .mfrac > .frac-line,
  [data-rendered-content] .katex-display .katex .mfrac > .frac-line {
    margin: 0.1em 0 !important;
  }

  /* Center the entire matrix structure */
  .rendered-content .katex-display .katex .base,
  [data-rendered-content] .katex-display .katex .base {
    text-align: center !important;
  }

  /* Add spacing between brackets and matrix content */
  .rendered-content .katex-display .katex .minner,
  [data-rendered-content] .katex-display .katex .minner {
    margin: 0 0.15em !important;
  }

  /* Additional spacing for opening bracket */
  .rendered-content .katex-display .katex .mopen,
  [data-rendered-content] .katex-display .katex .mopen {
    margin-right: 0.1em !important;
  }

  /* Additional spacing for closing bracket */
  .rendered-content .katex-display .katex .mclose,
  [data-rendered-content] .katex-display .katex .mclose {
    margin-left: 0.1em !important;
  }
}

/* Shimmer animation for skeleton loading */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Subtle bounce animation for badge */
@keyframes bounce-subtle {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

.animate-bounce-subtle {
  animation: bounce-subtle 0.3s ease-in-out;
}
