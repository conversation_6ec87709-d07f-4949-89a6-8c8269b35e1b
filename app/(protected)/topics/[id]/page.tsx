'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  BookOpen,
  TrendingUp,
  Building2,
  BarChart,
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  Target,
} from 'lucide-react';
import { QuestionCard } from '@/components/question-card';
import { Question } from '@/types/question';
import { QuestionCardSkeleton } from '@/components/question-card-skeleton';

interface TopicDetail {
  id: string;
  name: string;
  subject: {
    id: string;
    name: string;
  };
  description: string;
  totalQuestions: number;
  totalExams: number;
  averageDifficulty: number;
  successRate: number;
  growthRate: number;
  yearlyDistribution: {
    year: number;
    questionCount: number;
    successRate: number;
  }[];
  examBoards: {
    id: string;
    name: string;
    questionCount: number;
    percentage: number;
    difficulty: number;
  }[];
  topInstitutions: {
    id: string;
    name: string;
    acronym?: string;
    questionCount: number;
    lastExam: string;
  }[];
  relatedTopics: {
    id: string;
    name: string;
    subject: string;
    similarity: number;
  }[];
  commonMistakes: {
    type: string;
    description: string;
    frequency: number;
  }[];
  studyTips: {
    title: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
  }[];
  keywordFrequency: {
    keyword: string;
    count: number;
  }[];
}

export default function TopicDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [topic, setTopic] = useState<TopicDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(false);

  useEffect(() => {
    const fetchTopic = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`/api/topics/${params.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Tópico não encontrado');
          } else {
            throw new Error('Failed to fetch topic');
          }
          return;
        }

        const data = await response.json();
        setTopic(data);
      } catch (error) {
        console.error('Error fetching topic:', error);
        setError('Erro ao carregar dados do tópico');
      } finally {
        setLoading(false);
      }
    };

    fetchTopic();
  }, [params.id]);

  const fetchQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const response = await fetch(`/api/questions?topic=${topic?.name}&limit=5`);

      if (!response.ok) {
        throw new Error('Failed to fetch questions');
      }

      const data = await response.json();
      setQuestions(data.questions || []);
    } catch (error) {
      console.error('Error fetching questions:', error);
    } finally {
      setLoadingQuestions(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Skeleton className="mb-2 h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error || !topic) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Alert className="mx-auto mb-4 max-w-md">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error || 'Tópico não encontrado'}</AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/topics')}>Voltar para Tópicos</Button>
        </div>
      </div>
    );
  }

  const maxYearlyQuestions = Math.max(...topic.yearlyDistribution.map((y) => y.questionCount));

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button variant="ghost" size="sm" onClick={() => router.push('/topics')} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar para Tópicos
        </Button>

        <div className="flex items-start justify-between">
          <div>
            <h1 className="mb-2 text-3xl font-bold">{topic.name}</h1>
            <div className="mb-3 flex items-center gap-2">
              <Badge
                variant="outline"
                className="hover:bg-accent cursor-pointer"
                onClick={() => router.push(`/subjects/${topic.subject.id}`)}
              >
                {topic.subject.name}
              </Badge>
            </div>
            <p className="text-muted-foreground max-w-3xl">{topic.description}</p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <BookOpen className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">
                {topic.totalQuestions.toLocaleString('pt-BR')}
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Taxa de Acerto</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Target className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{topic.successRate}%</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Dificuldade</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <BarChart className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{topic.averageDifficulty.toFixed(1)}/5</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Crescimento</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <TrendingUp className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                +{topic.growthRate}%
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="study">Guia de Estudo</TabsTrigger>
          <TabsTrigger value="statistics">Estatísticas</TabsTrigger>
          <TabsTrigger value="institutions">Instituições</TabsTrigger>
          <TabsTrigger value="questions">Questões</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Yearly Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Evolução Anual</CardTitle>
              <CardDescription>
                Número de questões e taxa de acerto ao longo dos anos
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex h-48 items-end gap-1">
                  {topic.yearlyDistribution.map((year) => {
                    const height = (year.questionCount / maxYearlyQuestions) * 100;
                    const successColor = year.successRate > 50 ? 'bg-green-500' : 'bg-orange-500';
                    return (
                      <div key={year.year} className="flex flex-1 flex-col items-center gap-1">
                        <div className="mb-1 text-xs font-medium">{year.questionCount}</div>
                        <div
                          className="bg-primary/20 hover:bg-primary/30 group relative w-full cursor-pointer rounded-t transition-colors"
                          style={{ height: `${height}%` }}
                          onClick={() => router.push(`/years/${year.year}`)}
                        >
                          <div className="bg-popover text-popover-foreground absolute -top-8 left-1/2 -translate-x-1/2 rounded px-2 py-1 text-xs whitespace-nowrap opacity-0 transition-opacity group-hover:opacity-100">
                            Taxa: {year.successRate}%
                          </div>
                          <div
                            className={`absolute right-0 bottom-0 left-0 h-1 ${successColor} opacity-70`}
                          />
                        </div>
                        <span className="text-muted-foreground text-xs">{year.year}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Keywords */}
          <Card>
            <CardHeader>
              <CardTitle>Palavras-chave Frequentes</CardTitle>
              <CardDescription>Termos mais comuns nas questões deste tópico</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {topic.keywordFrequency.map((keyword) => (
                  <Badge
                    key={keyword.keyword}
                    variant="secondary"
                    className="text-sm"
                    style={{
                      fontSize: `${Math.min(0.8 + keyword.count / 500, 1.2)}rem`,
                      opacity: Math.min(0.6 + keyword.count / 500, 1),
                    }}
                  >
                    {keyword.keyword} ({keyword.count})
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Related Topics */}
          <Card>
            <CardHeader>
              <CardTitle>Tópicos Relacionados</CardTitle>
              <CardDescription>Tópicos frequentemente cobrados em conjunto</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {topic.relatedTopics.map((related) => (
                  <div
                    key={related.id}
                    className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-3"
                    onClick={() => router.push(`/topics/${related.id}`)}
                  >
                    <div>
                      <p className="font-medium">{related.name}</p>
                      <p className="text-muted-foreground text-sm">{related.subject}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground text-sm">Similaridade:</span>
                      <Badge variant="secondary">{Math.round(related.similarity * 100)}%</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="study" className="space-y-4">
          {/* Study Tips */}
          <Card>
            <CardHeader>
              <CardTitle>Dicas de Estudo</CardTitle>
              <CardDescription>Recomendações para dominar este tópico</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topic.studyTips.map((tip, index) => (
                  <div
                    key={index}
                    className={`rounded-lg border p-4 ${
                      tip.priority === 'high'
                        ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
                        : tip.priority === 'medium'
                          ? 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-900/20'
                          : 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <CheckCircle
                        className={`mt-0.5 h-5 w-5 ${
                          tip.priority === 'high'
                            ? 'text-red-600 dark:text-red-400'
                            : tip.priority === 'medium'
                              ? 'text-yellow-600 dark:text-yellow-400'
                              : 'text-green-600 dark:text-green-400'
                        }`}
                      />
                      <div>
                        <p className="font-medium">{tip.title}</p>
                        <p className="text-muted-foreground mt-1 text-sm">{tip.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Common Mistakes */}
          <Card>
            <CardHeader>
              <CardTitle>Erros Comuns</CardTitle>
              <CardDescription>Principais armadilhas a evitar</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topic.commonMistakes.map((mistake, index) => (
                  <div key={index} className="rounded-lg border p-4">
                    <div className="flex items-start gap-3">
                      <AlertCircle className="mt-0.5 h-5 w-5 text-red-500" />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">{mistake.type}</p>
                          <Badge variant="destructive">{mistake.frequency}% dos erros</Badge>
                        </div>
                        <p className="text-muted-foreground mt-1 text-sm">{mistake.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics" className="space-y-4">
          {/* Exam Boards Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Distribuição por Banca</CardTitle>
              <CardDescription>Como cada banca aborda este tópico</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topic.examBoards.map((board) => (
                  <div
                    key={board.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/exam-boards/${board.id}`)}
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <div>
                        <span className="font-medium">{board.name}</span>
                        <Badge variant="outline" className="ml-2">
                          Dificuldade: {board.difficulty.toFixed(1)}
                        </Badge>
                      </div>
                      <span className="text-muted-foreground text-sm">
                        {board.questionCount} ({board.percentage}%)
                      </span>
                    </div>
                    <div className="bg-muted h-2 overflow-hidden rounded-full">
                      <div
                        className="bg-primary/60 h-full"
                        style={{ width: `${board.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Métricas de Desempenho</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="rounded-lg bg-green-50 p-4 dark:bg-green-900/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-muted-foreground text-sm">Taxa de Acerto Geral</p>
                      <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {topic.successRate}%
                      </p>
                    </div>
                    <Target className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                </div>
                <div className="rounded-lg bg-orange-50 p-4 dark:bg-orange-900/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-muted-foreground text-sm">Nível de Dificuldade</p>
                      <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                        {topic.averageDifficulty.toFixed(1)}/5
                      </p>
                    </div>
                    <BarChart className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="institutions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Instituições que Mais Cobram</CardTitle>
              <CardDescription>Órgãos com mais questões deste tópico</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {topic.topInstitutions.map((institution) => (
                  <div
                    key={institution.id}
                    className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-3"
                    onClick={() => router.push(`/institutions/${institution.id}`)}
                  >
                    <div className="flex items-center gap-3">
                      <Building2 className="text-muted-foreground h-5 w-5" />
                      <div>
                        <p className="font-medium">
                          {institution.acronym ? `${institution.acronym} - ` : ''}
                          {institution.name}
                        </p>
                        <p className="text-muted-foreground text-sm">
                          {institution.questionCount} questões
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline">Último: {institution.lastExam}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          {questions.length === 0 && !loadingQuestions ? (
            <Card>
              <CardContent className="py-12 text-center">
                <BookOpen className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">Questões sobre {topic.name}</h3>
                <p className="text-muted-foreground mb-4">Veja exemplos de questões deste tópico</p>
                <Button onClick={fetchQuestions}>Ver Questões de Exemplo</Button>
              </CardContent>
            </Card>
          ) : loadingQuestions ? (
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <QuestionCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="space-y-6">
              {questions.map((question) => (
                <QuestionCard
                  key={question.id}
                  questionNumber={question.number}
                  examBoard={question.exam.examBoard || undefined}
                  examBoardId={question.exam.examBoardId}
                  institution={question.institution?.name || question.exam.name}
                  institutionId={question.institution?.id}
                  year={question.exam.year || undefined}
                  position={question.position?.name}
                  positionId={question.position?.id}
                  specialization={question.specialization?.name}
                  specializationId={question.specialization?.id}
                  subject={question.subject?.name || ''}
                  subjectId={question.subject?.id}
                  topic={question.topic?.name || ''}
                  topicId={question.topic?.id}
                  questionText={question.stem}
                  associatedText={question.associatedText}
                  options={question.options.map((opt) => ({
                    letter: opt.optionLetter,
                    text: opt.text,
                  }))}
                  correctAnswer={question.options.find((opt) => opt.isCorrect)?.optionLetter}
                  hideNavigation
                  totalQuestions={questions.length}
                  commentCount={question.commentCount}
                />
              ))}
              {questions.length > 0 && (
                <div className="pt-4 text-center">
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/questions?topic=${topic.name}`)}
                  >
                    Ver Mais Questões
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
