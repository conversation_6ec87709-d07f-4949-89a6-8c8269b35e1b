'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { BookOpen, TrendingUp, Search, FileText, BarChart } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface Topic {
  id: string;
  name: string;
  subjectId: string;
  subjectName: string;
  questionCount: number;
  examCount: number;
  averageDifficulty: number;
  successRate: number;
  popularExamBoards: {
    name: string;
    count: number;
  }[];
  relatedTopics: string[];
  yearlyGrowth: number; // percentage
  lastUpdated: string;
}

// Mock data - replace with actual API call
const mockTopics: Topic[] = [
  {
    id: 'interpretacao-texto',
    name: 'Interpretação de Texto',
    subjectId: 'lingua-portuguesa',
    subjectName: 'Língua Portuguesa',
    questionCount: 4250,
    examCount: 178,
    averageDifficulty: 3.2,
    successRate: 58.4,
    popularExamBoards: [
      { name: 'CESPE', count: 1450 },
      { name: 'FCC', count: 1230 },
      { name: 'FGV', count: 980 },
    ],
    relatedTopics: ['Compreensão Textual', 'Inferência', 'Coesão e Coerência'],
    yearlyGrowth: 12.5,
    lastUpdated: '2024-10-15',
  },
  {
    id: 'licitacoes-contratos',
    name: 'Licitações e Contratos',
    subjectId: 'direito-administrativo',
    subjectName: 'Direito Administrativo',
    questionCount: 3200,
    examCount: 156,
    averageDifficulty: 4.2,
    successRate: 38.2,
    popularExamBoards: [
      { name: 'FCC', count: 1080 },
      { name: 'CESPE', count: 950 },
      { name: 'FGV', count: 720 },
    ],
    relatedTopics: ['Lei 8666/93', 'Pregão', 'Modalidades de Licitação'],
    yearlyGrowth: 8.3,
    lastUpdated: '2024-10-18',
  },
  {
    id: 'porcentagem',
    name: 'Porcentagem',
    subjectId: 'matematica',
    subjectName: 'Matemática',
    questionCount: 2100,
    examCount: 142,
    averageDifficulty: 3.5,
    successRate: 52.1,
    popularExamBoards: [
      { name: 'VUNESP', count: 620 },
      { name: 'FCC', count: 580 },
      { name: 'CESPE', count: 450 },
    ],
    relatedTopics: ['Aumentos e Descontos', 'Juros Simples', 'Proporção'],
    yearlyGrowth: 5.2,
    lastUpdated: '2024-10-20',
  },
  {
    id: 'direitos-garantias',
    name: 'Direitos e Garantias Fundamentais',
    subjectId: 'direito-constitucional',
    subjectName: 'Direito Constitucional',
    questionCount: 2450,
    examCount: 168,
    averageDifficulty: 3.9,
    successRate: 45.7,
    popularExamBoards: [
      { name: 'CESPE', count: 850 },
      { name: 'FCC', count: 720 },
      { name: 'FGV', count: 580 },
    ],
    relatedTopics: ['Art. 5º CF', 'Remédios Constitucionais', 'Direitos Sociais'],
    yearlyGrowth: 10.1,
    lastUpdated: '2024-10-22',
  },
  {
    id: 'logica-proposicional',
    name: 'Lógica Proposicional',
    subjectId: 'raciocinio-logico',
    subjectName: 'Raciocínio Lógico',
    questionCount: 2100,
    examCount: 125,
    averageDifficulty: 4.1,
    successRate: 39.8,
    popularExamBoards: [
      { name: 'CESPE', count: 780 },
      { name: 'FCC', count: 620 },
      { name: 'FGV', count: 480 },
    ],
    relatedTopics: ['Tabela Verdade', 'Conectivos Lógicos', 'Equivalências'],
    yearlyGrowth: 15.3,
    lastUpdated: '2024-10-19',
  },
  {
    id: 'windows',
    name: 'Windows',
    subjectId: 'informatica',
    subjectName: 'Informática',
    questionCount: 1850,
    examCount: 112,
    averageDifficulty: 3.3,
    successRate: 54.6,
    popularExamBoards: [
      { name: 'FCC', count: 580 },
      { name: 'CESPE', count: 520 },
      { name: 'VUNESP', count: 420 },
    ],
    relatedTopics: ['Windows 10', 'Atalhos', 'Configurações'],
    yearlyGrowth: -2.1,
    lastUpdated: '2024-10-17',
  },
];

const mockSubjects = [
  { id: 'todos', name: 'Todas as Matérias' },
  { id: 'lingua-portuguesa', name: 'Língua Portuguesa' },
  { id: 'direito-administrativo', name: 'Direito Administrativo' },
  { id: 'matematica', name: 'Matemática' },
  { id: 'direito-constitucional', name: 'Direito Constitucional' },
  { id: 'raciocinio-logico', name: 'Raciocínio Lógico' },
  { id: 'informatica', name: 'Informática' },
];

export default function TopicsPage() {
  const router = useRouter();
  const [topics, setTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('todos');

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setTopics(mockTopics);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredTopics = topics.filter((topic) => {
    const matchesSearch =
      topic.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      topic.subjectName.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesSubject = selectedSubject === 'todos' || topic.subjectId === selectedSubject;
    return matchesSearch && matchesSubject;
  });

  const totalQuestions = topics.reduce((sum, topic) => sum + topic.questionCount, 0);
  const avgDifficulty =
    topics.reduce((sum, topic) => sum + topic.averageDifficulty, 0) / topics.length;
  const avgSuccessRate = topics.reduce((sum, topic) => sum + topic.successRate, 0) / topics.length;

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Tópicos</h1>
          <p className="text-muted-foreground mt-2">Explore questões por tópicos específicos</p>
        </div>

        {/* Loading skeleton for stats */}
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Loading skeleton for filters */}
        <div className="mb-6 flex flex-col gap-4 md:flex-row">
          <Skeleton className="h-10 w-full md:w-64" />
          <Skeleton className="h-10 w-full md:w-48" />
        </div>

        {/* Loading skeleton for grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="mt-2 h-4 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-5/6" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Tópicos</h1>
        <p className="text-muted-foreground mt-2">Explore questões por tópicos específicos</p>
      </div>

      {/* Overall Statistics */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Tópicos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{topics.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalQuestions.toLocaleString('pt-BR')}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Dificuldade Média</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgDifficulty.toFixed(1)}/5</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Taxa de Acerto Média</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgSuccessRate.toFixed(1)}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="mb-6 flex flex-col gap-4 md:flex-row">
        <div className="relative flex-1">
          <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
          <Input
            placeholder="Buscar tópico..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={selectedSubject} onValueChange={setSelectedSubject}>
          <SelectTrigger className="w-full md:w-48">
            <SelectValue placeholder="Filtrar por matéria" />
          </SelectTrigger>
          <SelectContent>
            {mockSubjects.map((subject) => (
              <SelectItem key={subject.id} value={subject.id}>
                {subject.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Topics Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredTopics.map((topic) => (
          <Card
            key={topic.id}
            className="cursor-pointer transition-shadow hover:shadow-lg"
            onClick={() => router.push(`/topics/${topic.id}`)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-base">{topic.name}</CardTitle>
                  <CardDescription className="mt-1">{topic.subjectName}</CardDescription>
                </div>
                <div className="flex items-center gap-1">
                  {topic.yearlyGrowth > 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-600 dark:text-green-400" />
                  ) : (
                    <TrendingUp className="h-4 w-4 rotate-180 text-red-600 dark:text-red-400" />
                  )}
                  <span
                    className={`text-xs font-medium ${
                      topic.yearlyGrowth > 0
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}
                  >
                    {Math.abs(topic.yearlyGrowth)}%
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Statistics */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <BookOpen className="h-3.5 w-3.5" />
                    <span className="text-xs">Questões</span>
                  </div>
                  <div className="font-medium">{topic.questionCount.toLocaleString('pt-BR')}</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <FileText className="h-3.5 w-3.5" />
                    <span className="text-xs">Provas</span>
                  </div>
                  <div className="font-medium">{topic.examCount}</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <BarChart className="h-3.5 w-3.5" />
                    <span className="text-xs">Dificuldade</span>
                  </div>
                  <div className="font-medium">{topic.averageDifficulty.toFixed(1)}/5</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <TrendingUp className="h-3.5 w-3.5" />
                    <span className="text-xs">Taxa de Acerto</span>
                  </div>
                  <div className="font-medium">{topic.successRate}%</div>
                </div>
              </div>

              {/* Progress Bar - Success Rate Visual */}
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-muted-foreground text-xs">Taxa de Acerto</span>
                  <span className="text-xs font-medium">{topic.successRate}%</span>
                </div>
                <div className="bg-muted h-2 overflow-hidden rounded-full">
                  <div
                    className={`h-full transition-all duration-500 ${
                      topic.successRate >= 50
                        ? 'bg-green-500 dark:bg-green-600'
                        : topic.successRate >= 30
                          ? 'bg-yellow-500 dark:bg-yellow-600'
                          : 'bg-red-500 dark:bg-red-600'
                    }`}
                    style={{ width: `${topic.successRate}%` }}
                  />
                </div>
              </div>

              {/* Top Exam Boards */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Principais Bancas</p>
                <div className="flex flex-wrap gap-1">
                  {topic.popularExamBoards.slice(0, 3).map((board) => (
                    <Badge key={board.name} variant="outline" className="text-xs">
                      {board.name} ({board.count})
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Related Topics */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Tópicos Relacionados</p>
                <div className="flex flex-wrap gap-1">
                  {topic.relatedTopics.slice(0, 2).map((related) => (
                    <Badge key={related} variant="secondary" className="text-xs">
                      {related}
                    </Badge>
                  ))}
                  {topic.relatedTopics.length > 2 && (
                    <Badge variant="secondary" className="text-xs">
                      +{topic.relatedTopics.length - 2}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Last Updated */}
              <div className="text-muted-foreground text-xs">
                Atualizado em {new Date(topic.lastUpdated).toLocaleDateString('pt-BR')}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTopics.length === 0 && (
        <div className="py-12 text-center">
          <p className="text-muted-foreground">Nenhum tópico encontrado</p>
        </div>
      )}
    </div>
  );
}
