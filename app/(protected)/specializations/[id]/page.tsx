'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Briefcase,
  BookOpen,
  TrendingUp,
  Building2,
  Target,
  ArrowLeft,
  ChartLine,
  AlertCircle,
} from 'lucide-react';
import { QuestionCard } from '@/components/question-card';
import { Question } from '@/types/question';
import { QuestionCardSkeleton } from '@/components/question-card-skeleton';

interface SpecializationDetail {
  id: string;
  name: string;
  code?: string;
  position: {
    id: string;
    name: string;
  };
  totalQuestions: number;
  totalPositions: number;
  totalExams: number;
  growthRate: number;
  relatedPositions: {
    id: string;
    name: string;
    institution: string;
    lastExam: string;
    questionCount: number;
  }[];
  topSubjects: {
    id: string;
    name: string;
    questionCount: number;
    percentage: number;
  }[];
  topInstitutions: {
    id: string;
    name: string;
    code?: string;
    acronym?: string;
    examCount: number;
    lastExam: string;
  }[];
  yearlyGrowth: {
    year: number;
    questionCount: number;
    positionCount: number;
  }[];
}

export default function SpecializationDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [specialization, setSpecialization] = useState<SpecializationDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(false);

  useEffect(() => {
    const fetchSpecialization = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`/api/specializations/${params.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Especialização não encontrada');
          } else {
            throw new Error('Failed to fetch specialization');
          }
          return;
        }

        const data = await response.json();
        setSpecialization(data);
      } catch (error) {
        console.error('Error fetching specialization:', error);
        setError('Erro ao carregar dados da especialização');
      } finally {
        setLoading(false);
      }
    };

    fetchSpecialization();
  }, [params.id]);

  const fetchQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const response = await fetch(`/api/questions?specialization=${specialization?.name}&limit=5`);

      if (!response.ok) {
        throw new Error('Failed to fetch questions');
      }

      const data = await response.json();
      setQuestions(data.questions || []);
    } catch (error) {
      console.error('Error fetching questions:', error);
    } finally {
      setLoadingQuestions(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Skeleton className="mb-2 h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error || !specialization) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Alert className="mx-auto mb-4 max-w-md">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error || 'Especialização não encontrada'}</AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/specializations')}>
            Voltar para Especializações
          </Button>
        </div>
      </div>
    );
  }

  const maxYearlyQuestions = Math.max(...specialization.yearlyGrowth.map((y) => y.questionCount));

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/specializations')}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar para Especializações
        </Button>

        <div className="flex items-start justify-between">
          <div>
            <div className="mb-2 flex items-center gap-3">
              <h1 className="text-3xl font-bold">
                {specialization.code ? `${specialization.code} - ` : ''}
                {specialization.name}
              </h1>
            </div>
            <p className="text-muted-foreground max-w-3xl">
              Especialização do cargo de {specialization.position.name} com{' '}
              {specialization.totalExams} concursos realizados e mais de{' '}
              {specialization.totalQuestions.toLocaleString('pt-BR')} questões em nosso banco de
              dados.
            </p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <BookOpen className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">
                {specialization.totalQuestions.toLocaleString('pt-BR')}
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Concursos Realizados</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Briefcase className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{specialization.totalExams}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Cargos Relacionados</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Target className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{specialization.totalPositions}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Taxa de Crescimento</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <TrendingUp className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">
                {specialization.growthRate > 0 ? '+' : ''}
                {specialization.growthRate}%
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="positions">Cargos</TabsTrigger>
          <TabsTrigger value="subjects">Matérias</TabsTrigger>
          <TabsTrigger value="growth">Crescimento</TabsTrigger>
          <TabsTrigger value="questions">Questões</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Parent Position */}
          <Card>
            <CardHeader>
              <CardTitle>Cargo Principal</CardTitle>
              <CardDescription>Cargo ao qual esta especialização pertence</CardDescription>
            </CardHeader>
            <CardContent>
              <div
                className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-4"
                onClick={() => router.push(`/positions/${specialization.position.id}`)}
              >
                <div className="flex items-center gap-3">
                  <Briefcase className="text-muted-foreground h-5 w-5" />
                  <p className="text-lg font-medium">{specialization.position.name}</p>
                </div>
                <Button variant="ghost" size="sm">
                  Ver Cargo →
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Top Institutions */}
          <Card>
            <CardHeader>
              <CardTitle>Principais Instituições</CardTitle>
              <CardDescription>Órgãos que mais contratam para esta especialização</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {specialization.topInstitutions.map((institution) => (
                  <div
                    key={institution.id}
                    className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-3"
                    onClick={() => router.push(`/institutions/${institution.id}`)}
                  >
                    <div className="flex items-center gap-3">
                      <Building2 className="text-muted-foreground h-5 w-5" />
                      <div>
                        <p className="font-medium">
                          {institution.acronym ? `${institution.acronym} - ` : ''}
                          {institution.name}
                        </p>
                        <p className="text-muted-foreground text-sm">
                          {institution.examCount} concursos realizados
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline">Último: {institution.lastExam}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="positions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cargos Relacionados</CardTitle>
              <CardDescription>Cargos que oferecem esta especialização</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {specialization.relatedPositions.map((position) => (
                  <div
                    key={position.id}
                    className="hover:bg-accent cursor-pointer rounded-lg border p-4"
                    onClick={() => router.push(`/positions/${position.id}`)}
                  >
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="text-lg font-medium">{position.name}</p>
                        <p className="text-muted-foreground mt-1 text-sm">{position.institution}</p>
                      </div>
                      <div className="text-right">
                        <Badge variant="secondary">Último: {position.lastExam}</Badge>
                        <p className="text-muted-foreground mt-1 text-sm">
                          {position.questionCount.toLocaleString('pt-BR')} questões
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Matérias Mais Cobradas</CardTitle>
              <CardDescription>Distribuição de questões por matéria</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {specialization.topSubjects.map((subject) => (
                  <div
                    key={subject.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/subjects/${subject.id}`)}
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">{subject.name}</span>
                      <span className="text-muted-foreground text-sm">
                        {subject.questionCount.toLocaleString('pt-BR')} ({subject.percentage}%)
                      </span>
                    </div>
                    <div className="bg-muted h-2 overflow-hidden rounded-full">
                      <div
                        className="bg-primary/60 h-full"
                        style={{ width: `${subject.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="growth" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Evolução Anual</CardTitle>
              <CardDescription>Crescimento da especialização ao longo dos anos</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex h-48 items-end gap-2">
                  {specialization.yearlyGrowth.map((year) => {
                    const height =
                      maxYearlyQuestions > 0 ? (year.questionCount / maxYearlyQuestions) * 100 : 0;
                    return (
                      <div key={year.year} className="flex flex-1 flex-col items-center gap-2">
                        <div className="text-xs font-medium">
                          {year.questionCount.toLocaleString('pt-BR')}
                        </div>
                        <div
                          className="bg-primary/20 hover:bg-primary/30 group relative w-full rounded-t transition-colors"
                          style={{ height: `${height}%` }}
                        >
                          <div className="bg-popover text-popover-foreground absolute -top-8 left-1/2 -translate-x-1/2 rounded px-2 py-1 text-xs whitespace-nowrap opacity-0 transition-opacity group-hover:opacity-100">
                            {year.positionCount} cargos
                          </div>
                        </div>
                        <span
                          className="text-muted-foreground cursor-pointer text-xs hover:underline"
                          onClick={() => router.push(`/years/${year.year}`)}
                        >
                          {year.year}
                        </span>
                      </div>
                    );
                  })}
                </div>

                {specialization.growthRate !== 0 && (
                  <div className="flex items-center justify-center gap-2 border-t pt-4">
                    <ChartLine className="text-muted-foreground h-4 w-4" />
                    <span className="text-muted-foreground text-sm">
                      Taxa de crescimento anual:
                      <span
                        className={`ml-1 font-medium ${specialization.growthRate > 0 ? 'text-green-600' : 'text-red-600'}`}
                      >
                        {specialization.growthRate > 0 ? '+' : ''}
                        {specialization.growthRate}%
                      </span>
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          {questions.length === 0 && !loadingQuestions ? (
            <Card>
              <CardContent className="py-12 text-center">
                <BookOpen className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">Questões de {specialization.name}</h3>
                <p className="text-muted-foreground mb-4">
                  Veja exemplos de questões desta especialização
                </p>
                <Button onClick={fetchQuestions}>Ver Questões de Exemplo</Button>
              </CardContent>
            </Card>
          ) : loadingQuestions ? (
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <QuestionCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="space-y-6">
              {questions.map((question) => (
                <QuestionCard
                  key={question.id}
                  questionNumber={question.number}
                  examBoard={question.exam.examBoard || undefined}
                  examBoardId={question.exam.examBoardId}
                  institution={question.institution?.name || question.exam.name}
                  institutionId={question.institution?.id}
                  year={question.exam.year || undefined}
                  position={question.position?.name}
                  positionId={question.position?.id}
                  specialization={question.specialization?.name}
                  specializationId={question.specialization?.id}
                  subject={question.subject?.name || ''}
                  subjectId={question.subject?.id}
                  topic={question.topic?.name || ''}
                  topicId={question.topic?.id}
                  questionText={question.stem}
                  associatedText={question.associatedText}
                  options={question.options.map((opt) => ({
                    letter: opt.optionLetter,
                    text: opt.text,
                  }))}
                  correctAnswer={question.options.find((opt) => opt.isCorrect)?.optionLetter}
                  hideNavigation
                  totalQuestions={questions.length}
                  commentCount={question.commentCount}
                />
              ))}
              {questions.length > 0 && (
                <div className="pt-4 text-center">
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/questions?specialization=${specialization.name}`)}
                  >
                    Ver Mais Questões
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
