'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { BookOpen, Users, TrendingUp, Search, Briefcase } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface Specialization {
  id: string;
  name: string;
  area:
    | 'direito'
    | 'administracao'
    | 'contabilidade'
    | 'ti'
    | 'saude'
    | 'educacao'
    | 'engenharia'
    | 'geral';
  questionCount: number;
  examCount: number;
  positionCount: number;
  requiredKnowledge: string[];
  relatedPositions: {
    name: string;
    count: number;
  }[];
  difficultyAverage: number;
  growthTrend: 'increasing' | 'stable' | 'decreasing';
}

// Mock data - replace with actual API call
const mockSpecializations: Specialization[] = [
  {
    id: 'controle-externo',
    name: 'Controle Externo',
    area: 'administracao',
    questionCount: 3280,
    examCount: 28,
    positionCount: 15,
    requiredKnowledge: [
      'Auditoria Governamental',
      'Contabilidade Pública',
      'Direito Administrativo',
      'Orçamento Público',
    ],
    relatedPositions: [
      { name: 'Auditor Federal', count: 850 },
      { name: 'Analista de Controle', count: 620 },
      { name: 'Técnico de Controle', count: 480 },
    ],
    difficultyAverage: 4.2,
    growthTrend: 'increasing',
  },
  {
    id: 'area-juridica',
    name: 'Área Jurídica',
    area: 'direito',
    questionCount: 8560,
    examCount: 65,
    positionCount: 32,
    requiredKnowledge: [
      'Direito Constitucional',
      'Direito Administrativo',
      'Direito Processual',
      'Direito Penal',
    ],
    relatedPositions: [
      { name: 'Analista Judiciário', count: 2350 },
      { name: 'Técnico Judiciário', count: 1820 },
      { name: 'Procurador', count: 980 },
    ],
    difficultyAverage: 4.4,
    growthTrend: 'stable',
  },
  {
    id: 'tecnologia-informacao',
    name: 'Tecnologia da Informação',
    area: 'ti',
    questionCount: 4230,
    examCount: 42,
    positionCount: 25,
    requiredKnowledge: ['Programação', 'Banco de Dados', 'Redes', 'Segurança', 'Governança de TI'],
    relatedPositions: [
      { name: 'Analista de Sistemas', count: 1250 },
      { name: 'Analista de TI', count: 980 },
      { name: 'Técnico de TI', count: 720 },
    ],
    difficultyAverage: 3.8,
    growthTrend: 'increasing',
  },
  {
    id: 'area-fiscal',
    name: 'Área Fiscal',
    area: 'contabilidade',
    questionCount: 5780,
    examCount: 38,
    positionCount: 18,
    requiredKnowledge: [
      'Direito Tributário',
      'Contabilidade',
      'Legislação Tributária',
      'Matemática Financeira',
    ],
    relatedPositions: [
      { name: 'Auditor Fiscal', count: 1680 },
      { name: 'Fiscal de Tributos', count: 1230 },
      { name: 'Analista Tributário', count: 890 },
    ],
    difficultyAverage: 4.5,
    growthTrend: 'stable',
  },
  {
    id: 'educacao',
    name: 'Educação',
    area: 'educacao',
    questionCount: 6890,
    examCount: 82,
    positionCount: 28,
    requiredKnowledge: [
      'Pedagogia',
      'Legislação Educacional',
      'Didática',
      'Psicologia da Educação',
    ],
    relatedPositions: [
      { name: 'Professor', count: 3450 },
      { name: 'Pedagogo', count: 1280 },
      { name: 'Coordenador Pedagógico', count: 780 },
    ],
    difficultyAverage: 3.2,
    growthTrend: 'increasing',
  },
  {
    id: 'area-policial',
    name: 'Área Policial',
    area: 'geral',
    questionCount: 3450,
    examCount: 22,
    positionCount: 12,
    requiredKnowledge: [
      'Direito Penal',
      'Direito Processual Penal',
      'Criminologia',
      'Legislação Especial',
    ],
    relatedPositions: [
      { name: 'Agente de Polícia', count: 1280 },
      { name: 'Escrivão', count: 780 },
      { name: 'Investigador', count: 650 },
    ],
    difficultyAverage: 4.1,
    growthTrend: 'stable',
  },
];

const areaLabels = {
  direito: 'Direito',
  administracao: 'Administração',
  contabilidade: 'Contabilidade',
  ti: 'Tecnologia',
  saude: 'Saúde',
  educacao: 'Educação',
  engenharia: 'Engenharia',
  geral: 'Geral',
};

const areaColors = {
  direito: 'bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400',
  administracao: 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400',
  contabilidade: 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400',
  ti: 'bg-cyan-100 text-cyan-700 dark:bg-cyan-900/20 dark:text-cyan-400',
  saude: 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400',
  educacao: 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400',
  engenharia: 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400',
  geral: 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400',
};

const trendIcons = {
  increasing: '↗️',
  stable: '→',
  decreasing: '↘️',
};

const trendLabels = {
  increasing: 'Em crescimento',
  stable: 'Estável',
  decreasing: 'Em declínio',
};

export default function SpecializationsPage() {
  const router = useRouter();
  const [specializations, setSpecializations] = useState<Specialization[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setSpecializations(mockSpecializations);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredSpecializations = specializations.filter((spec) =>
    spec.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalQuestions = specializations.reduce((sum, spec) => sum + spec.questionCount, 0);
  const totalExams = specializations.reduce((sum, spec) => sum + spec.examCount, 0);
  const totalPositions = specializations.reduce((sum, spec) => sum + spec.positionCount, 0);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Especializações</h1>
          <p className="text-muted-foreground mt-2">
            Explore questões e estatísticas por área de especialização
          </p>
        </div>

        {/* Loading skeleton for stats */}
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Loading skeleton for grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="mt-2 h-4 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-5/6" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Especializações</h1>
        <p className="text-muted-foreground mt-2">
          Explore questões e estatísticas por área de especialização
        </p>
      </div>

      {/* Overall Statistics */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Especializações</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{specializations.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalQuestions.toLocaleString('pt-BR')}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Concursos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalExams}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Cargos Relacionados</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPositions}</div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
          <Input
            placeholder="Buscar especialização..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Specializations Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredSpecializations.map((spec) => (
          <Card
            key={spec.id}
            className="cursor-pointer transition-shadow hover:shadow-lg"
            onClick={() => router.push(`/specializations/${spec.id}`)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-base">{spec.name}</CardTitle>
                  <div className="mt-2 flex items-center gap-2">
                    <Badge className={cn(areaColors[spec.area])}>{areaLabels[spec.area]}</Badge>
                    <div className="flex items-center gap-1 text-sm">
                      <span className="text-muted-foreground">{trendIcons[spec.growthTrend]}</span>
                      <span className="text-muted-foreground text-xs">
                        {trendLabels[spec.growthTrend]}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Statistics */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <BookOpen className="h-3.5 w-3.5" />
                    <span className="text-xs">Questões</span>
                  </div>
                  <div className="font-medium">{spec.questionCount.toLocaleString('pt-BR')}</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <Briefcase className="h-3.5 w-3.5" />
                    <span className="text-xs">Cargos</span>
                  </div>
                  <div className="font-medium">{spec.positionCount}</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <Users className="h-3.5 w-3.5" />
                    <span className="text-xs">Concursos</span>
                  </div>
                  <div className="font-medium">{spec.examCount}</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <TrendingUp className="h-3.5 w-3.5" />
                    <span className="text-xs">Dificuldade</span>
                  </div>
                  <div className="font-medium">{spec.difficultyAverage.toFixed(1)}/5</div>
                </div>
              </div>

              {/* Related Positions */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Cargos Principais</p>
                <div className="space-y-1">
                  {spec.relatedPositions.slice(0, 3).map((position) => (
                    <div key={position.name} className="flex items-center justify-between">
                      <span className="text-xs">{position.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {position.count}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              {/* Required Knowledge */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Conhecimentos Exigidos</p>
                <div className="flex flex-wrap gap-1">
                  {spec.requiredKnowledge.slice(0, 3).map((knowledge) => (
                    <Badge key={knowledge} variant="secondary" className="text-xs">
                      {knowledge}
                    </Badge>
                  ))}
                  {spec.requiredKnowledge.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{spec.requiredKnowledge.length - 3}
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredSpecializations.length === 0 && (
        <div className="py-12 text-center">
          <p className="text-muted-foreground">Nenhuma especialização encontrada</p>
        </div>
      )}
    </div>
  );
}
