'use client';

import { useAuth } from '@/hooks/use-auth';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { GraduationCap, Activity } from 'lucide-react';

export default function DashboardPage() {
  const { user } = useAuth();
  const router = useRouter();

  return (
    <div className="space-y-6 p-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Bem-vindo de volta!</h2>
        <p className="text-muted-foreground">
          {user?.name ? `Olá ${user.name}` : `<PERSON>lá ${user?.email}`}, pronto para continuar
          aprendendo?
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card
          className="cursor-pointer transition-shadow hover:shadow-lg"
          onClick={() => router.push('/study')}
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sessão de Revisão</CardTitle>
            <GraduationCap className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Estudar</div>
            <p className="text-muted-foreground text-xs">
              Revisar cartões programados para repetição espaçada
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Seu Progresso</CardTitle>
            <Activity className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0 cartões</div>
            <p className="text-muted-foreground text-xs">Revisados hoje</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Estatísticas Rápidas</CardTitle>
          <CardDescription>Visão geral do seu aprendizado</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground text-sm">Total de cartões estudados</span>
              <span className="text-sm font-medium">0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground text-sm">Sequência atual</span>
              <span className="text-sm font-medium">0 dias</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground text-sm">Retenção média</span>
              <span className="text-sm font-medium">-</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
