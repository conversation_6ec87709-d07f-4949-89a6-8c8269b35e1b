'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { BookOpen, Calendar, Users, Search, MapPin } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface Institution {
  id: string;
  name: string;
  acronym?: string;
  type: 'federal' | 'estadual' | 'municipal' | 'autarquia' | 'empresa-publica';
  location: {
    state: string;
    region: string;
  };
  questionCount: number;
  examCount: number;
  yearRange: {
    min: number;
    max: number;
  };
  popularExamBoards: string[];
  popularPositions: string[];
}

// Mock data - replace with actual API call
const mockInstitutions: Institution[] = [
  {
    id: 'tcu',
    name: 'Tribunal de Contas da União',
    acronym: 'TCU',
    type: 'federal',
    location: { state: 'DF', region: 'Centro-Oeste' },
    questionCount: 3240,
    examCount: 15,
    yearRange: { min: 2010, max: 2024 },
    popularExamBoards: ['CESPE', 'FGV'],
    popularPositions: ['Auditor Federal', 'Técnico Federal'],
  },
  {
    id: 'receita-federal',
    name: 'Receita Federal do Brasil',
    acronym: 'RFB',
    type: 'federal',
    location: { state: 'DF', region: 'Centro-Oeste' },
    questionCount: 4850,
    examCount: 12,
    yearRange: { min: 2009, max: 2023 },
    popularExamBoards: ['ESAF', 'FGV'],
    popularPositions: ['Auditor Fiscal', 'Analista Tributário'],
  },
  {
    id: 'inss',
    name: 'Instituto Nacional do Seguro Social',
    acronym: 'INSS',
    type: 'autarquia',
    location: { state: 'DF', region: 'Centro-Oeste' },
    questionCount: 2890,
    examCount: 8,
    yearRange: { min: 2011, max: 2024 },
    popularExamBoards: ['CESPE', 'FCC'],
    popularPositions: ['Técnico do Seguro Social', 'Analista do Seguro Social'],
  },
  {
    id: 'banco-brasil',
    name: 'Banco do Brasil',
    acronym: 'BB',
    type: 'empresa-publica',
    location: { state: 'DF', region: 'Centro-Oeste' },
    questionCount: 3650,
    examCount: 10,
    yearRange: { min: 2010, max: 2023 },
    popularExamBoards: ['FCC', 'CESGRANRIO'],
    popularPositions: ['Escriturário', 'Analista Bancário'],
  },
  {
    id: 'trf2',
    name: 'Tribunal Regional Federal da 2ª Região',
    acronym: 'TRF2',
    type: 'federal',
    location: { state: 'RJ', region: 'Sudeste' },
    questionCount: 1580,
    examCount: 6,
    yearRange: { min: 2012, max: 2024 },
    popularExamBoards: ['FCC', 'CONSULPLAN'],
    popularPositions: ['Técnico Judiciário', 'Analista Judiciário'],
  },
  {
    id: 'prefeitura-sp',
    name: 'Prefeitura de São Paulo',
    type: 'municipal',
    location: { state: 'SP', region: 'Sudeste' },
    questionCount: 2320,
    examCount: 18,
    yearRange: { min: 2011, max: 2024 },
    popularExamBoards: ['VUNESP', 'FCC'],
    popularPositions: ['Professor', 'Assistente Administrativo'],
  },
];

const typeLabels = {
  federal: 'Federal',
  estadual: 'Estadual',
  municipal: 'Municipal',
  autarquia: 'Autarquia',
  'empresa-publica': 'Empresa Pública',
};

const typeColors = {
  federal: 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400',
  estadual: 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400',
  municipal: 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400',
  autarquia: 'bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400',
  'empresa-publica': 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400',
};

export default function InstitutionsPage() {
  const router = useRouter();
  const [institutions, setInstitutions] = useState<Institution[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setInstitutions(mockInstitutions);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredInstitutions = institutions.filter(
    (inst) =>
      inst.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      inst.acronym?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalQuestions = institutions.reduce((sum, inst) => sum + inst.questionCount, 0);
  const totalExams = institutions.reduce((sum, inst) => sum + inst.examCount, 0);
  const typeDistribution = institutions.reduce(
    (acc, inst) => {
      acc[inst.type] = (acc[inst.type] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Instituições</h1>
          <p className="text-muted-foreground mt-2">Explore concursos e questões por instituição</p>
        </div>

        {/* Loading skeleton for stats */}
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Loading skeleton for grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="mt-2 h-4 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-5/6" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Instituições</h1>
        <p className="text-muted-foreground mt-2">Explore concursos e questões por instituição</p>
      </div>

      {/* Overall Statistics */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Instituições</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{institutions.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalQuestions.toLocaleString('pt-BR')}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Concursos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalExams}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Tipos de Instituição</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-1">
              {Object.entries(typeDistribution).map(([type, count]) => (
                <Badge key={type} variant="secondary" className="text-xs">
                  {typeLabels[type as keyof typeof typeLabels]}: {count}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
          <Input
            placeholder="Buscar instituição..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Institutions Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredInstitutions.map((inst) => (
          <Card
            key={inst.id}
            className="cursor-pointer transition-shadow hover:shadow-lg"
            onClick={() => router.push(`/institutions/${inst.id}`)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-base">
                    {inst.acronym ? (
                      <>
                        {inst.acronym} - {inst.name}
                      </>
                    ) : (
                      inst.name
                    )}
                  </CardTitle>
                  <div className="mt-2 flex items-center gap-2 text-sm">
                    <MapPin className="text-muted-foreground h-3 w-3" />
                    <span className="text-muted-foreground">
                      {inst.location.state} - {inst.location.region}
                    </span>
                  </div>
                </div>
                <Badge className={cn('ml-2', typeColors[inst.type])}>{typeLabels[inst.type]}</Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Statistics */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <BookOpen className="text-muted-foreground h-4 w-4" />
                  <span className="text-muted-foreground">Questões</span>
                </div>
                <div className="font-medium">{inst.questionCount.toLocaleString('pt-BR')}</div>

                <div className="flex items-center gap-2">
                  <Users className="text-muted-foreground h-4 w-4" />
                  <span className="text-muted-foreground">Concursos</span>
                </div>
                <div className="font-medium">{inst.examCount}</div>

                <div className="flex items-center gap-2">
                  <Calendar className="text-muted-foreground h-4 w-4" />
                  <span className="text-muted-foreground">Período</span>
                </div>
                <div className="font-medium">
                  {inst.yearRange.min} - {inst.yearRange.max}
                </div>
              </div>

              {/* Popular Exam Boards */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Bancas Frequentes</p>
                <div className="flex flex-wrap gap-1">
                  {inst.popularExamBoards.map((board) => (
                    <Badge key={board} variant="outline" className="text-xs">
                      {board}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Popular Positions */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Cargos Populares</p>
                <div className="flex flex-wrap gap-1">
                  {inst.popularPositions.slice(0, 2).map((position) => (
                    <Badge key={position} variant="secondary" className="text-xs">
                      {position}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredInstitutions.length === 0 && (
        <div className="py-12 text-center">
          <p className="text-muted-foreground">Nenhuma instituição encontrada</p>
        </div>
      )}
    </div>
  );
}
