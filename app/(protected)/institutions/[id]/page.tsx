'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Tabs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Building2,
  BookOpen,
  Calendar,
  GraduationCap,
  Briefcase,
  ArrowLeft,
  AlertCircle,
} from 'lucide-react';
import { QuestionCard } from '@/components/question-card';
import { Question } from '@/types/question';

interface InstitutionDetail {
  id: string;
  name: string;
  code?: string;
  fullName: string;
  totalQuestions: number;
  totalExams: number;
  totalPositions: number;
  totalExamBoards: number;
  examBoards: {
    id: string;
    name: string;
    fullName: string;
    examCount: number;
    questionCount: number;
    lastExam: string;
  }[];
  positions: {
    id: string;
    name: string;
    code?: string;
    examCount: number;
    questionCount: number;
    lastExam: string;
  }[];
  specializations: {
    id: string;
    name: string;
    code?: string;
    positionId: string;
    positionName: string;
    questionCount: number;
  }[];
  topSubjects: {
    id: string;
    name: string;
    questionCount: number;
    percentage: number;
  }[];
  yearlyStatistics: {
    year: number;
    examCount: number;
    questionCount: number;
    positionCount: number;
  }[];
  recentExams: {
    id: string;
    year: number;
    position: string;
    positionId: string;
    specialization?: string;
    specializationId?: string;
    examBoard: string;
    examBoardId: string;
    questionCount: number;
    testCount: number;
  }[];
}

export default function InstitutionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [institution, setInstitution] = useState<InstitutionDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(false);

  useEffect(() => {
    const fetchInstitution = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`/api/institutions/${params.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Instituição não encontrada');
          } else {
            throw new Error('Failed to fetch institution');
          }
          return;
        }

        const data = await response.json();
        setInstitution(data);
      } catch (error) {
        console.error('Error fetching institution:', error);
        setError('Erro ao carregar dados da instituição');
      } finally {
        setLoading(false);
      }
    };

    fetchInstitution();
  }, [params.id]);

  const fetchQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const response = await fetch(`/api/questions?institution=${institution?.name}&limit=5`);

      if (!response.ok) {
        throw new Error('Failed to fetch questions');
      }

      const data = await response.json();
      setQuestions(data.questions || []);
    } catch (error) {
      console.error('Error fetching questions:', error);
    } finally {
      setLoadingQuestions(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Skeleton className="mb-2 h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error || !institution) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Alert className="mx-auto mb-4 max-w-md">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error || 'Instituição não encontrada'}</AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/institutions')}>Voltar para Instituições</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/institutions')}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar para Instituições
        </Button>

        <div className="flex items-start justify-between">
          <div>
            <div className="mb-2 flex items-center gap-3">
              <h1 className="text-3xl font-bold">
                {institution.code ? `${institution.code} - ` : ''}
                {institution.fullName}
              </h1>
            </div>
            <p className="text-muted-foreground max-w-3xl">
              Instituição com {institution.totalExams} concursos realizados e mais de{' '}
              {institution.totalQuestions.toLocaleString('pt-BR')} questões em nosso banco de dados.
            </p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <BookOpen className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">
                {institution.totalQuestions.toLocaleString('pt-BR')}
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Concursos Realizados</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <GraduationCap className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{institution.totalExams}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Cargos Oferecidos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Briefcase className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{institution.totalPositions}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Bancas Utilizadas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Building2 className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{institution.totalExamBoards}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="positions">Cargos</TabsTrigger>
          <TabsTrigger value="history">Histórico</TabsTrigger>
          <TabsTrigger value="subjects">Matérias</TabsTrigger>
          <TabsTrigger value="questions">Questões</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Exam Boards */}
          <Card>
            <CardHeader>
              <CardTitle>Bancas Examinadoras</CardTitle>
              <CardDescription>
                Bancas que realizaram concursos para esta instituição
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {institution.examBoards.map((board) => (
                  <div
                    key={board.id}
                    className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-3"
                    onClick={() => router.push(`/exam-boards/${board.id}`)}
                  >
                    <div>
                      <p className="font-medium">{board.name}</p>
                      <p className="text-muted-foreground text-sm">
                        {board.examCount} {board.examCount === 1 ? 'concurso' : 'concursos'} •{' '}
                        {board.questionCount} questões
                      </p>
                    </div>
                    <Badge variant="outline">Último: {board.lastExam}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Yearly Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Evolução Anual</CardTitle>
              <CardDescription>Concursos e questões ao longo dos anos</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {institution.yearlyStatistics.map((stat) => (
                  <div key={stat.year} className="flex items-center justify-between">
                    <div
                      className="flex cursor-pointer items-center gap-3"
                      onClick={() => router.push(`/years/${stat.year}`)}
                    >
                      <Calendar className="text-muted-foreground h-4 w-4" />
                      <span className="font-medium hover:underline">{stat.year}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <Badge variant="secondary">
                        {stat.questionCount.toLocaleString('pt-BR')} questões
                      </Badge>
                      <Badge variant="outline">{stat.examCount} concursos</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="positions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cargos Disponíveis</CardTitle>
              <CardDescription>Cargos oferecidos em concursos recentes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {institution.positions.map((position) => (
                  <div
                    key={position.id}
                    className="hover:bg-accent cursor-pointer rounded-lg border p-4"
                    onClick={() => router.push(`/positions/${position.id}`)}
                  >
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="text-lg font-medium">{position.name}</p>
                        <div className="text-muted-foreground mt-2 flex items-center gap-4 text-sm">
                          <span>{position.examCount} concursos</span>
                          <span>•</span>
                          <span>Último: {position.lastExam}</span>
                          <span>•</span>
                          <span>{position.questionCount.toLocaleString('pt-BR')} questões</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {institution.specializations.length > 0 && (
                  <>
                    <h3 className="mt-6 mb-3 text-lg font-semibold">Especializações</h3>
                    {institution.specializations.map((spec) => (
                      <div
                        key={spec.id}
                        className="hover:bg-accent cursor-pointer rounded-lg border p-3"
                        onClick={() => router.push(`/specializations/${spec.id}`)}
                      >
                        <p className="font-medium">{spec.name}</p>
                        <p className="text-muted-foreground text-sm">
                          {spec.positionName} • {spec.questionCount.toLocaleString('pt-BR')}{' '}
                          questões
                        </p>
                      </div>
                    ))}
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Histórico de Concursos</CardTitle>
              <CardDescription>Concursos realizados nos últimos anos</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {institution.recentExams.map((exam) => (
                  <div key={exam.id} className="rounded-lg border p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center gap-3">
                          <Calendar className="text-muted-foreground h-4 w-4" />
                          <span className="font-medium">{exam.year}</span>
                          <Badge
                            variant="outline"
                            className="hover:bg-accent cursor-pointer"
                            onClick={() => router.push(`/exam-boards/${exam.examBoardId}`)}
                          >
                            {exam.examBoard}
                          </Badge>
                        </div>
                        <p
                          className="mt-1 cursor-pointer text-sm font-medium hover:underline"
                          onClick={() => router.push(`/positions/${exam.positionId}`)}
                        >
                          {exam.position}
                        </p>
                        {exam.specialization && (
                          <p
                            className="text-muted-foreground cursor-pointer text-sm hover:underline"
                            onClick={() =>
                              exam.specializationId &&
                              router.push(`/specializations/${exam.specializationId}`)
                            }
                          >
                            {exam.specialization}
                          </p>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {exam.questionCount.toLocaleString('pt-BR')} questões
                        </p>
                        <p className="text-muted-foreground text-xs">{exam.testCount} provas</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Matérias Mais Cobradas</CardTitle>
              <CardDescription>Distribuição de questões por matéria</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {institution.topSubjects.map((subject) => (
                  <div
                    key={subject.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/subjects/${subject.id}`)}
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">{subject.name}</span>
                      <span className="text-muted-foreground text-sm">
                        {subject.questionCount.toLocaleString('pt-BR')} ({subject.percentage}%)
                      </span>
                    </div>
                    <div className="bg-muted h-2 overflow-hidden rounded-full">
                      <div
                        className="bg-primary/60 h-full"
                        style={{ width: `${subject.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          {questions.length === 0 && !loadingQuestions ? (
            <Card>
              <CardContent className="py-12 text-center">
                <BookOpen className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">
                  Explore Questões do {institution.code || institution.name}
                </h3>
                <p className="text-muted-foreground mb-4">
                  Veja exemplos de questões de concursos anteriores
                </p>
                <Button onClick={fetchQuestions}>Ver Questões de Exemplo</Button>
              </CardContent>
            </Card>
          ) : loadingQuestions ? (
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <Skeleton className="mb-4 h-4 w-3/4" />
                    <Skeleton className="mb-4 h-20 w-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-10 w-full" />
                      <Skeleton className="h-10 w-full" />
                      <Skeleton className="h-10 w-full" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-6">
              {questions.map((question) => (
                <QuestionCard
                  key={question.id}
                  questionNumber={question.number}
                  examBoard={question.exam.examBoard || undefined}
                  examBoardId={question.exam.examBoardId}
                  institution={question.institution?.name || question.exam.name}
                  institutionId={question.institution?.id}
                  year={question.exam.year || undefined}
                  position={question.position?.name}
                  positionId={question.position?.id}
                  specialization={question.specialization?.name}
                  specializationId={question.specialization?.id}
                  subject={question.subject?.name || ''}
                  subjectId={question.subject?.id}
                  topic={question.topic?.name || ''}
                  topicId={question.topic?.id}
                  questionText={question.stem}
                  associatedText={question.associatedText}
                  options={question.options.map((opt) => ({
                    letter: opt.optionLetter,
                    text: opt.text,
                  }))}
                  correctAnswer={question.options.find((opt) => opt.isCorrect)?.optionLetter}
                  hideNavigation
                  totalQuestions={questions.length}
                  commentCount={question.commentCount}
                />
              ))}
              {questions.length > 0 && (
                <div className="pt-4 text-center">
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/questions?institution=${institution.name}`)}
                  >
                    Ver Mais Questões
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
