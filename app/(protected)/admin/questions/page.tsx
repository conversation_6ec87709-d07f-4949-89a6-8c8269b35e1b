'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Copy,
  Edit,
  Save,
  X,
  Loader2,
  Trash2,
  ArrowLeft,
  FileText,
  Eye,
  ToggleLeft,
  ToggleRight,
} from 'lucide-react';
import { toast } from 'sonner';
import { QuestionCard } from '@/components/question-card';

// Data structure based on the provided JSON
interface Question {
  id: string;
  number: number;
  subject: string;
  topic: string | null;
  associated_text: string | null;
  associated_text_references?: Array<{ snippet: string; location_pointer: string }> | null;
  stem: string;
  options: {
    order: number;
    text: string;
  }[];
  images: string[];
  correct_answer_order: number | null;
  is_null: boolean;
  change_reason: string;
  exam_info?: {
    exam_board: string;
    institution: string;
    year: number;
    position?: string;
    specialization?: string;
  };
}

interface QuestionsResponse {
  questions: Question[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export default function AdminQuestionsPage() {
  const searchParams = useSearchParams();
  const highlightId = searchParams.get('id');

  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingQuestion, setEditingQuestion] = useState<string | null>(null);
  const [editData, setEditData] = useState<Question | null>(null);
  const [saving, setSaving] = useState(false);
  const [, setShowJsonEditor] = useState(false);
  const [jsonInput, setJsonInput] = useState('');
  const [activeTab, setActiveTab] = useState('content');
  const [showCorrectAnswer, setShowCorrectAnswer] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });
  // Toast is imported from sonner

  // Load questions from API
  const loadQuestions = async (page: number = 1, search: string = '') => {
    try {
      setLoading(true);

      // If we have a specific question ID to highlight, load just that question
      if (highlightId && !search) {
        const response = await fetch(`/api/admin/questions/${highlightId}`);
        if (response.ok) {
          const question = await response.json();
          setQuestions([question]);
          setPagination({ page: 1, limit: 1, total: 1, totalPages: 1 });
          return;
        }
      }

      // Otherwise, load questions normally
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
      });

      if (search) {
        params.append('search', search);
      }

      const response = await fetch(`/api/admin/questions?${params}`);
      if (!response.ok) {
        throw new Error('Failed to load questions');
      }

      const data: QuestionsResponse = await response.json();
      setQuestions(data.questions);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error loading questions:', error);
      toast.error('Falha ao carregar questões');
    } finally {
      setLoading(false);
    }
  };

  // Load questions on component mount
  useEffect(() => {
    loadQuestions();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Auto-edit question if ID is provided in URL
  useEffect(() => {
    if (highlightId && questions.length > 0) {
      const question = questions.find((q) => q.id === highlightId);
      if (question) {
        setEditingQuestion(highlightId);
        setEditData({ ...question });
        // Scroll to the question
        setTimeout(() => {
          const element = document.getElementById(`question-${highlightId}`);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 100);
      }
    }
  }, [highlightId, questions]);

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      loadQuestions(1, searchTerm);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]); // eslint-disable-line react-hooks/exhaustive-deps

  // Function to exit single-question mode
  const exitSingleQuestionMode = () => {
    window.history.replaceState({}, '', '/admin/questions');
    setSearchTerm('');
    loadQuestions(1, '');
  };

  const filteredQuestions = questions; // Already filtered by API

  const copyQuestionJSON = (question: Question) => {
    const jsonData = {
      number: question.number,
      subject: question.subject,
      topic: question.topic,
      associated_text: question.associated_text,
      associated_text_references: question.associated_text_references,
      stem: question.stem,
      options: question.options,
      images: question.images,
      correct_answer_order: question.correct_answer_order,
      is_null: question.is_null,
      change_reason: question.change_reason,
    };

    navigator.clipboard.writeText(JSON.stringify(jsonData, null, 2));
    toast.success('JSON da questão foi copiado para a área de transferência');
  };

  const startEditing = (question: Question) => {
    setEditingQuestion(question.id);
    setEditData({ ...question });
  };

  const cancelEditing = () => {
    setEditingQuestion(null);
    setEditData(null);
    setShowJsonEditor(false);
    setJsonInput('');
    setActiveTab('content');
    setShowCorrectAnswer(false);
  };

  const saveQuestion = async () => {
    if (!editData) return;

    try {
      setSaving(true);
      const response = await fetch(`/api/admin/questions/${editData.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...editData,
          associated_text: editData.associated_text,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update question');
      }

      // Update local state
      setQuestions((prev) => prev.map((q) => (q.id === editData.id ? editData : q)));

      setEditingQuestion(null);
      setEditData(null);

      toast.success('Questão foi atualizada com sucesso');

      // If in single-question mode, go back to all questions after saving
      if (highlightId) {
        setTimeout(() => {
          exitSingleQuestionMode();
        }, 1000);
      }
    } catch (error) {
      console.error('Error updating question:', error);
      toast.error('Falha ao atualizar questão');
    } finally {
      setSaving(false);
    }
  };

  const deleteQuestion = async (questionId: string) => {
    if (!confirm('Tem certeza que deseja excluir esta questão? Esta ação não pode ser desfeita.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/questions/${questionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete question');
      }

      // Remove from local state
      setQuestions((prev) => prev.filter((q) => q.id !== questionId));

      toast.success('Questão foi excluída com sucesso');

      // If in single-question mode and we just deleted the question, go back to all questions
      if (highlightId === questionId) {
        setTimeout(() => {
          exitSingleQuestionMode();
        }, 1000);
      }
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error('Falha ao excluir questão');
    }
  };

  const updateEditData = (field: keyof Question, value: Question[keyof Question]) => {
    if (!editData) return;
    setEditData((prev) => (prev ? { ...prev, [field]: value } : null));
  };

  const openJsonEditor = () => {
    if (!editData) return;

    const jsonData = {
      number: editData.number,
      subject: editData.subject,
      topic: editData.topic,
      associated_text: editData.associated_text,
      associated_text_references: editData.associated_text_references,
      stem: editData.stem,
      options: editData.options,
      images: editData.images,
      correct_answer_order: editData.correct_answer_order,
      is_null: editData.is_null,
      change_reason: editData.change_reason,
    };

    setJsonInput(JSON.stringify(jsonData, null, 2));
    setShowJsonEditor(true);
    setActiveTab('json');
  };

  const applyJsonChanges = () => {
    try {
      const parsedData = JSON.parse(jsonInput);

      // Validate required fields
      if (!parsedData.stem || !parsedData.options || !Array.isArray(parsedData.options)) {
        toast.error('JSON inválido: Campos obrigatórios ausentes (enunciado, opções)');
        return;
      }

      // Validate options structure
      if (
        !parsedData.options.every((opt: { order?: number; text?: string }) => opt.order && opt.text)
      ) {
        toast.error('JSON inválido: Opções devem ter campos de ordem e texto');
        return;
      }

      // Validate correct_answer_order
      if (
        parsedData.correct_answer_order &&
        (parsedData.correct_answer_order < 1 ||
          parsedData.correct_answer_order > parsedData.options.length)
      ) {
        toast.error(
          `JSON inválido: correct_answer_order deve estar entre 1 e ${parsedData.options.length}`
        );
        return;
      }

      // Validate options order numbers
      const expectedOrders = Array.from({ length: parsedData.options.length }, (_, i) => i + 1);
      const actualOrders = parsedData.options
        .map((opt: { order: number }) => opt.order)
        .sort((a: number, b: number) => a - b);
      if (JSON.stringify(expectedOrders) !== JSON.stringify(actualOrders)) {
        toast.error('JSON inválido: Opções devem ter números de ordem consecutivos começando em 1');
        return;
      }

      // Update the edit data
      if (editData) {
        const updatedData: Question = {
          ...editData,
          number: parsedData.number || editData.number,
          subject: parsedData.subject || editData.subject,
          topic: parsedData.topic || editData.topic,
          associated_text: parsedData.associated_text || editData.associated_text,
          associated_text_references:
            parsedData.associated_text_references || editData.associated_text_references,
          stem: parsedData.stem,
          options: parsedData.options,
          images: parsedData.images || [],
          correct_answer_order: parsedData.correct_answer_order || null,
          is_null: parsedData.is_null || false,
          change_reason: parsedData.change_reason || '',
        };

        setEditData(updatedData);
        setShowJsonEditor(false);
        setJsonInput('');
        setActiveTab('content');
        toast.success('JSON aplicado com sucesso');
      }
    } catch (error) {
      console.error('JSON parsing error:', error);
      toast.error('Formato JSON inválido');
    }
  };

  const updateOption = (index: number, text: string) => {
    if (!editData) return;
    const newOptions = [...editData.options];
    newOptions[index] = { ...newOptions[index], text };
    updateEditData('options', newOptions);
  };

  const addOption = () => {
    if (!editData) return;
    const newOptions = [...editData.options];
    newOptions.push({
      order: newOptions.length + 1,
      text: '',
    });
    updateEditData('options', newOptions);
  };

  const removeOption = (index: number) => {
    if (!editData || editData.options.length <= 2) return;
    const newOptions = editData.options.filter((_, i) => i !== index);
    // Reorder the remaining options
    newOptions.forEach((option, i) => {
      option.order = i + 1;
    });
    updateEditData('options', newOptions);

    // Adjust correct answer if needed
    if (editData.correct_answer_order && editData.correct_answer_order > newOptions.length) {
      updateEditData('correct_answer_order', newOptions.length);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto space-y-6 p-6">
        <Card>
          <CardHeader>
            <CardTitle>Admin - Gerenciamento de Questões</CardTitle>
            <CardDescription>
              Gerencie questões com funcionalidade de copiar JSON e editar
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Carregando questões...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto min-h-screen space-y-4 p-4 pb-8">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            {highlightId && (
              <Button
                variant="ghost"
                size="sm"
                onClick={exitSingleQuestionMode}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Voltar para Todas as Questões
              </Button>
            )}
            <div className="flex-1">
              <CardTitle>
                {highlightId ? 'Editar Questão' : 'Admin - Gerenciamento de Questões'}
              </CardTitle>
              <CardDescription>
                {highlightId
                  ? 'Editar esta questão específica'
                  : 'Gerencie questões com funcionalidade de copiar JSON e editar'}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        {!highlightId && (
          <CardContent>
            <div className="mb-4 flex gap-4">
              <div className="flex-1">
                <Label htmlFor="search">Buscar Questões</Label>
                <Input
                  id="search"
                  placeholder="Buscar por texto da questão, número ou matéria..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div className="text-muted-foreground text-sm">
              Encontradas {pagination.total} questões (mostrando {filteredQuestions.length} na
              página {pagination.page})
            </div>
          </CardContent>
        )}
      </Card>

      <div className="space-y-2">
        {filteredQuestions.map((question) => (
          <Card
            key={question.id}
            id={`question-${question.id}`}
            className={`relative ${highlightId === question.id ? 'ring-opacity-50 ring-2 ring-blue-500' : ''}`}
          >
            <CardHeader className="p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="font-medium">Questão {question.number}</span>
                  <Badge variant="secondary">{question.subject}</Badge>
                  {question.topic && <Badge variant="outline">{question.topic}</Badge>}
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={() => copyQuestionJSON(question)}>
                    <Copy className="mr-2 h-4 w-4" />
                    Copiar JSON
                  </Button>
                  {!highlightId && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => startEditing(question)}
                      disabled={editingQuestion === question.id}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Editar
                    </Button>
                  )}
                  {editingQuestion === question.id && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setActiveTab('preview')}
                        disabled={saving}
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        Visualizar
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={openJsonEditor}
                        disabled={saving}
                      >
                        <FileText className="mr-2 h-4 w-4" />
                        Colar JSON
                      </Button>
                    </>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => deleteQuestion(question.id)}
                    disabled={editingQuestion === question.id}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Excluir
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent className="p-3">
              {editingQuestion === question.id && editData ? (
                <div className="space-y-4">
                  <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="grid w-full grid-cols-4">
                      <TabsTrigger value="content">Conteúdo</TabsTrigger>
                      <TabsTrigger value="metadata">Metadados</TabsTrigger>
                      <TabsTrigger value="json">Importar JSON</TabsTrigger>
                      <TabsTrigger value="preview">Visualizar</TabsTrigger>
                    </TabsList>

                    <TabsContent value="content" className="mt-4 space-y-4">
                      <div>
                        <Label htmlFor="associated-text">Texto Associado</Label>
                        <textarea
                          id="associated-text"
                          className="mt-1 min-h-[120px] w-full resize-y rounded-md border p-3 font-mono text-sm"
                          value={editData.associated_text || ''}
                          onChange={(e) =>
                            updateEditData('associated_text', e.target.value || null)
                          }
                          placeholder="Digite qualquer texto que deve ser exibido junto com esta questão (ex: texto para leitura, material de referência, etc.)"
                        />
                      </div>

                      <div>
                        <Label htmlFor="stem">Enunciado da Questão</Label>
                        <textarea
                          id="stem"
                          className="mt-1 min-h-[120px] w-full resize-y rounded-md border p-3 font-mono text-sm"
                          value={editData.stem}
                          onChange={(e) => updateEditData('stem', e.target.value)}
                        />
                      </div>

                      <div>
                        <Label>Opções</Label>
                        <div className="mt-2 space-y-2">
                          {editData.options.map((option, index) => (
                            <div key={index} className="flex items-center gap-2">
                              <span className="w-8 text-sm font-medium">
                                {String.fromCharCode(65 + index)}.
                              </span>
                              <textarea
                                className="flex-1 resize-y rounded-md border p-3 font-mono text-sm"
                                value={option.text}
                                onChange={(e) => updateOption(index, e.target.value)}
                                rows={3}
                              />
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => removeOption(index)}
                                disabled={editData.options.length <= 2}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={addOption}
                            className="w-full"
                          >
                            Adicionar Opção
                          </Button>
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="correct-answer">Resposta Correta</Label>
                        <select
                          id="correct-answer"
                          className="mt-1 w-full rounded-md border p-2"
                          value={editData.correct_answer_order || ''}
                          onChange={(e) =>
                            updateEditData(
                              'correct_answer_order',
                              e.target.value ? parseInt(e.target.value) : null
                            )
                          }
                        >
                          {editData.options.map((_, index) => (
                            <option key={index} value={index + 1}>
                              {String.fromCharCode(65 + index)} - Opção {index + 1}
                            </option>
                          ))}
                        </select>
                      </div>
                    </TabsContent>

                    <TabsContent value="metadata" className="mt-4 space-y-4">
                      <div>
                        <Label htmlFor="number">Número da Questão</Label>
                        <Input
                          id="number"
                          type="number"
                          value={editData.number}
                          onChange={(e) => updateEditData('number', parseInt(e.target.value))}
                        />
                      </div>

                      <div>
                        <Label htmlFor="subject">Matéria</Label>
                        <Input
                          id="subject"
                          value={editData.subject}
                          onChange={(e) => updateEditData('subject', e.target.value)}
                        />
                      </div>

                      <div>
                        <Label htmlFor="topic">Tópico</Label>
                        <Input
                          id="topic"
                          value={editData.topic || ''}
                          onChange={(e) => updateEditData('topic', e.target.value || null)}
                        />
                      </div>

                      <div>
                        <Label htmlFor="change-reason">Motivo da Alteração</Label>
                        <textarea
                          id="change-reason"
                          className="mt-1 w-full resize-y rounded-md border p-3 font-mono text-sm"
                          value={editData.change_reason}
                          onChange={(e) => updateEditData('change_reason', e.target.value)}
                          rows={3}
                        />
                      </div>

                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="is-null"
                          checked={editData.is_null}
                          onChange={(e) => updateEditData('is_null', e.target.checked)}
                        />
                        <Label htmlFor="is-null">Questão está cancelada/anulada</Label>
                      </div>
                    </TabsContent>

                    <TabsContent value="json" className="mt-4 space-y-4">
                      <div>
                        <div className="mb-2 flex items-center justify-between">
                          <Label htmlFor="json-input">Colar JSON Modificado</Label>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={openJsonEditor}
                            disabled={!editData}
                          >
                            Carregar Dados Atuais
                          </Button>
                        </div>
                        <div className="text-muted-foreground mb-2 text-sm">
                          Cole a estrutura JSON completa aqui para substituir os dados atuais da
                          questão. Você pode usar &quot;Carregar Dados Atuais&quot; para preencher o
                          editor com a questão atual.
                        </div>
                        <textarea
                          id="json-input"
                          className="mt-1 w-full resize-y rounded-md border p-3 font-mono text-sm"
                          rows={15}
                          value={jsonInput}
                          onChange={(e) => setJsonInput(e.target.value)}
                          placeholder="Cole seu JSON aqui..."
                        />
                      </div>

                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setShowJsonEditor(false);
                            setJsonInput('');
                            setActiveTab('content');
                          }}
                        >
                          Cancelar
                        </Button>
                        <Button onClick={applyJsonChanges}>Aplicar Alterações JSON</Button>
                      </div>
                    </TabsContent>

                    <TabsContent value="preview" className="mt-4 space-y-4">
                      <div className="space-y-4">
                        {/* Preview Controls */}
                        <div className="bg-muted/30 flex items-center justify-between rounded-lg border p-4">
                          <div>
                            <h3 className="text-foreground text-sm font-medium">
                              Visualização da Questão
                            </h3>
                            <p className="text-muted-foreground text-xs">
                              Veja como esta questão aparece para os usuários
                            </p>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <Label htmlFor="show-answer" className="text-sm">
                                Mostrar Resposta Correta
                              </Label>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => setShowCorrectAnswer(!showCorrectAnswer)}
                                className="h-6 w-10 p-1"
                              >
                                {showCorrectAnswer ? (
                                  <ToggleRight className="h-4 w-4 text-green-600" />
                                ) : (
                                  <ToggleLeft className="text-muted-foreground h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>

                        {/* Preview Question */}
                        <div className="overflow-hidden rounded-lg border">
                          <QuestionCard
                            hideNavigation={true}
                            questionNumber={editData.number}
                            totalQuestions={1}
                            examBoard="Modo Visualização"
                            institution="Visualização Admin"
                            year={new Date().getFullYear()}
                            subject={editData.subject || 'Sem Matéria'}
                            topic={editData.topic || 'Sem Tópico'}
                            questionText={editData.stem}
                            associatedText={editData.associated_text}
                            options={editData.options.map((opt) => ({
                              letter: String.fromCharCode(64 + opt.order),
                              text: opt.text,
                            }))}
                            correctAnswer={
                              showCorrectAnswer && editData.correct_answer_order
                                ? String.fromCharCode(64 + editData.correct_answer_order)
                                : undefined
                            }
                          />
                        </div>

                        {/* Preview Info */}
                        <div className="rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-950/20">
                          <div className="flex items-center gap-2 text-sm">
                            <Eye className="h-4 w-4 text-blue-600" />
                            <span className="font-medium text-blue-900 dark:text-blue-100">
                              Informações da Visualização
                            </span>
                          </div>
                          <ul className="mt-2 space-y-1 text-xs text-blue-700 dark:text-blue-200">
                            <li>
                              • Os metadados do exame (Modo Visualização, Visualização Admin) são
                              apenas para fins de visualização
                            </li>
                            <li>
                              • Use &quot;Mostrar Resposta Correta&quot; para ver como a questão
                              aparece quando respondida
                            </li>
                            <li>
                              • Isso mostra a renderização exata que os usuários verão durante as
                              sessões de estudo
                            </li>
                            {editData.is_null && (
                              <li>
                                • <strong>Nota:</strong> Esta questão está marcada como
                                cancelada/anulada
                              </li>
                            )}
                          </ul>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>

                  <div className="mt-6 flex justify-end gap-2 border-t pt-4">
                    <Button variant="outline" onClick={cancelEditing} disabled={saving}>
                      Cancelar
                    </Button>
                    <Button onClick={saveQuestion} disabled={saving}>
                      {saving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Salvando...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Salvar Alterações
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <QuestionCard
                  hideNavigation={true}
                  questionNumber={question.number}
                  totalQuestions={1}
                  examBoard={question.exam_info?.exam_board}
                  institution={question.exam_info?.institution}
                  year={question.exam_info?.year}
                  position={question.exam_info?.position}
                  specialization={question.exam_info?.specialization}
                  subject={question.subject}
                  topic={question.topic || 'Sem Tópico'}
                  questionText={question.stem}
                  associatedText={question.associated_text}
                  options={question.options.map((option) => ({
                    letter: String.fromCharCode(64 + option.order),
                    text: option.text,
                  }))}
                  correctAnswer={
                    question.correct_answer_order
                      ? String.fromCharCode(64 + question.correct_answer_order)
                      : undefined
                  }
                />
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
