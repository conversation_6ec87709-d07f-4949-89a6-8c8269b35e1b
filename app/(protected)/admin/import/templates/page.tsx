'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/use-auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit2, Trash2, FileText, ShieldX } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

interface OcrTemplate {
  id: string;
  name: string;
  description: string | null;
  promptType: 'prova' | 'gabarito' | 'prova_gabarito' | 'edital';
  promptContent: string;
  modelName: string;
  isDefault: boolean;
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function TemplatesPage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const [templates, setTemplates] = useState<OcrTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedType, setSelectedType] = useState<
    'all' | 'prova' | 'gabarito' | 'prova_gabarito' | 'edital'
  >('all');
  const [editingTemplate, setEditingTemplate] = useState<OcrTemplate | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    promptType: 'prova' as 'prova' | 'gabarito' | 'prova_gabarito' | 'edital',
    promptContent: '',
    modelName: 'gemini-2.5-flash',
    isDefault: false,
  });

  // Check if user is admin
  useEffect(() => {
    if (!isLoading && (!user || user.role !== 'admin')) {
      router.push('/dashboard');
    }
  }, [user, isLoading, router]);

  useEffect(() => {
    if (user && user.role === 'admin') {
      fetchTemplates();
    }
  }, [user]);

  const fetchTemplates = async () => {
    try {
      const response = await fetch('/api/admin/import/templates');
      if (!response.ok) throw new Error('Failed to fetch templates');
      const data = await response.json();
      setTemplates(data.templates);
    } catch (error) {
      console.error('Failed to fetch templates:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async () => {
    try {
      const response = await fetch('/api/admin/import/templates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to create template');

      await fetchTemplates();
      setShowCreateDialog(false);
      resetForm();
    } catch (error) {
      console.error('Failed to create template:', error);
    }
  };

  const handleUpdate = async () => {
    if (!editingTemplate) return;

    try {
      const response = await fetch(`/api/admin/import/templates/${editingTemplate.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      if (!response.ok) throw new Error('Failed to update template');

      await fetchTemplates();
      setEditingTemplate(null);
      resetForm();
    } catch (error) {
      console.error('Failed to update template:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Tem certeza de que deseja excluir este template?')) return;

    try {
      const response = await fetch(`/api/admin/import/templates/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete template');

      await fetchTemplates();
    } catch (error) {
      console.error('Failed to delete template:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      promptType: 'prova',
      promptContent: '',
      modelName: 'gemini-2.5-flash',
      isDefault: false,
    });
  };

  const filteredTemplates =
    selectedType === 'all' ? templates : templates.filter((t) => t.promptType === selectedType);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'prova':
        return 'bg-green-500';
      case 'gabarito':
        return 'bg-orange-500';
      case 'prova_gabarito':
        return 'bg-blue-500';
      case 'edital':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Show loading state
  if (isLoading || loading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <div className="border-primary mx-auto h-12 w-12 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-4">Carregando...</p>
        </div>
      </div>
    );
  }

  // Show unauthorized message
  if (!user || user.role !== 'admin') {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="flex flex-col items-center py-8">
            <ShieldX className="text-destructive mb-4 h-12 w-12" />
            <h3 className="mb-2 text-lg font-semibold">Acesso Negado</h3>
            <p className="text-muted-foreground text-center">
              Você não tem permissão para acessar esta página. Apenas administradores podem
              gerenciar templates.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="mb-2 text-3xl font-bold">Templates OCR</h1>
          <p className="text-muted-foreground">
            Gerenciar templates de prompt para extração de PDF
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Novo Template
        </Button>
      </div>

      <Tabs
        value={selectedType}
        onValueChange={(v) =>
          setSelectedType(v as 'all' | 'prova' | 'gabarito' | 'prova_gabarito' | 'edital')
        }
      >
        <TabsList>
          <TabsTrigger value="all">Todos os Templates</TabsTrigger>
          <TabsTrigger value="prova">Prova</TabsTrigger>
          <TabsTrigger value="gabarito">Gabarito</TabsTrigger>
          <TabsTrigger value="prova_gabarito">Prova + Gabarito</TabsTrigger>
          <TabsTrigger value="edital">Edital</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedType} className="mt-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredTemplates.map((template) => (
              <Card key={template.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <div className="mt-2 flex gap-2">
                        <Badge className={getTypeColor(template.promptType)}>
                          {template.promptType}
                        </Badge>
                        {template.isSystem && <Badge variant="outline">Sistema</Badge>}
                        {template.isDefault && <Badge variant="secondary">Padrão</Badge>}
                      </div>
                    </div>
                    <FileText className="text-muted-foreground h-5 w-5" />
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="mb-4">
                    {template.description || 'Sem descrição'}
                  </CardDescription>
                  <div className="text-muted-foreground mb-4 text-sm">
                    Modelo: {template.modelName}
                  </div>
                  {!template.isSystem && (
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setEditingTemplate(template);
                          setFormData({
                            name: template.name,
                            description: template.description || '',
                            promptType: template.promptType,
                            promptContent: template.promptContent,
                            modelName: template.modelName,
                            isDefault: template.isDefault,
                          });
                        }}
                      >
                        <Edit2 className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => handleDelete(template.id)}>
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      <Dialog
        open={showCreateDialog || !!editingTemplate}
        onOpenChange={(open) => {
          if (!open) {
            setShowCreateDialog(false);
            setEditingTemplate(null);
            resetForm();
          }
        }}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{editingTemplate ? 'Editar Template' : 'Criar Template'}</DialogTitle>
            <DialogDescription>
              {editingTemplate
                ? 'Atualizar o template de prompt OCR'
                : 'Criar um novo template de prompt OCR para extração de PDF'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Nome do template"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descrição</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Descrição do template (opcional)"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Tipo de Documento</Label>
                <Select
                  value={formData.promptType}
                  onValueChange={(value) =>
                    setFormData({
                      ...formData,
                      promptType: value as 'prova' | 'gabarito' | 'prova_gabarito' | 'edital',
                    })
                  }
                >
                  <SelectTrigger id="type">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="prova">Prova</SelectItem>
                    <SelectItem value="gabarito">Gabarito</SelectItem>
                    <SelectItem value="prova_gabarito">Prova + Gabarito</SelectItem>
                    <SelectItem value="edital">Edital</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="model">Modelo</Label>
                <Select
                  value={formData.modelName}
                  onValueChange={(value) => setFormData({ ...formData, modelName: value })}
                >
                  <SelectTrigger id="model">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gemini-2.5-flash">Gemini 2.5 Flash</SelectItem>
                    <SelectItem value="gemini-2.5-pro">Gemini 2.5 Pro</SelectItem>
                    <SelectItem value="gemini-2.0-flash-001">Gemini 2.0 Flash</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="prompt">Conteúdo do Prompt</Label>
              <Textarea
                id="prompt"
                value={formData.promptContent}
                onChange={(e) => setFormData({ ...formData, promptContent: e.target.value })}
                placeholder="Digite o prompt de extração OCR..."
                className="min-h-[200px] font-mono text-sm"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="default"
                checked={formData.isDefault}
                onCheckedChange={(checked) => setFormData({ ...formData, isDefault: checked })}
              />
              <Label htmlFor="default">Definir como padrão para este tipo de documento</Label>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowCreateDialog(false);
                setEditingTemplate(null);
                resetForm();
              }}
            >
              Cancelar
            </Button>
            <Button onClick={editingTemplate ? handleUpdate : handleCreate}>
              {editingTemplate ? 'Atualizar' : 'Criar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
