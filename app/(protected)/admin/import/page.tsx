'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/use-auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { FileUpload } from '@/components/admin/import/file-upload';
import { DataMapper } from '@/components/admin/import/data-mapper';
import { QuestionPreview } from '@/components/admin/import/question-preview';
import { ExamMatcher } from '@/components/admin/import/exam-matcher';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { InfoIcon, ShieldX, Settings } from 'lucide-react';
import Link from 'next/link';
import type { ImportData, MappedData } from '@/types/import';
import { parseMarkdownJSON } from '@/lib/utils/json-parser';

export default function ImportPage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const [importData, setImportData] = useState<ImportData | null>(null);
  const [mappedData, setMappedData] = useState<MappedData | null>(null);
  const [selectedQuestions, setSelectedQuestions] = useState<Set<number>>(new Set());
  const [currentStep, setCurrentStep] = useState<'upload' | 'match' | 'map' | 'preview' | 'import'>(
    'upload'
  );
  const [matchedExamId, setMatchedExamId] = useState<string | null>(null);
  const [matchedTestId, setMatchedTestId] = useState<string | null>(null);
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<{
    success: boolean;
    examId: string;
    testId: string;
    importedCount: number;
    importedQuestions: Array<{ questionId: string; number: number }>;
  } | null>(null);

  // Check if user is admin
  useEffect(() => {
    if (!isLoading && (!user || user.role !== 'admin')) {
      router.push('/dashboard');
    }
  }, [user, isLoading, router]);

  // Add beforeunload event listener when importing
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (importing) {
        e.preventDefault();
        e.returnValue = 'Import process is in progress. Are you sure you want to leave?';
        return e.returnValue;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [importing]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <div className="border-primary mx-auto h-12 w-12 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  // Show unauthorized message
  if (!user || user.role !== 'admin') {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="flex flex-col items-center py-8">
            <ShieldX className="text-destructive mb-4 h-12 w-12" />
            <h3 className="mb-2 text-lg font-semibold">Access Denied</h3>
            <p className="text-muted-foreground text-center">
              You don&apos;t have permission to access this page. Only administrators can import
              questions.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleFileUpload = (data: ImportData) => {
    // Debug log to check if associated_text_references is received
    console.log('File upload received data:', {
      totalQuestions: data.questions.length,
      questionsWithReferences: data.questions.filter((q) => q.associated_text_references).length,
      sampleQuestionWithRef: data.questions.find((q) => q.associated_text_references),
    });

    setImportData(data);

    // Pre-select all questions
    setSelectedQuestions(new Set(data.questions.map((q) => q.number)));

    // Go to map step
    setCurrentStep('map');
  };

  const handlePdfProcess = async (
    file: File,
    promptType: string,
    templateId?: string,
    modelName?: string,
    modelParams?: {
      temperature?: number;
      topP?: number;
      topK?: number;
      thinkingBudget?: number;
      customPrompt?: string;
    },
    keysFile?: File,
    imageExtractionOptions?: {
      extractImages?: boolean;
      imageFolder?: string;
      includeVisualDetection?: boolean;
    }
  ) => {
    const formData = new FormData();
    formData.append('file', file);

    // Map our prompt types to API prompt types
    const apiPromptType = promptType === 'questions_and_keys' ? 'prova' : 'gabarito';
    formData.append('promptType', apiPromptType);

    if (modelName) formData.append('modelName', modelName);
    if (templateId) formData.append('templateId', templateId);

    // Add model parameters
    if (modelParams) {
      if (modelParams.temperature !== undefined)
        formData.append('temperature', modelParams.temperature.toString());
      if (modelParams.topP !== undefined) formData.append('topP', modelParams.topP.toString());
      if (modelParams.topK !== undefined) formData.append('topK', modelParams.topK.toString());
      if (modelParams.thinkingBudget !== undefined)
        formData.append('thinkingBudget', modelParams.thinkingBudget.toString());
      if (modelParams.customPrompt) formData.append('customPrompt', modelParams.customPrompt);
    }

    // Handle special case for JSON + PDF mode (image extraction only)
    if (promptType === 'json_with_images') {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('mode', 'image_extraction_only');

      // Add image extraction options
      if (imageExtractionOptions) {
        if (imageExtractionOptions.extractImages) formData.append('extractImages', 'true');
        if (imageExtractionOptions.imageFolder)
          formData.append('imageFolder', imageExtractionOptions.imageFolder);
        if (imageExtractionOptions.includeVisualDetection)
          formData.append('includeVisualDetection', 'true');
      }

      const response = await fetch('/api/admin/import/ocr-pdf', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Image extraction failed');
      }

      const result = await response.json();
      // For JSON + PDF mode, we only need the extracted images
      handleOcrResultInternal({
        rawText: 'Image extraction completed',
        extractedImages: result.extractedImages,
        imgTagCount: result.imgTagCount,
      });
      return;
    }

    // Handle dual PDF processing if keys file is provided
    if (keysFile) {
      // Use the dual PDF endpoint
      const dualFormData = new FormData();
      dualFormData.append('provaFile', file);
      dualFormData.append('gabaritoFile', keysFile);
      dualFormData.append('modelName', modelName || 'gemini-2.5-flash');

      // Add model parameters
      if (modelParams) {
        if (modelParams.temperature !== undefined)
          dualFormData.append('temperature', modelParams.temperature.toString());
        if (modelParams.topP !== undefined)
          dualFormData.append('topP', modelParams.topP.toString());
        if (modelParams.topK !== undefined)
          dualFormData.append('topK', modelParams.topK.toString());
        if (modelParams.thinkingBudget !== undefined)
          dualFormData.append('thinkingBudget', modelParams.thinkingBudget.toString());
        if (modelParams.customPrompt) dualFormData.append('customPrompt', modelParams.customPrompt);
      }

      // Add image extraction options
      if (imageExtractionOptions) {
        if (imageExtractionOptions.extractImages) dualFormData.append('extractImages', 'true');
        if (imageExtractionOptions.imageFolder)
          dualFormData.append('imageFolder', imageExtractionOptions.imageFolder);
        if (imageExtractionOptions.includeVisualDetection)
          dualFormData.append('includeVisualDetection', 'true');
      }

      const response = await fetch('/api/admin/import/ocr-dual-pdf', {
        method: 'POST',
        body: dualFormData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'OCR processing failed');
      }

      const result = await response.json();
      handleOcrResultInternal(result);
    } else {
      // Add image extraction options for single PDF
      if (imageExtractionOptions) {
        if (imageExtractionOptions.extractImages) formData.append('extractImages', 'true');
        if (imageExtractionOptions.imageFolder)
          formData.append('imageFolder', imageExtractionOptions.imageFolder);
        if (imageExtractionOptions.includeVisualDetection)
          formData.append('includeVisualDetection', 'true');
      }

      // Single PDF processing
      const response = await fetch('/api/admin/import/ocr-pdf', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'OCR processing failed');
      }

      const result = await response.json();
      handleOcrResultInternal(result);
    }
  };

  const handleOcrResultInternal = (result: {
    parsedData?: ImportData;
    rawText: string;
    extractedImages?: Array<{
      success: boolean;
      page_number?: number;
      cloudinary_url?: string;
      method?: string;
    }>;
    imgTagCount?: number;
  }) => {
    // Pass the result to the FileUpload component's handleOcrResult
    type HandleOcrResult = (result: {
      parsedData: ImportData | null;
      rawText: string;
      extractedImages?: Array<{
        success: boolean;
        page_number?: number;
        cloudinary_url?: string;
        method?: string;
      }>;
      imgTagCount?: number;
    }) => void;
    const handleOcrResult = (window as unknown as { handleOcrResult?: HandleOcrResult })
      .handleOcrResult;
    if (handleOcrResult) {
      try {
        // Try to parse the raw text as JSON, handling markdown code blocks
        const parsedData = parseMarkdownJSON<ImportData>(result.rawText);
        handleOcrResult({
          parsedData,
          rawText: result.rawText,
          extractedImages: result.extractedImages,
          imgTagCount: result.imgTagCount,
        });
      } catch {
        // If parsing fails, just pass the raw text
        handleOcrResult({
          parsedData: null,
          rawText: result.rawText,
          extractedImages: result.extractedImages,
          imgTagCount: result.imgTagCount,
        });
      }
    }
  };

  const handleDataMapped = (mapped: MappedData) => {
    setMappedData(mapped);
    setCurrentStep('preview');
  };

  const handleExamMatched = (examId: string, testId: string, mapped: MappedData) => {
    setMatchedExamId(examId);
    setMatchedTestId(testId);
    setMappedData(mapped);
    setCurrentStep('preview');
  };

  const handleImport = async () => {
    setImporting(true);
    try {
      // Debug log to check if associated_text_references is present
      console.log('Import data being sent:', {
        hasAssociatedTextReferences: importData?.questions.some(
          (q) => q.associated_text_references
        ),
        sampleQuestion: importData?.questions.find((q) => q.associated_text_references),
      });

      const response = await fetch('/api/admin/import/process', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          importData,
          mappedData,
          selectedQuestions: Array.from(selectedQuestions),
          matchedExamId,
          matchedTestId,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Falha na importação');
      }

      const result = await response.json();
      setImportResult(result);
    } catch (error) {
      console.error('Import error:', error);
      alert(`Falha na importação: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    } finally {
      setImporting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex items-start justify-between">
        <div>
          <h1 className="mb-2 text-3xl font-bold">Importar Questões</h1>
          <p className="text-muted-foreground">
            Faça upload de arquivos JSON contendo questões de prova e mapeie-as para o banco de
            dados
          </p>
        </div>
        <Link href="/admin/import/templates">
          <Button variant="outline" className="gap-2">
            <Settings className="h-4 w-4" />
            Gerenciar Templates OCR
          </Button>
        </Link>
      </div>

      <Alert className="mb-6">
        <InfoIcon className="h-4 w-4" />
        <AlertDescription>
          Importe arquivos JSON seguindo o formato padrão. O sistema verificará dados existentes e
          permitirá criar novas entradas ou selecionar das existentes.
        </AlertDescription>
      </Alert>

      <Tabs
        value={currentStep}
        onValueChange={(v: string) =>
          setCurrentStep(v as 'upload' | 'match' | 'map' | 'preview' | 'import')
        }
      >
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="upload">Enviar</TabsTrigger>
          <TabsTrigger value="match" disabled={true}>
            Corresponder Prova
          </TabsTrigger>
          <TabsTrigger value="map" disabled={!importData || !!matchedExamId}>
            Mapear Dados
          </TabsTrigger>
          <TabsTrigger value="preview" disabled={!mappedData}>
            Visualizar
          </TabsTrigger>
          <TabsTrigger value="import" disabled={!mappedData}>
            Importar
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Enviar Arquivo JSON</CardTitle>
              <CardDescription>
                Selecione ou arraste e solte um arquivo JSON contendo questões de prova
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FileUpload onFileUploaded={handleFileUpload} onPdfProcess={handlePdfProcess} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="match" className="mt-6">
          {importData && (
            <ExamMatcher
              importData={importData}
              onExamMatched={handleExamMatched}
              onBack={() => setCurrentStep('upload')}
            />
          )}
        </TabsContent>

        <TabsContent value="map" className="mt-6">
          {importData && !matchedExamId && (
            <DataMapper
              importData={importData}
              onDataMapped={handleDataMapped}
              onBack={() => setCurrentStep('upload')}
              onSelectExistingExam={() => setCurrentStep('match')}
            />
          )}
        </TabsContent>

        <TabsContent value="preview" className="mt-6">
          {importData && mappedData && (
            <QuestionPreview
              questions={importData.questions}
              selectedQuestions={selectedQuestions}
              onSelectionChange={setSelectedQuestions}
              onBack={() => setCurrentStep('map')}
              onImport={() => setCurrentStep('import')}
              importData={importData}
            />
          )}
        </TabsContent>

        <TabsContent value="import" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Resumo da Importação</CardTitle>
              <CardDescription>
                {importResult
                  ? 'Importação concluída com sucesso'
                  : 'Revise os detalhes da importação antes de prosseguir'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!importResult ? (
                <div className="space-y-4">
                  <div>
                    <p className="text-muted-foreground text-sm">Questões Selecionadas</p>
                    <p className="text-2xl font-bold">{selectedQuestions.size}</p>
                  </div>
                  <button
                    onClick={handleImport}
                    disabled={importing}
                    className="bg-primary text-primary-foreground hover:bg-primary/90 w-full rounded-md px-4 py-2 disabled:opacity-50"
                  >
                    {importing ? 'Importando...' : 'Iniciar Importação'}
                  </button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="rounded-md bg-green-50 p-4 dark:bg-green-950">
                    <p className="font-medium text-green-900 dark:text-green-100">
                      ✓ Importação concluída com sucesso
                    </p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-muted-foreground text-sm">Questões Importadas</p>
                      <p className="text-2xl font-bold">{importResult.importedCount}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground text-sm">ID do Teste</p>
                      <p className="font-mono text-sm">{importResult.testId}</p>
                    </div>
                  </div>
                  <Button
                    onClick={() => {
                      setImportData(null);
                      setMappedData(null);
                      setSelectedQuestions(new Set());
                      setImportResult(null);
                      setCurrentStep('upload');
                    }}
                    className="w-full"
                  >
                    Importar Outro Arquivo
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
