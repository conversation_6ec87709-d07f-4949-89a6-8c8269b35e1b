'use client';

import { useState, useEffect } from 'react';
import { QuestionCard } from '@/components/question-card';
import { QuestionCardSkeleton } from '@/components/question-card-skeleton';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/use-auth';
import { useComments } from '@/hooks/use-comments';
import { useCommentCounts } from '@/contexts/comment-count-context';
import { toast } from 'sonner';
import { ModelConfig } from '@/lib/openrouter/client';
import { questionToMarkdown } from '@/lib/utils/question-to-markdown';

interface StudyQuestion {
  cardId: string | null;
  questionId: string;
  questionNumber: number;
  // Exam information
  examBoard: string;
  examBoardId?: string;
  institution: string;
  institutionId?: string;
  year: number;
  position: string;
  positionId?: string;
  specialization: string;
  specializationId?: string;
  testType: string;
  booklet?: string | null;
  // Subject and topic
  subject: string;
  subjectId?: string;
  topic: string;
  topicId?: string;
  questionText: string;
  associatedText?: string | null;
  associatedTextReferences?: Array<{ snippet: string; location_pointer: string }> | null;
  correctAnswer?: string;
  options: {
    letter: string;
    text: string;
    commentary?: string;
  }[];
  images?: {
    url: string;
    altText: string;
    order: number;
  }[];
  isNew?: boolean;
  commentCount?: number;
}

export default function StudyPage() {
  const router = useRouter();
  const { user, isLoading: authLoading } = useAuth();
  const { updateCommentCount } = useCommentCounts();
  const [questions, setQuestions] = useState<StudyQuestion[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [schedulingOptions, setSchedulingOptions] = useState<{
    again: { interval: number; due: Date; state: number };
    hard: { interval: number; due: Date; state: number };
    good: { interval: number; due: Date; state: number };
    easy: { interval: number; due: Date; state: number };
  } | null>(null);
  const [isReviewing, setIsReviewing] = useState(false);
  const [, setLoadingScheduling] = useState(false);
  const [currentSeed, setCurrentSeed] = useState<string | null>(null);
  const [activeSettings, setActiveSettings] = useState<{
    availableModels?: ModelConfig[];
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    reasoning?: {
      enabled?: boolean;
      effort?: 'low' | 'medium' | 'high';
      maxTokens?: number;
      exclude?: boolean;
    };
  } | null>(null);

  const currentQuestion = questions[currentIndex];

  // Comments hook
  const { comments, handleVote, handleReply, handleNewComment, handleDelete, handleEdit } =
    useComments({
      questionId: currentQuestion?.questionId || null,
      currentUserId: user?.id,
    });

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/sign-in');
    } else if (user) {
      fetchDueCards();
      fetchActiveSettings();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authLoading, user, router]);

  // Remove automatic fetching of scheduling options
  // We'll fetch them only when the user clicks "Resolver Questão"

  const fetchActiveSettings = async () => {
    try {
      const response = await fetch('/api/model-settings/active');
      if (response.ok) {
        const settings = await response.json();
        setActiveSettings(settings);
      }
    } catch (error) {
      console.error('Error fetching active settings:', error);
    }
  };

  const fetchDueCards = async () => {
    try {
      const response = await fetch('/api/study/due-cards', {
        credentials: 'include',
      });
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch due cards');
      }

      if (data.questions.length === 0) {
        setError(data.message || 'No due cards');
        if (data.nextReview) {
          setError(
            `${data.message}. Next review: ${new Date(data.nextReview).toLocaleString('pt-BR')}`
          );
        }
      } else {
        setQuestions(data.questions);
        // Initialize comment counts in context
        data.questions.forEach((question: StudyQuestion) => {
          if (question.commentCount !== undefined) {
            updateCommentCount(question.questionId, question.commentCount);
          }
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load questions');
    } finally {
      setLoading(false);
    }
  };

  const fetchSchedulingOptions = async (
    cardId: string | null,
    questionId: string,
    seed: string
  ) => {
    try {
      setLoadingScheduling(true);
      const response = await fetch('/api/fsrs/schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ cardId, questionId, seed }),
      });

      const data = await response.json();
      if (response.ok) {
        console.log('[Study Page] Received scheduling options:', data.schedulingOptions);
        setSchedulingOptions(data.schedulingOptions);
      }
    } catch (err) {
      console.error('Failed to fetch scheduling options:', err);
    } finally {
      setLoadingScheduling(false);
    }
  };

  const handleAnswer = (answer: string) => {
    console.log(`Answered ${answer} for question ${currentQuestion.questionId}`);
  };

  const handleReview = async (rating: number) => {
    if (isReviewing || !currentQuestion) return;

    setIsReviewing(true);

    try {
      const response = await fetch('/api/fsrs/review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          cardId: currentQuestion.cardId,
          questionId: currentQuestion.questionId,
          rating,
          seed: currentSeed,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        console.log(
          `Card reviewed. Next review: ${new Date(data.nextReview).toLocaleString('pt-BR')}`
        );

        // Update the cardId for new cards after first review
        if (!currentQuestion.cardId && data.cardId) {
          const updatedQuestions = [...questions];
          const currentQ = updatedQuestions[currentIndex];
          if (currentQ) {
            currentQ.cardId = data.cardId;
            setQuestions(updatedQuestions);
          }
        }

        // Move to next question after a short delay
        setTimeout(() => {
          if (currentIndex < questions.length - 1) {
            setCurrentIndex((prev) => prev + 1);
            setIsReviewing(false);
            // Clear scheduling options for the next question
            setSchedulingOptions(null);
          } else {
            // Session complete
            router.push('/dashboard');
          }
        }, 1000);
      } else {
        throw new Error(data.error || 'Failed to review card');
      }
    } catch (err) {
      console.error('Error reviewing card:', err);
      setIsReviewing(false);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      // Clear scheduling options when navigating
      setSchedulingOptions(null);
    }
  };

  const handleNext = () => {
    if (currentIndex < questions.length - 1) {
      setCurrentIndex(currentIndex + 1);
      // Clear scheduling options when navigating
      setSchedulingOptions(null);
    }
  };

  const formatInterval = (days: number) => {
    if (days < 0.001) {
      return '< 1 min';
    }
    if (days < 0.04) {
      const minutes = Math.round(days * 24 * 60);
      return `${minutes} min`;
    }
    if (days < 1) {
      const hours = Math.round(days * 24);
      return `${hours}h`;
    }
    if (days < 30) {
      const roundedDays = Math.round(days);
      return roundedDays === 1 ? '1 dia' : `${roundedDays} dias`;
    }
    if (days < 365) {
      const months = Math.round(days / 30);
      return months === 1 ? '1 mês' : `${months} meses`;
    }
    const years = Math.round(days / 365);
    return years === 1 ? '1 ano' : `${years} anos`;
  };

  // Admin functions
  const handleAdminCopyJSON = (questionId: string) => {
    const question = questions.find((q) => q.questionId === questionId);
    if (!question) return;

    const jsonData = {
      number: question.questionNumber,
      subject: question.subject,
      topic: question.topic,
      associated_text: question.associatedText || null,
      stem: question.questionText,
      options: question.options.map((opt) => ({
        order: opt.letter.charCodeAt(0) - 64, // Convert A,B,C... to 1,2,3...
        text: opt.text,
      })),
      images: [], // TODO: Add images support if needed
      correct_answer_order: question.correctAnswer
        ? question.correctAnswer.charCodeAt(0) - 64
        : null,
      is_null: false,
      change_reason: '',
    };

    navigator.clipboard.writeText(JSON.stringify(jsonData, null, 2));
    toast.success('Question JSON copied to clipboard');
  };

  const handleAdminCopyMarkdown = (questionId: string) => {
    const question = questions.find((q) => q.questionId === questionId);
    if (!question) return;

    const markdown = questionToMarkdown({
      examBoard: question.examBoard,
      institution: question.institution,
      year: question.year,
      position: question.position,
      specialization: question.specialization,
      subject: question.subject,
      topic: question.topic,
      associatedText: question.associatedText || undefined,
      questionText: question.questionText,
      options: question.options,
      correctAnswer: question.correctAnswer,
    });

    navigator.clipboard.writeText(markdown);
    toast.success('Question copied as Markdown');
  };

  const handleAdminEdit = (questionId: string) => {
    router.push(`/admin/questions?id=${questionId}`);
  };

  const handleAdminDelete = async (questionId: string) => {
    if (
      !confirm('Tem certeza de que deseja excluir esta questão? Esta ação não pode ser desfeita.')
    ) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/questions/${questionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Falha ao excluir questão');
      }

      // Remove from local state and adjust current index
      const updatedQuestions = questions.filter((q) => q.questionId !== questionId);
      setQuestions(updatedQuestions);

      // Adjust current index if needed
      if (currentIndex >= updatedQuestions.length) {
        setCurrentIndex(Math.max(0, updatedQuestions.length - 1));
      }

      toast.success('Questão excluída com sucesso');
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error('Falha ao excluir questão');
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex h-full flex-col">
        <main className="flex-1">
          <div className="mx-auto max-w-5xl px-6">
            <QuestionCardSkeleton showNavigation />
          </div>
        </main>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  if (error || questions.length === 0) {
    return (
      <div className="bg-background flex min-h-screen items-center justify-center">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold">{error || 'Nenhuma revisão pendente'}</h1>
          <button
            onClick={() => router.push('/dashboard')}
            className="bg-primary text-primary-foreground hover:bg-primary/90 mt-4 rounded-md px-4 py-2"
          >
            Voltar ao Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex h-full flex-col">
      <main className="flex-1">
        <div className="mx-auto max-w-5xl px-6">
          {currentQuestion && (
            <QuestionCard
              {...currentQuestion}
              associatedText={currentQuestion.associatedText}
              associatedTextReferences={currentQuestion.associatedTextReferences}
              questionNumber={currentIndex + 1}
              totalQuestions={questions.length}
              questionId={currentQuestion.questionId}
              onAnswer={handleAnswer}
              onReview={handleReview}
              onPrevious={handlePrevious}
              onNext={handleNext}
              onBeforeSubmit={async () => {
                // Fetch scheduling options when user clicks "Resolver Questão"
                if (!schedulingOptions && currentQuestion) {
                  const currentIdentifier = currentQuestion.cardId || currentQuestion.questionId;

                  // Generate a seed based on cardId or questionId and current minute
                  const now = new Date();
                  const minuteTimestamp = Math.floor(now.getTime() / 60000) * 60000;
                  const seed = `${currentIdentifier}-${minuteTimestamp}`;
                  setCurrentSeed(seed);

                  await fetchSchedulingOptions(
                    currentQuestion.cardId,
                    currentQuestion.questionId,
                    seed
                  );
                }
              }}
              comments={comments}
              commentCount={currentQuestion.commentCount}
              currentUserId={user?.id}
              onCommentVote={handleVote}
              onCommentReply={handleReply}
              onNewComment={handleNewComment}
              onCommentDelete={handleDelete}
              onCommentEdit={handleEdit}
              fsrsIntervals={
                schedulingOptions
                  ? [
                      {
                        rating: 1,
                        label: 'Errei',
                        interval: formatInterval(schedulingOptions.again.interval),
                        emoji: '😵',
                      },
                      {
                        rating: 2,
                        label: 'Difícil',
                        interval: formatInterval(schedulingOptions.hard.interval),
                        emoji: '😓',
                      },
                      {
                        rating: 3,
                        label: 'Bom',
                        interval: formatInterval(schedulingOptions.good.interval),
                        emoji: '😊',
                      },
                      {
                        rating: 4,
                        label: 'Fácil',
                        interval: formatInterval(schedulingOptions.easy.interval),
                        emoji: '😎',
                      },
                    ]
                  : undefined
              }
              // Admin props
              userRole={user?.role}
              onAdminCopyJSON={handleAdminCopyJSON}
              onAdminCopyMarkdown={handleAdminCopyMarkdown}
              onAdminEdit={handleAdminEdit}
              onAdminDelete={handleAdminDelete}
              // AI Assistant props
              showAIAssistant={true}
              availableModels={activeSettings?.availableModels}
              maxTokens={activeSettings?.maxTokens}
              temperature={activeSettings?.temperature}
              topP={activeSettings?.topP}
              frequencyPenalty={activeSettings?.frequencyPenalty}
              presencePenalty={activeSettings?.presencePenalty}
              reasoning={activeSettings?.reasoning}
            />
          )}
        </div>
      </main>
    </div>
  );
}
