'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Briefcase,
  BookOpen,
  Users,
  TrendingUp,
  Building2,
  Calendar,
  ArrowLeft,
  Target,
  AlertCircle,
} from 'lucide-react';
import { QuestionCard } from '@/components/question-card';
import { Question } from '@/types/question';
import { QuestionCardSkeleton } from '@/components/question-card-skeleton';

interface PositionDetail {
  id: string;
  name: string;
  code?: string;
  totalQuestions: number;
  totalExams: number;
  totalSpecializations: number;
  specializations: {
    id: string;
    name: string;
    code?: string;
    questionCount: number;
  }[];
  examHistory: {
    id: string;
    year: number;
    institution: string;
    institutionId: string;
    institutionCode?: string;
    examBoard: string;
    examBoardId: string;
    questionCount: number;
    specialization?: string;
    specializationId?: string;
  }[];
  subjectDistribution: {
    id: string;
    name: string;
    questionCount: number;
    percentage: number;
  }[];
  competitionTrend: {
    year: number;
    questionCount: number;
    examCount: number;
  }[];
  topInstitutions: {
    id: string;
    name: string;
    code?: string;
    examCount: number;
    lastExam: string;
  }[];
}

export default function PositionDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [position, setPosition] = useState<PositionDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(false);

  useEffect(() => {
    const fetchPosition = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`/api/positions/${params.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Cargo não encontrado');
          } else {
            throw new Error('Failed to fetch position');
          }
          return;
        }

        const data = await response.json();
        setPosition(data);
      } catch (error) {
        console.error('Error fetching position:', error);
        setError('Erro ao carregar dados do cargo');
      } finally {
        setLoading(false);
      }
    };

    fetchPosition();
  }, [params.id]);

  const fetchQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const response = await fetch(`/api/questions?position=${position?.name}&limit=5`);

      if (!response.ok) {
        throw new Error('Failed to fetch questions');
      }

      const data = await response.json();
      setQuestions(data.questions || []);
    } catch (error) {
      console.error('Error fetching questions:', error);
    } finally {
      setLoadingQuestions(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Skeleton className="mb-2 h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error || !position) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Alert className="mx-auto mb-4 max-w-md">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error || 'Cargo não encontrado'}</AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/positions')}>Voltar para Cargos</Button>
        </div>
      </div>
    );
  }

  const latestYear = position.competitionTrend.length > 0 ? position.competitionTrend[0] : null;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/positions')}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar para Cargos
        </Button>

        <div className="flex items-start justify-between">
          <div>
            <div className="mb-2 flex items-center gap-3">
              <h1 className="text-3xl font-bold">
                {position.code ? `${position.code} - ` : ''}
                {position.name}
              </h1>
            </div>
            <p className="text-muted-foreground max-w-3xl">
              Cargo com {position.totalExams} concursos realizados e mais de{' '}
              {position.totalQuestions.toLocaleString('pt-BR')} questões em nosso banco de dados.
            </p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <BookOpen className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">
                {position.totalQuestions.toLocaleString('pt-BR')}
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Concursos Realizados</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Briefcase className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{position.totalExams}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Especializações</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Target className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{position.totalSpecializations}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Tendência</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <TrendingUp className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{latestYear ? latestYear.examCount : 0}</span>
              <span className="text-muted-foreground text-sm">
                em {latestYear ? latestYear.year : new Date().getFullYear()}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="specializations">Especializações</TabsTrigger>
          <TabsTrigger value="history">Histórico</TabsTrigger>
          <TabsTrigger value="subjects">Matérias</TabsTrigger>
          <TabsTrigger value="questions">Questões</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Competition Trend */}
          <Card>
            <CardHeader>
              <CardTitle>Evolução Anual</CardTitle>
              <CardDescription>Questões e concursos ao longo dos anos</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {position.competitionTrend.map((trend) => (
                  <div key={trend.year} className="flex items-center justify-between">
                    <div
                      className="flex cursor-pointer items-center gap-3"
                      onClick={() => router.push(`/years/${trend.year}`)}
                    >
                      <Calendar className="text-muted-foreground h-4 w-4" />
                      <span className="font-medium hover:underline">{trend.year}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <Badge variant="secondary">
                        {trend.questionCount.toLocaleString('pt-BR')} questões
                      </Badge>
                      <Badge variant="outline">{trend.examCount} concursos</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Institutions */}
          <Card>
            <CardHeader>
              <CardTitle>Principais Instituições</CardTitle>
              <CardDescription>Órgãos que mais contratam para este cargo</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {position.topInstitutions.map((institution) => (
                  <div
                    key={institution.id}
                    className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-3"
                    onClick={() => router.push(`/institutions/${institution.id}`)}
                  >
                    <div className="flex items-center gap-3">
                      <Building2 className="text-muted-foreground h-5 w-5" />
                      <div>
                        <p className="font-medium">
                          {institution.code ? `${institution.code} - ` : ''}
                          {institution.name}
                        </p>
                        <p className="text-muted-foreground text-sm">
                          {institution.examCount} concursos realizados
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline">Último: {institution.lastExam}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="specializations" className="space-y-4">
          {position.specializations.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Especializações Disponíveis</CardTitle>
                <CardDescription>Áreas de especialização para este cargo</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {position.specializations.map((spec) => (
                    <div
                      key={spec.id}
                      className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-4"
                      onClick={() => router.push(`/specializations/${spec.id}`)}
                    >
                      <div>
                        <p className="font-medium">
                          {spec.code ? `${spec.code} - ` : ''}
                          {spec.name}
                        </p>
                      </div>
                      <Badge variant="outline">
                        {spec.questionCount.toLocaleString('pt-BR')} questões
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="py-12 text-center">
                <Users className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">Sem Especializações</h3>
                <p className="text-muted-foreground">
                  Este cargo não possui especializações cadastradas
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Histórico de Concursos</CardTitle>
              <CardDescription>Concursos realizados para este cargo</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {position.examHistory.map((exam) => (
                  <div key={exam.id} className="rounded-lg border p-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="mb-1 flex items-center gap-3">
                          <Calendar className="text-muted-foreground h-4 w-4" />
                          <span className="font-medium">{exam.year}</span>
                          <p
                            className="cursor-pointer font-medium hover:underline"
                            onClick={() => router.push(`/institutions/${exam.institutionId}`)}
                          >
                            {exam.institutionCode ? `${exam.institutionCode} - ` : ''}
                            {exam.institution}
                          </p>
                        </div>
                        <div className="mt-1 flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className="hover:bg-accent cursor-pointer"
                            onClick={() => router.push(`/exam-boards/${exam.examBoardId}`)}
                          >
                            {exam.examBoard}
                          </Badge>
                          {exam.specialization && (
                            <>
                              <span className="text-muted-foreground text-sm">•</span>
                              <span
                                className="text-muted-foreground cursor-pointer text-sm hover:underline"
                                onClick={() =>
                                  exam.specializationId &&
                                  router.push(`/specializations/${exam.specializationId}`)
                                }
                              >
                                {exam.specialization}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold">
                          {exam.questionCount.toLocaleString('pt-BR')}
                        </p>
                        <p className="text-muted-foreground text-xs">questões</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Matérias Cobradas</CardTitle>
              <CardDescription>Distribuição de questões por matéria neste cargo</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {position.subjectDistribution.map((subject) => (
                  <div
                    key={subject.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/subjects/${subject.id}`)}
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">{subject.name}</span>
                      <span className="text-muted-foreground text-sm">
                        {subject.questionCount.toLocaleString('pt-BR')} ({subject.percentage}%)
                      </span>
                    </div>
                    <div className="bg-muted h-2 overflow-hidden rounded-full">
                      <div
                        className="bg-primary/60 h-full"
                        style={{ width: `${subject.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          {questions.length === 0 && !loadingQuestions ? (
            <Card>
              <CardContent className="py-12 text-center">
                <BookOpen className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">Questões de {position.name}</h3>
                <p className="text-muted-foreground mb-4">
                  Veja exemplos de questões cobradas para este cargo
                </p>
                <Button onClick={fetchQuestions}>Ver Questões de Exemplo</Button>
              </CardContent>
            </Card>
          ) : loadingQuestions ? (
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <QuestionCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="space-y-6">
              {questions.map((question) => (
                <QuestionCard
                  key={question.id}
                  questionNumber={question.number}
                  examBoard={question.exam.examBoard || undefined}
                  examBoardId={question.exam.examBoardId}
                  institution={question.institution?.name || question.exam.name}
                  institutionId={question.institution?.id}
                  year={question.exam.year || undefined}
                  position={question.position?.name}
                  positionId={question.position?.id}
                  specialization={question.specialization?.name}
                  specializationId={question.specialization?.id}
                  subject={question.subject?.name || ''}
                  subjectId={question.subject?.id}
                  topic={question.topic?.name || ''}
                  topicId={question.topic?.id}
                  questionText={question.stem}
                  associatedText={question.associatedText}
                  options={question.options.map((opt) => ({
                    letter: opt.optionLetter,
                    text: opt.text,
                  }))}
                  correctAnswer={question.options.find((opt) => opt.isCorrect)?.optionLetter}
                  hideNavigation
                  totalQuestions={questions.length}
                  commentCount={question.commentCount}
                />
              ))}
              {questions.length > 0 && (
                <div className="pt-4 text-center">
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/questions?position=${position.name}`)}
                  >
                    Ver Mais Questões
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
