'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Briefcase, BookOpen, TrendingUp, Search, Users } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface Position {
  id: string;
  name: string;
  level: 'superior' | 'medio' | 'fundamental' | 'tecnico';
  category:
    | 'administrativo'
    | 'fiscal'
    | 'juridico'
    | 'educacao'
    | 'saude'
    | 'tecnologia'
    | 'engenharia'
    | 'seguranca';
  questionCount: number;
  examCount: number;
  institutionCount: number;
  averageSalary: {
    min: number;
    max: number;
  };
  popularInstitutions: {
    name: string;
    acronym?: string;
  }[];
  requiredSubjects: string[];
  difficultyAverage: number;
  competitionRatio: number;
}

// Mock data - replace with actual API call
const mockPositions: Position[] = [
  {
    id: 'auditor-fiscal',
    name: 'Auditor Fiscal',
    level: 'superior',
    category: 'fiscal',
    questionCount: 4850,
    examCount: 32,
    institutionCount: 25,
    averageSalary: { min: 15000, max: 35000 },
    popularInstitutions: [
      { name: 'Receita Federal', acronym: 'RFB' },
      { name: 'Secretarias Estaduais' },
      { name: 'Prefeituras' },
    ],
    requiredSubjects: [
      'Direito Tributário',
      'Contabilidade',
      'Língua Portuguesa',
      'Matemática Financeira',
    ],
    difficultyAverage: 4.5,
    competitionRatio: 120,
  },
  {
    id: 'analista-judiciario',
    name: 'Analista Judiciário',
    level: 'superior',
    category: 'juridico',
    questionCount: 6230,
    examCount: 45,
    institutionCount: 38,
    averageSalary: { min: 12000, max: 18000 },
    popularInstitutions: [
      { name: 'TRF', acronym: 'TRF' },
      { name: 'TJ', acronym: 'TJ' },
      { name: 'TRT', acronym: 'TRT' },
    ],
    requiredSubjects: [
      'Direito Constitucional',
      'Direito Administrativo',
      'Língua Portuguesa',
      'Direito Processual',
    ],
    difficultyAverage: 4.2,
    competitionRatio: 85,
  },
  {
    id: 'tecnico-seguro-social',
    name: 'Técnico do Seguro Social',
    level: 'medio',
    category: 'administrativo',
    questionCount: 3450,
    examCount: 8,
    institutionCount: 1,
    averageSalary: { min: 5000, max: 7000 },
    popularInstitutions: [{ name: 'INSS', acronym: 'INSS' }],
    requiredSubjects: [
      'Língua Portuguesa',
      'Informática',
      'Direito Previdenciário',
      'Raciocínio Lógico',
    ],
    difficultyAverage: 3.4,
    competitionRatio: 150,
  },
  {
    id: 'professor',
    name: 'Professor',
    level: 'superior',
    category: 'educacao',
    questionCount: 8920,
    examCount: 68,
    institutionCount: 152,
    averageSalary: { min: 3000, max: 12000 },
    popularInstitutions: [
      { name: 'Secretarias Estaduais' },
      { name: 'Secretarias Municipais' },
      { name: 'Institutos Federais' },
    ],
    requiredSubjects: [
      'Conhecimentos Pedagógicos',
      'Língua Portuguesa',
      'Legislação Educacional',
      'Conhecimentos Específicos',
    ],
    difficultyAverage: 3.2,
    competitionRatio: 45,
  },
  {
    id: 'analista-sistemas',
    name: 'Analista de Sistemas',
    level: 'superior',
    category: 'tecnologia',
    questionCount: 2870,
    examCount: 35,
    institutionCount: 42,
    averageSalary: { min: 8000, max: 15000 },
    popularInstitutions: [
      { name: 'Banco do Brasil', acronym: 'BB' },
      { name: 'Dataprev' },
      { name: 'Serpro' },
    ],
    requiredSubjects: ['Programação', 'Banco de Dados', 'Redes', 'Segurança da Informação'],
    difficultyAverage: 3.8,
    competitionRatio: 60,
  },
  {
    id: 'agente-policia-federal',
    name: 'Agente da Polícia Federal',
    level: 'superior',
    category: 'seguranca',
    questionCount: 1680,
    examCount: 5,
    institutionCount: 1,
    averageSalary: { min: 12000, max: 16000 },
    popularInstitutions: [{ name: 'Polícia Federal', acronym: 'PF' }],
    requiredSubjects: [
      'Direito Penal',
      'Direito Processual Penal',
      'Língua Portuguesa',
      'Informática',
    ],
    difficultyAverage: 4.3,
    competitionRatio: 200,
  },
];

const levelLabels = {
  superior: 'Nível Superior',
  medio: 'Nível Médio',
  fundamental: 'Nível Fundamental',
  tecnico: 'Nível Técnico',
};

const levelColors = {
  superior: 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400',
  medio: 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400',
  fundamental: 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400',
  tecnico: 'bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400',
};

const categoryLabels = {
  administrativo: 'Administrativo',
  fiscal: 'Fiscal',
  juridico: 'Jurídico',
  educacao: 'Educação',
  saude: 'Saúde',
  tecnologia: 'Tecnologia',
  engenharia: 'Engenharia',
  seguranca: 'Segurança',
};

export default function PositionsPage() {
  const router = useRouter();
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setPositions(mockPositions);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredPositions = positions.filter((position) =>
    position.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalQuestions = positions.reduce((sum, pos) => sum + pos.questionCount, 0);
  const totalExams = positions.reduce((sum, pos) => sum + pos.examCount, 0);
  const avgCompetition =
    positions.reduce((sum, pos) => sum + pos.competitionRatio, 0) / positions.length;

  const formatSalary = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Cargos</h1>
          <p className="text-muted-foreground mt-2">Explore questões e estatísticas por cargo</p>
        </div>

        {/* Loading skeleton for stats */}
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Loading skeleton for grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="mt-2 h-4 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-5/6" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Cargos</h1>
        <p className="text-muted-foreground mt-2">Explore questões e estatísticas por cargo</p>
      </div>

      {/* Overall Statistics */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Cargos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{positions.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalQuestions.toLocaleString('pt-BR')}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Concursos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalExams}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Concorrência Média</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgCompetition.toFixed(0)}:1</div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
          <Input
            placeholder="Buscar cargo..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Positions Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredPositions.map((position) => (
          <Card
            key={position.id}
            className="cursor-pointer transition-shadow hover:shadow-lg"
            onClick={() => router.push(`/positions/${position.id}`)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-base">{position.name}</CardTitle>
                  <div className="mt-2 flex items-center gap-2">
                    <Badge className={cn(levelColors[position.level])}>
                      {levelLabels[position.level]}
                    </Badge>
                    <Badge variant="outline">{categoryLabels[position.category]}</Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Salary Range */}
              <div className="bg-muted/50 rounded-lg p-3">
                <p className="text-muted-foreground mb-1 text-xs">Faixa Salarial</p>
                <p className="text-sm font-medium">
                  {formatSalary(position.averageSalary.min)} -{' '}
                  {formatSalary(position.averageSalary.max)}
                </p>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <BookOpen className="h-3.5 w-3.5" />
                    <span className="text-xs">Questões</span>
                  </div>
                  <div className="font-medium">
                    {position.questionCount.toLocaleString('pt-BR')}
                  </div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <Users className="h-3.5 w-3.5" />
                    <span className="text-xs">Concorrência</span>
                  </div>
                  <div className="font-medium">{position.competitionRatio}:1</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <Briefcase className="h-3.5 w-3.5" />
                    <span className="text-xs">Concursos</span>
                  </div>
                  <div className="font-medium">{position.examCount}</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <TrendingUp className="h-3.5 w-3.5" />
                    <span className="text-xs">Dificuldade</span>
                  </div>
                  <div className="font-medium">{position.difficultyAverage.toFixed(1)}/5</div>
                </div>
              </div>

              {/* Popular Institutions */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Instituições Frequentes</p>
                <div className="flex flex-wrap gap-1">
                  {position.popularInstitutions.slice(0, 3).map((inst) => (
                    <Badge key={inst.name} variant="secondary" className="text-xs">
                      {inst.acronym || inst.name}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Required Subjects */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Matérias Principais</p>
                <div className="flex flex-wrap gap-1">
                  {position.requiredSubjects.slice(0, 3).map((subject) => (
                    <Badge key={subject} variant="outline" className="text-xs">
                      {subject}
                    </Badge>
                  ))}
                  {position.requiredSubjects.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{position.requiredSubjects.length - 3}
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredPositions.length === 0 && (
        <div className="py-12 text-center">
          <p className="text-muted-foreground">Nenhum cargo encontrado</p>
        </div>
      )}
    </div>
  );
}
