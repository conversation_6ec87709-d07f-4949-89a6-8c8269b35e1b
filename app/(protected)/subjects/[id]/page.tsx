'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  BookOpen,
  Building2,
  ArrowLeft,
  TrendingUp,
  AlertCircle,
  Brain,
  Target,
} from 'lucide-react';
import { QuestionCard } from '@/components/question-card';
import { Question } from '@/types/question';
import { QuestionCardSkeleton } from '@/components/question-card-skeleton';

interface SubjectDetail {
  id: string;
  name: string;
  totalQuestions: number;
  totalTopics: number;
  totalExams: number;
  yearlyTrend: {
    year: number;
    questionCount: number;
  }[];
  topExamBoards: {
    id: string;
    name: string;
    questionCount: number;
    percentage: number;
  }[];
  topInstitutions: {
    id: string;
    name: string;
    code?: string;
    acronym?: string;
    questionCount: number;
    lastExam: string;
  }[];
  topPositions: {
    id: string;
    name: string;
    institution: string;
    questionCount: number;
  }[];
  topics: {
    id: string;
    name: string;
    questionCount: number;
    percentage: number;
  }[];
  questionDistribution: {
    type: string;
    count: number;
    percentage: number;
  }[];
}

export default function SubjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [subject, setSubject] = useState<SubjectDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(false);

  useEffect(() => {
    const fetchSubject = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`/api/subjects/${params.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Matéria não encontrada');
          } else {
            throw new Error('Failed to fetch subject');
          }
          return;
        }

        const data = await response.json();
        setSubject(data);
      } catch (error) {
        console.error('Error fetching subject:', error);
        setError('Erro ao carregar dados da matéria');
      } finally {
        setLoading(false);
      }
    };

    fetchSubject();
  }, [params.id]);

  const fetchQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const response = await fetch(`/api/questions?subject=${subject?.name}&limit=5`);

      if (!response.ok) {
        throw new Error('Failed to fetch questions');
      }

      const data = await response.json();
      setQuestions(data.questions || []);
    } catch (error) {
      console.error('Error fetching questions:', error);
    } finally {
      setLoadingQuestions(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Skeleton className="mb-2 h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error || !subject) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Alert className="mx-auto mb-4 max-w-md">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error || 'Matéria não encontrada'}</AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/subjects')}>Voltar para Matérias</Button>
        </div>
      </div>
    );
  }

  const maxYearlyQuestions = Math.max(...subject.yearlyTrend.map((y) => y.questionCount));

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button variant="ghost" size="sm" onClick={() => router.push('/subjects')} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar para Matérias
        </Button>

        <div className="flex items-start justify-between">
          <div>
            <h1 className="mb-2 text-3xl font-bold">{subject.name}</h1>
            <p className="text-muted-foreground max-w-3xl">
              Matéria com {subject.totalExams} concursos realizados e mais de{' '}
              {subject.totalQuestions.toLocaleString('pt-BR')} questões em nosso banco de dados.
            </p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <BookOpen className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">
                {subject.totalQuestions.toLocaleString('pt-BR')}
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Tópicos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Brain className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{subject.totalTopics}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Concursos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Target className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{subject.totalExams}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Questões/Concurso</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <TrendingUp className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">
                {subject.totalExams > 0
                  ? Math.round(subject.totalQuestions / subject.totalExams)
                  : 0}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="topics">Tópicos</TabsTrigger>
          <TabsTrigger value="institutions">Instituições</TabsTrigger>
          <TabsTrigger value="statistics">Estatísticas</TabsTrigger>
          <TabsTrigger value="questions">Questões</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Yearly Trend */}
          <Card>
            <CardHeader>
              <CardTitle>Evolução Anual</CardTitle>
              <CardDescription>Número de questões ao longo dos anos</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex h-48 items-end gap-2">
                {subject.yearlyTrend.map((year) => {
                  const height =
                    maxYearlyQuestions > 0 ? (year.questionCount / maxYearlyQuestions) * 100 : 0;
                  return (
                    <div key={year.year} className="group flex flex-1 flex-col items-center gap-2">
                      <div className="text-xs font-medium opacity-0 transition-opacity group-hover:opacity-100">
                        {year.questionCount.toLocaleString('pt-BR')}
                      </div>
                      <div
                        className="bg-primary/20 hover:bg-primary/30 w-full rounded-t transition-colors"
                        style={{ height: `${height}%` }}
                      />
                      <span
                        className="text-muted-foreground cursor-pointer text-xs hover:underline"
                        onClick={() => router.push(`/years/${year.year}`)}
                      >
                        {year.year}
                      </span>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Top Exam Boards */}
          <Card>
            <CardHeader>
              <CardTitle>Principais Bancas</CardTitle>
              <CardDescription>Bancas que mais cobram esta matéria</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {subject.topExamBoards.map((board) => (
                  <div
                    key={board.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/exam-boards/${board.id}`)}
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">{board.name}</span>
                      <span className="text-muted-foreground text-sm">
                        {board.questionCount.toLocaleString('pt-BR')} ({board.percentage}%)
                      </span>
                    </div>
                    <div className="bg-muted h-2 overflow-hidden rounded-full">
                      <div
                        className="bg-primary/60 h-full"
                        style={{ width: `${board.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Question Type Distribution */}
          {subject.questionDistribution.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Tipos de Questão</CardTitle>
                <CardDescription>Distribuição por tipo de questão</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {subject.questionDistribution.map((type) => (
                    <div key={type.type} className="flex items-center justify-between">
                      <span className="text-sm font-medium">{type.type}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-muted-foreground text-sm">
                          {type.count.toLocaleString('pt-BR')}
                        </span>
                        <Badge variant="secondary">{type.percentage}%</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="topics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Tópicos da Matéria</CardTitle>
              <CardDescription>Principais tópicos cobrados</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {subject.topics.map((topic) => (
                  <div
                    key={topic.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/topics/${topic.id}`)}
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">{topic.name}</span>
                      <span className="text-muted-foreground text-sm">
                        {topic.questionCount.toLocaleString('pt-BR')} ({topic.percentage}%)
                      </span>
                    </div>
                    <div className="bg-muted h-2 overflow-hidden rounded-full">
                      <div
                        className="bg-primary/60 h-full"
                        style={{ width: `${topic.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="institutions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Instituições que Mais Cobram</CardTitle>
              <CardDescription>Órgãos com mais questões desta matéria</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {subject.topInstitutions.map((institution) => (
                  <div
                    key={institution.id}
                    className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-3"
                    onClick={() => router.push(`/institutions/${institution.id}`)}
                  >
                    <div className="flex items-center gap-3">
                      <Building2 className="text-muted-foreground h-5 w-5" />
                      <div>
                        <p className="font-medium">
                          {institution.acronym ? `${institution.acronym} - ` : ''}
                          {institution.name}
                        </p>
                        <p className="text-muted-foreground text-sm">
                          {institution.questionCount.toLocaleString('pt-BR')} questões
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline">Último: {institution.lastExam}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Positions */}
          <Card>
            <CardHeader>
              <CardTitle>Cargos que Mais Cobram</CardTitle>
              <CardDescription>Cargos com mais questões desta matéria</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {subject.topPositions.map((position) => (
                  <div
                    key={position.id}
                    className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-3"
                    onClick={() => router.push(`/positions/${position.id}`)}
                  >
                    <div>
                      <p className="font-medium">{position.name}</p>
                      <p className="text-muted-foreground text-sm">{position.institution}</p>
                    </div>
                    <Badge variant="outline">
                      {position.questionCount.toLocaleString('pt-BR')} questões
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Estatísticas Detalhadas</CardTitle>
              <CardDescription>Análise da evolução da matéria</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <p className="mb-2 text-sm font-medium">Crescimento Anual</p>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    {subject.yearlyTrend.slice(0, 4).map((year, idx) => {
                      const prevYear = subject.yearlyTrend[idx + 1];
                      const growth = prevYear
                        ? (
                            ((year.questionCount - prevYear.questionCount) /
                              prevYear.questionCount) *
                            100
                          ).toFixed(1)
                        : null;

                      return (
                        <div key={year.year} className="flex items-center justify-between">
                          <span className="text-muted-foreground">{year.year}</span>
                          {growth && (
                            <span
                              className={`font-medium ${parseFloat(growth) > 0 ? 'text-green-600' : 'text-red-600'}`}
                            >
                              {parseFloat(growth) > 0 ? '+' : ''}
                              {growth}%
                            </span>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                <div>
                  <p className="mb-2 text-sm font-medium">Média de Questões</p>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Por Concurso</span>
                      <span className="font-medium">
                        {subject.totalExams > 0
                          ? Math.round(subject.totalQuestions / subject.totalExams)
                          : 0}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Por Tópico</span>
                      <span className="font-medium">
                        {subject.totalTopics > 0
                          ? Math.round(subject.totalQuestions / subject.totalTopics)
                          : 0}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          {questions.length === 0 && !loadingQuestions ? (
            <Card>
              <CardContent className="py-12 text-center">
                <BookOpen className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">Questões de {subject.name}</h3>
                <p className="text-muted-foreground mb-4">
                  Veja exemplos de questões desta matéria
                </p>
                <Button onClick={fetchQuestions}>Ver Questões de Exemplo</Button>
              </CardContent>
            </Card>
          ) : loadingQuestions ? (
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <QuestionCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="space-y-6">
              {questions.map((question) => (
                <QuestionCard
                  key={question.id}
                  questionNumber={question.number}
                  examBoard={question.exam.examBoard || undefined}
                  examBoardId={question.exam.examBoardId}
                  institution={question.institution?.name || question.exam.name}
                  institutionId={question.institution?.id}
                  year={question.exam.year || undefined}
                  position={question.position?.name}
                  positionId={question.position?.id}
                  specialization={question.specialization?.name}
                  specializationId={question.specialization?.id}
                  subject={question.subject?.name || ''}
                  subjectId={question.subject?.id}
                  topic={question.topic?.name || ''}
                  topicId={question.topic?.id}
                  questionText={question.stem}
                  associatedText={question.associatedText}
                  options={question.options.map((opt) => ({
                    letter: opt.optionLetter,
                    text: opt.text,
                  }))}
                  correctAnswer={question.options.find((opt) => opt.isCorrect)?.optionLetter}
                  hideNavigation
                  totalQuestions={questions.length}
                  commentCount={question.commentCount}
                />
              ))}
              {questions.length > 0 && (
                <div className="pt-4 text-center">
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/questions?subject=${subject.name}`)}
                  >
                    Ver Mais Questões
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
