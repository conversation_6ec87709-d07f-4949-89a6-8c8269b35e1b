'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { BookOpen, Users, TrendingUp, Search, GraduationCap } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface Subject {
  id: string;
  name: string;
  category: 'exatas' | 'humanas' | 'juridicas' | 'linguagens' | 'tecnologia' | 'saude' | 'geral';
  questionCount: number;
  examCount: number;
  topicCount: number;
  averageDifficulty: number;
  successRate: number;
  popularTopics: {
    name: string;
    count: number;
  }[];
  examBoardDistribution: {
    name: string;
    percentage: number;
  }[];
  requiredFor: string[]; // Positions that require this subject
  yearlyTrend: {
    year: number;
    count: number;
  }[];
}

// Mock data - replace with actual API call
const mockSubjects: Subject[] = [
  {
    id: 'lingua-portuguesa',
    name: 'Língua Portuguesa',
    category: 'linguagens',
    questionCount: 18500,
    examCount: 285,
    topicCount: 42,
    averageDifficulty: 3.4,
    successRate: 52.3,
    popularTopics: [
      { name: 'Interpretação de Texto', count: 4250 },
      { name: 'Gramática', count: 3800 },
      { name: 'Ortografia', count: 2150 },
    ],
    examBoardDistribution: [
      { name: 'CESPE', percentage: 35 },
      { name: 'FCC', percentage: 28 },
      { name: 'FGV', percentage: 22 },
      { name: 'Outros', percentage: 15 },
    ],
    requiredFor: ['Todos os cargos'],
    yearlyTrend: [
      { year: 2022, count: 5800 },
      { year: 2023, count: 6200 },
      { year: 2024, count: 6500 },
    ],
  },
  {
    id: 'direito-administrativo',
    name: 'Direito Administrativo',
    category: 'juridicas',
    questionCount: 12800,
    examCount: 198,
    topicCount: 38,
    averageDifficulty: 4.1,
    successRate: 41.2,
    popularTopics: [
      { name: 'Licitações e Contratos', count: 3200 },
      { name: 'Atos Administrativos', count: 2450 },
      { name: 'Servidores Públicos', count: 1980 },
    ],
    examBoardDistribution: [
      { name: 'FCC', percentage: 32 },
      { name: 'CESPE', percentage: 30 },
      { name: 'FGV', percentage: 25 },
      { name: 'Outros', percentage: 13 },
    ],
    requiredFor: ['Analista Judiciário', 'Auditor', 'Procurador'],
    yearlyTrend: [
      { year: 2022, count: 4100 },
      { year: 2023, count: 4300 },
      { year: 2024, count: 4400 },
    ],
  },
  {
    id: 'matematica',
    name: 'Matemática',
    category: 'exatas',
    questionCount: 9850,
    examCount: 165,
    topicCount: 28,
    averageDifficulty: 3.8,
    successRate: 45.7,
    popularTopics: [
      { name: 'Porcentagem', count: 2100 },
      { name: 'Razão e Proporção', count: 1850 },
      { name: 'Regra de Três', count: 1620 },
    ],
    examBoardDistribution: [
      { name: 'VUNESP', percentage: 28 },
      { name: 'FCC', percentage: 26 },
      { name: 'CESPE', percentage: 24 },
      { name: 'Outros', percentage: 22 },
    ],
    requiredFor: ['Professor', 'Técnico', 'Assistente Administrativo'],
    yearlyTrend: [
      { year: 2022, count: 3200 },
      { year: 2023, count: 3350 },
      { year: 2024, count: 3300 },
    ],
  },
  {
    id: 'informatica',
    name: 'Informática',
    category: 'tecnologia',
    questionCount: 8640,
    examCount: 142,
    topicCount: 35,
    averageDifficulty: 3.5,
    successRate: 48.9,
    popularTopics: [
      { name: 'Windows', count: 1850 },
      { name: 'MS Office', count: 1620 },
      { name: 'Internet e Navegadores', count: 1380 },
    ],
    examBoardDistribution: [
      { name: 'FCC', percentage: 30 },
      { name: 'CESPE', percentage: 28 },
      { name: 'VUNESP', percentage: 22 },
      { name: 'Outros', percentage: 20 },
    ],
    requiredFor: ['Técnico', 'Assistente', 'Analista de TI'],
    yearlyTrend: [
      { year: 2022, count: 2800 },
      { year: 2023, count: 2920 },
      { year: 2024, count: 2920 },
    ],
  },
  {
    id: 'direito-constitucional',
    name: 'Direito Constitucional',
    category: 'juridicas',
    questionCount: 10250,
    examCount: 176,
    topicCount: 32,
    averageDifficulty: 4.0,
    successRate: 43.5,
    popularTopics: [
      { name: 'Direitos e Garantias', count: 2450 },
      { name: 'Organização do Estado', count: 2100 },
      { name: 'Poderes', count: 1850 },
    ],
    examBoardDistribution: [
      { name: 'CESPE', percentage: 34 },
      { name: 'FCC', percentage: 29 },
      { name: 'FGV', percentage: 24 },
      { name: 'Outros', percentage: 13 },
    ],
    requiredFor: ['Analista Judiciário', 'Procurador', 'Defensor'],
    yearlyTrend: [
      { year: 2022, count: 3300 },
      { year: 2023, count: 3450 },
      { year: 2024, count: 3500 },
    ],
  },
  {
    id: 'raciocinio-logico',
    name: 'Raciocínio Lógico',
    category: 'exatas',
    questionCount: 7890,
    examCount: 138,
    topicCount: 22,
    averageDifficulty: 3.9,
    successRate: 42.8,
    popularTopics: [
      { name: 'Lógica Proposicional', count: 2100 },
      { name: 'Conjuntos', count: 1450 },
      { name: 'Sequências', count: 1280 },
    ],
    examBoardDistribution: [
      { name: 'CESPE', percentage: 32 },
      { name: 'FCC', percentage: 28 },
      { name: 'FGV', percentage: 25 },
      { name: 'Outros', percentage: 15 },
    ],
    requiredFor: ['Técnico', 'Analista', 'Auditor'],
    yearlyTrend: [
      { year: 2022, count: 2500 },
      { year: 2023, count: 2690 },
      { year: 2024, count: 2700 },
    ],
  },
];

const categoryLabels = {
  exatas: 'Exatas',
  humanas: 'Humanas',
  juridicas: 'Jurídicas',
  linguagens: 'Linguagens',
  tecnologia: 'Tecnologia',
  saude: 'Saúde',
  geral: 'Geral',
};

const categoryColors = {
  exatas: 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400',
  humanas: 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-400',
  juridicas: 'bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400',
  linguagens: 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400',
  tecnologia: 'bg-cyan-100 text-cyan-700 dark:bg-cyan-900/20 dark:text-cyan-400',
  saude: 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400',
  geral: 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400',
};

export default function SubjectsPage() {
  const router = useRouter();
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setSubjects(mockSubjects);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredSubjects = subjects.filter((subject) =>
    subject.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalQuestions = subjects.reduce((sum, subject) => sum + subject.questionCount, 0);
  const totalTopics = subjects.reduce((sum, subject) => sum + subject.topicCount, 0);
  const avgSuccessRate =
    subjects.reduce((sum, subject) => sum + subject.successRate, 0) / subjects.length;

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Matérias</h1>
          <p className="text-muted-foreground mt-2">Explore questões e estatísticas por matéria</p>
        </div>

        {/* Loading skeleton for stats */}
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Loading skeleton for grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="mt-2 h-4 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-5/6" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Matérias</h1>
        <p className="text-muted-foreground mt-2">Explore questões e estatísticas por matéria</p>
      </div>

      {/* Overall Statistics */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Matérias</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{subjects.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalQuestions.toLocaleString('pt-BR')}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Tópicos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTopics}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Taxa de Acerto Média</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgSuccessRate.toFixed(1)}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
          <Input
            placeholder="Buscar matéria..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Subjects Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredSubjects.map((subject) => (
          <Card
            key={subject.id}
            className="cursor-pointer transition-shadow hover:shadow-lg"
            onClick={() => router.push(`/subjects/${subject.id}`)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-base">{subject.name}</CardTitle>
                  <div className="mt-2 flex items-center gap-2">
                    <Badge className={cn(categoryColors[subject.category])}>
                      {categoryLabels[subject.category]}
                    </Badge>
                    <Badge variant="secondary">
                      {subject.questionCount.toLocaleString('pt-BR')} questões
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Mini Chart - Yearly Trend */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Tendência Anual</p>
                <div className="flex h-12 items-end gap-1">
                  {subject.yearlyTrend.map((year) => {
                    const maxCount = Math.max(...subject.yearlyTrend.map((y) => y.count));
                    const height = (year.count / maxCount) * 100;
                    return (
                      <div key={year.year} className="flex flex-1 flex-col items-center gap-1">
                        <div
                          className="bg-primary/20 hover:bg-primary/30 w-full rounded-t transition-colors"
                          style={{ height: `${height}%` }}
                          title={`${year.year}: ${year.count} questões`}
                        />
                        <span className="text-muted-foreground text-[9px]">{year.year}</span>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <BookOpen className="h-3.5 w-3.5" />
                    <span className="text-xs">Tópicos</span>
                  </div>
                  <div className="font-medium">{subject.topicCount}</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <Users className="h-3.5 w-3.5" />
                    <span className="text-xs">Concursos</span>
                  </div>
                  <div className="font-medium">{subject.examCount}</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <TrendingUp className="h-3.5 w-3.5" />
                    <span className="text-xs">Taxa de Acerto</span>
                  </div>
                  <div className="font-medium">{subject.successRate}%</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-1.5">
                    <GraduationCap className="h-3.5 w-3.5" />
                    <span className="text-xs">Dificuldade</span>
                  </div>
                  <div className="font-medium">{subject.averageDifficulty.toFixed(1)}/5</div>
                </div>
              </div>

              {/* Popular Topics */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Tópicos Populares</p>
                <div className="space-y-1">
                  {subject.popularTopics.slice(0, 3).map((topic) => (
                    <div key={topic.name} className="flex items-center justify-between">
                      <span className="truncate text-xs">{topic.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {topic.count.toLocaleString('pt-BR')}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>

              {/* Exam Board Distribution */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Distribuição por Banca</p>
                <div className="flex gap-1">
                  {subject.examBoardDistribution.map((board) => (
                    <div
                      key={board.name}
                      className="bg-muted flex h-2 flex-1 overflow-hidden rounded-full"
                      title={`${board.name}: ${board.percentage}%`}
                    >
                      <div className="bg-primary/60" style={{ width: `${board.percentage}%` }} />
                    </div>
                  ))}
                </div>
                <div className="mt-1 flex flex-wrap gap-1">
                  {subject.examBoardDistribution.slice(0, 2).map((board) => (
                    <span key={board.name} className="text-muted-foreground text-[10px]">
                      {board.name} {board.percentage}%
                    </span>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredSubjects.length === 0 && (
        <div className="py-12 text-center">
          <p className="text-muted-foreground">Nenhuma matéria encontrada</p>
        </div>
      )}
    </div>
  );
}
