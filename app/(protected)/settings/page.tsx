'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/theme-toggle';

export default function SettingsPage() {
  return (
    <div className="space-y-6 p-6">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Configurações</h2>
        <p className="text-muted-foreground">Gerencie suas preferências do aplicativo</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Aparência</CardTitle>
          <CardDescription>Personalize como o aplicativo se parece</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="theme">Tema</Label>
              <p className="text-muted-foreground text-sm">Escolha entre modo claro e escuro</p>
            </div>
            <ThemeToggle />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Preferências de Estudo</CardTitle>
          <CardDescription>Configure sua experiência de aprendizado</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="notifications">Lembretes Diários</Label>
              <p className="text-muted-foreground text-sm">
                Receba notificações sobre cartões pendentes para revisão
              </p>
            </div>
            <Switch id="notifications" disabled />
          </div>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="sound">Efeitos Sonoros</Label>
              <p className="text-muted-foreground text-sm">
                Reproduzir sons durante sessões de estudo
              </p>
            </div>
            <Switch id="sound" disabled />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Dados e Privacidade</CardTitle>
          <CardDescription>Gerencie seus dados</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button variant="outline" disabled>
            Exportar Dados de Estudo (Em Breve)
          </Button>
          <Button variant="outline" className="text-destructive" disabled>
            Excluir Conta (Em Breve)
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
