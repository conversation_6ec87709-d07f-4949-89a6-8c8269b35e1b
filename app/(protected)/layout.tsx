'use client';

import { useAuth } from '@/hooks/use-auth';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import {
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarFooter,
  SidebarTrigger,
  SidebarInset,
  SidebarRail,
} from '@/components/ui/sidebar';
import {
  Home,
  GraduationCap,
  Settings,
  LogOut,
  Library,
  Upload,
  HelpCircle,
  Sparkles,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/theme-toggle';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { ReputationProvider } from '@/contexts/reputation-context';
import { CommentCountProvider } from '@/contexts/comment-count-context';

export default function ProtectedLayout({ children }: { children: React.ReactNode }) {
  const { user, logout, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  if (isLoading || !user) {
    return (
      <div className="bg-background flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="border-primary mx-auto h-12 w-12 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-4">Carregando...</p>
        </div>
      </div>
    );
  }

  const navigation = [
    { name: 'Painel', href: '/dashboard', icon: Home },
    { name: 'Estudar', href: '/study', icon: GraduationCap },
    { name: 'Baralhos', href: '/decks', icon: Library },
    { name: 'Questões', href: '/questions', icon: HelpCircle },
    { name: 'Configurações', href: '/settings', icon: Settings },
  ];

  const adminNavigation = [
    { name: 'Importar Questões', href: '/admin/import', icon: Upload },
    { name: 'Gerenciar Questões', href: '/admin/questions', icon: Settings },
    { name: 'Prompts de IA', href: '/admin/ai-prompts', icon: Sparkles },
    { name: 'Modelos de IA', href: '/admin/model-settings', icon: Settings },
  ];

  return (
    <ReputationProvider>
      <CommentCountProvider>
        <SidebarProvider>
          <div className="flex h-screen w-full overflow-hidden">
            <Sidebar collapsible="icon">
              <SidebarHeader className="p-4">
                <h2 className="text-xl font-bold group-data-[collapsible=icon]:hidden">Memo App</h2>
              </SidebarHeader>
              <SidebarContent>
                <SidebarGroup>
                  <SidebarGroupLabel>Navegação</SidebarGroupLabel>
                  <SidebarGroupContent>
                    <SidebarMenu>
                      {navigation.map((item) => (
                        <SidebarMenuItem key={item.href}>
                          <SidebarMenuButton asChild isActive={pathname === item.href}>
                            <Link href={item.href}>
                              <item.icon className="h-4 w-4" />
                              <span>{item.name}</span>
                            </Link>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      ))}
                    </SidebarMenu>
                  </SidebarGroupContent>
                </SidebarGroup>
                {/* Admin navigation - only shown to admin users */}
                {user.role === 'admin' && (
                  <SidebarGroup>
                    <SidebarGroupLabel>Administração</SidebarGroupLabel>
                    <SidebarGroupContent>
                      <SidebarMenu>
                        {adminNavigation.map((item) => (
                          <SidebarMenuItem key={item.href}>
                            <SidebarMenuButton asChild isActive={pathname === item.href}>
                              <Link href={item.href}>
                                <item.icon className="h-4 w-4" />
                                <span>{item.name}</span>
                              </Link>
                            </SidebarMenuButton>
                          </SidebarMenuItem>
                        ))}
                      </SidebarMenu>
                    </SidebarGroupContent>
                  </SidebarGroup>
                )}
              </SidebarContent>
              <SidebarFooter className="p-4 group-data-[collapsible=icon]:p-2">
                <div className="mb-2 flex gap-2 group-data-[collapsible=icon]:mb-1 group-data-[collapsible=icon]:flex-col group-data-[collapsible=icon]:items-center group-data-[collapsible=icon]:gap-1">
                  <ThemeToggle />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleLogout}
                    className="hover:bg-sidebar-accent hover:text-sidebar-accent-foreground flex-1 text-red-500 group-data-[collapsible=icon]:h-8 group-data-[collapsible=icon]:w-8 group-data-[collapsible=icon]:flex-none group-data-[collapsible=icon]:p-0 hover:text-red-600"
                  >
                    <LogOut className="h-4 w-4 group-data-[collapsible=icon]:mr-0" />
                    <span className="ml-2 group-data-[collapsible=icon]:hidden">Sair</span>
                  </Button>
                </div>
                <div className="group-data-[collapsible=icon]:mb-0">
                  <Link
                    href="/profile"
                    className="hover:bg-sidebar-accent hover:text-sidebar-accent-foreground flex min-w-0 items-center gap-3 rounded-lg p-2 transition-colors group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:hover:bg-transparent group-data-[collapsible=icon]:hover:text-inherit"
                  >
                    <Avatar className="h-8 w-8 flex-shrink-0">
                      <AvatarImage src={user.image || undefined} alt={user.name || user.email} />
                      <AvatarFallback className="text-xs">
                        {user.name
                          ? user.name
                              .split(' ')
                              .map((n) => n[0])
                              .join('')
                              .toUpperCase()
                          : user.email[0].toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1 text-sm group-data-[collapsible=icon]:hidden">
                      <p className="truncate font-medium">{user.name || user.email}</p>
                      <p className="text-muted-foreground truncate text-xs">{user.email}</p>
                    </div>
                  </Link>
                </div>
              </SidebarFooter>
              <SidebarRail />
            </Sidebar>
            <SidebarInset className="flex flex-col overflow-hidden">
              <header className="flex items-center border-b px-4 py-3">
                <SidebarTrigger />
                <h1 className="ml-2 text-lg font-semibold">
                  {navigation.find((item) => item.href === pathname)?.name ||
                    adminNavigation.find((item) => item.href === pathname)?.name ||
                    'Página'}
                </h1>
              </header>
              <main className="bg-background flex-1 overflow-auto">{children}</main>
            </SidebarInset>
          </div>
        </SidebarProvider>
      </CommentCountProvider>
    </ReputationProvider>
  );
}
