'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Loader2, Plus, Trash2, <PERSON>, BookOpen } from 'lucide-react';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { DeckGridSkeleton } from '@/components/deck-card-skeleton';

interface Deck {
  id: string;
  title: string;
  description: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function DecksPage() {
  const router = useRouter();
  const [decks, setDecks] = useState<Deck[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [newDeck, setNewDeck] = useState({ title: '', description: '' });
  const [dialogOpen, setDialogOpen] = useState(false);

  useEffect(() => {
    fetchDecks();
  }, []);

  const fetchDecks = async () => {
    try {
      const response = await fetch('/api/decks');
      if (!response.ok) throw new Error('Failed to fetch decks');
      const data = await response.json();
      setDecks(data);
    } catch {
      toast.error('Falha ao carregar baralhos');
    } finally {
      setLoading(false);
    }
  };

  const createDeck = async () => {
    if (!newDeck.title.trim()) {
      toast.error('Título é obrigatório');
      return;
    }

    setCreating(true);
    try {
      const response = await fetch('/api/decks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newDeck),
      });

      if (!response.ok) throw new Error('Failed to create deck');

      const deck = await response.json();
      setDecks([...decks, deck]);
      setNewDeck({ title: '', description: '' });
      setDialogOpen(false);
      toast.success('Baralho criado com sucesso');
    } catch {
      toast.error('Falha ao criar baralho');
    } finally {
      setCreating(false);
    }
  };

  const deleteDeck = async (deckId: string) => {
    try {
      const response = await fetch(`/api/decks/${deckId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete deck');

      setDecks(decks.filter((d) => d.id !== deckId));
      toast.success('Baralho excluído com sucesso');
    } catch {
      toast.error('Falha ao excluir baralho');
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Meus Baralhos</h1>
            <p className="text-muted-foreground">Crie e gerencie seus baralhos de estudo</p>
          </div>
          <div className="opacity-50">
            <Button disabled>
              <Plus className="mr-2 h-4 w-4" />
              Criar Baralho
            </Button>
          </div>
        </div>
        <DeckGridSkeleton count={6} />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Meus Baralhos</h1>
          <p className="text-muted-foreground mt-2">Crie e gerencie seus baralhos de estudo</p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Criar Baralho
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Criar Novo Baralho</DialogTitle>
              <DialogDescription>
                Crie um novo baralho para organizar suas questões de estudo
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="title">Título</Label>
                <Input
                  id="title"
                  value={newDeck.title}
                  onChange={(e) => setNewDeck({ ...newDeck, title: e.target.value })}
                  placeholder="Digite o título do baralho"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Descrição (opcional)</Label>
                <Textarea
                  id="description"
                  value={newDeck.description}
                  onChange={(e) => setNewDeck({ ...newDeck, description: e.target.value })}
                  placeholder="Digite a descrição do baralho"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button onClick={createDeck} disabled={creating || !newDeck.title.trim()}>
                {creating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Criar Baralho
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {decks.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <BookOpen className="text-muted-foreground mb-4 h-12 w-12" />
            <h3 className="mb-2 text-lg font-semibold">Nenhum baralho ainda</h3>
            <p className="text-muted-foreground mb-4 text-center">
              Crie seu primeiro baralho para começar a organizar suas questões de estudo
            </p>
            <Button onClick={() => setDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Criar Primeiro Baralho
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {decks.map((deck) => (
            <Card key={deck.id} className="cursor-pointer transition-shadow hover:shadow-lg">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1" onClick={() => router.push(`/decks/${deck.id}`)}>
                    <CardTitle className="line-clamp-1">{deck.title}</CardTitle>
                    {deck.description && (
                      <CardDescription className="mt-2 line-clamp-2">
                        {deck.description}
                      </CardDescription>
                    )}
                  </div>
                  <div className="ml-2 flex items-center gap-2">
                    <Badge variant={deck.isActive ? 'default' : 'secondary'}>
                      {deck.isActive ? 'Ativo' : 'Inativo'}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <p className="text-muted-foreground text-sm">
                    Atualizado{' '}
                    {formatDistanceToNow(new Date(deck.updatedAt), {
                      addSuffix: true,
                      locale: ptBR,
                    })}
                  </p>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/decks/${deck.id}`);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button size="sm" variant="ghost" onClick={(e) => e.stopPropagation()}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Excluir Baralho</AlertDialogTitle>
                          <AlertDialogDescription>
                            Tem certeza de que deseja excluir &quot;{deck.title}&quot;? Esta ação
                            não pode ser desfeita.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancelar</AlertDialogCancel>
                          <AlertDialogAction onClick={() => deleteDeck(deck.id)}>
                            Excluir
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
