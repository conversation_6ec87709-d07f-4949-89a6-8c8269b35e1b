'use client';

import { useState, useEffect, useCallback, use } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Loader2,
  Search,
  ChevronLeft,
  ChevronRight,
  ArrowLeft,
  Plus,
  CheckSquare,
  Square,
  Users,
} from 'lucide-react';
import { toast } from 'sonner';
import { debounce } from 'lodash';
import { QuestionCard } from '@/components/question-card';

interface Question {
  id: string;
  number: number;
  stem: string;
  associatedText: string | null;
  options: Array<{
    id: string;
    optionLetter: string;
    text: string;
    isCorrect: boolean;
  }>;
  exam: {
    id: string;
    name: string;
    examBoard: string | null;
    year: number | null;
  };
  position: {
    id: string;
    name: string;
  } | null;
  specialization: {
    id: string;
    name: string;
  } | null;
  subject: {
    id: string;
    name: string;
  } | null;
  topic: {
    id: string;
    name: string;
  } | null;
}

interface Deck {
  id: string;
  title: string;
  description: string | null;
  isActive: boolean;
}

interface Filters {
  examBoards: string[];
  years: number[];
  subjects: Array<{ id: string; name: string }>;
  exams: Array<{
    id: string;
    name: string;
    examBoard: string | null;
    year: number | null;
  }>;
}

export default function AddQuestionsPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const { id: deckId } = use(params);

  const [deck, setDeck] = useState<Deck | null>(null);
  const [deckQuestionIds, setDeckQuestionIds] = useState<Set<string>>(new Set());
  const [questions, setQuestions] = useState<Question[]>([]);
  const [filters, setFilters] = useState<Filters | null>(null);
  const [loading, setLoading] = useState(true);
  const [searching, setSearching] = useState(false);
  const [selectedQuestions, setSelectedQuestions] = useState<Set<string>>(new Set());
  const [adding, setAdding] = useState(false);

  // Search parameters
  const [search, setSearch] = useState('');
  const [examBoard, setExamBoard] = useState('all');
  const [year, setYear] = useState('all');
  const [subjectId, setSubjectId] = useState('all');
  const [examId] = useState('all');
  const [sortBy, setSortBy] = useState('number');
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(1);
  const [totalQuestions, setTotalQuestions] = useState(0);

  // Fetch deck information and current questions
  const fetchDeckInfo = useCallback(async () => {
    try {
      const response = await fetch(`/api/decks/${deckId}/questions`);
      if (!response.ok) throw new Error('Failed to fetch deck');

      const data = await response.json();
      setDeck(data.deck);
      setDeckQuestionIds(
        new Set(data.questions.map((q: { question: { id: string } }) => q.question.id))
      );
    } catch {
      toast.error('Failed to load deck information');
      router.push('/decks');
    }
  }, [deckId, router]);

  // Fetch available questions
  const fetchQuestions = useCallback(async () => {
    setSearching(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pageSize.toString(),
        sortBy,
        ...(search && { search }),
        ...(examBoard && examBoard !== 'all' && { examBoard }),
        ...(year && year !== 'all' && { year }),
        ...(subjectId && subjectId !== 'all' && { subjectId }),
        ...(examId && examId !== 'all' && { examId }),
      });

      const response = await fetch(`/api/questions?${params}`);
      if (!response.ok) throw new Error('Failed to fetch questions');

      const data = await response.json();
      setQuestions(data.questions);
      setFilters(data.filters);
      setTotalPages(data.pagination.totalPages);
      setTotalQuestions(data.pagination.total);
    } catch {
      toast.error('Failed to load questions');
    } finally {
      setSearching(false);
      setLoading(false);
    }
  }, [page, pageSize, search, examBoard, year, subjectId, examId, sortBy]);

  const debouncedSearch = useCallback((value: string) => {
    const handler = debounce(() => {
      setSearch(value);
      setPage(1);
    }, 500);
    handler();
  }, []);

  useEffect(() => {
    fetchDeckInfo();
  }, [fetchDeckInfo]);

  useEffect(() => {
    if (deck) {
      fetchQuestions();
    }
  }, [deck, fetchQuestions]);

  // Selection handlers
  const toggleQuestionSelection = (questionId: string) => {
    const newSelected = new Set(selectedQuestions);
    if (newSelected.has(questionId)) {
      newSelected.delete(questionId);
    } else {
      newSelected.add(questionId);
    }
    setSelectedQuestions(newSelected);
  };

  const selectAllVisible = () => {
    const availableQuestions = questions.filter((q) => !deckQuestionIds.has(q.id));
    const newSelected = new Set(selectedQuestions);
    availableQuestions.forEach((q) => newSelected.add(q.id));
    setSelectedQuestions(newSelected);
  };

  const deselectAll = () => {
    setSelectedQuestions(new Set());
  };

  // Add questions to deck
  const addSelectedQuestions = async () => {
    if (selectedQuestions.size === 0) {
      toast.error('Please select questions to add');
      return;
    }

    setAdding(true);
    try {
      const response = await fetch(`/api/decks/${deckId}/questions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ questionIds: Array.from(selectedQuestions) }),
      });

      if (!response.ok) throw new Error('Failed to add questions');

      const result = await response.json();

      // Update deck questions set
      const newDeckQuestionIds = new Set(deckQuestionIds);
      selectedQuestions.forEach((id) => newDeckQuestionIds.add(id));
      setDeckQuestionIds(newDeckQuestionIds);

      // Clear selection
      setSelectedQuestions(new Set());

      toast.success(`Added ${result.added} questions to deck`);
      if (result.skipped > 0) {
        toast.info(`${result.skipped} questions were already in the deck`);
      }
    } catch {
      toast.error('Failed to add questions to deck');
    } finally {
      setAdding(false);
    }
  };

  if (loading || !deck) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  const availableQuestions = questions.filter((q) => !deckQuestionIds.has(q.id));
  const selectedAvailableCount = Array.from(selectedQuestions).filter((id) =>
    questions.some((q) => q.id === id && !deckQuestionIds.has(q.id))
  ).length;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-6">
        <Button variant="ghost" onClick={() => router.push(`/decks/${deckId}`)} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Deck
        </Button>

        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-2xl">
                  Add Questions to &quot;{deck.title}&quot;
                </CardTitle>
                {deck.description && (
                  <p className="text-muted-foreground mt-2">{deck.description}</p>
                )}
                <div className="mt-4 flex items-center gap-2">
                  <Badge variant="outline">
                    <Users className="mr-1 h-3 w-3" />
                    {deckQuestionIds.size} questions in deck
                  </Badge>
                  {selectedQuestions.size > 0 && (
                    <Badge variant="default">{selectedAvailableCount} selected</Badge>
                  )}
                </div>
              </div>
              {selectedQuestions.size > 0 && (
                <Button onClick={addSelectedQuestions} disabled={adding}>
                  {adding && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <Plus className="mr-2 h-4 w-4" />
                  Add Selected ({selectedAvailableCount})
                </Button>
              )}
            </div>
          </CardHeader>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Find Questions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div>
              <Label htmlFor="search">Search</Label>
              <Input
                id="search"
                placeholder="Search by question text..."
                onChange={(e) => debouncedSearch(e.target.value)}
              />
            </div>

            <div>
              <Label>Exam Board</Label>
              <Select value={examBoard} onValueChange={setExamBoard}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Exam Boards</SelectItem>
                  {filters?.examBoards.map((board) => (
                    <SelectItem key={board} value={board}>
                      {board}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Year</Label>
              <Select value={year} onValueChange={setYear}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Years</SelectItem>
                  {filters?.years.map((y) => (
                    <SelectItem key={y} value={y.toString()}>
                      {y}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Subject</Label>
              <Select value={subjectId} onValueChange={setSubjectId}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Subjects</SelectItem>
                  {filters?.subjects.map((subject) => (
                    <SelectItem key={subject.id} value={subject.id}>
                      {subject.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Sort By</Label>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="number">Question Number</SelectItem>
                  <SelectItem value="subject">Subject</SelectItem>
                  <SelectItem value="year">Year</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Bulk selection tools */}
          <div className="flex items-center gap-4 border-t pt-4">
            <Button
              variant="outline"
              size="sm"
              onClick={selectAllVisible}
              disabled={availableQuestions.length === 0}
            >
              <CheckSquare className="mr-2 h-4 w-4" />
              Select All Visible ({availableQuestions.length})
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={deselectAll}
              disabled={selectedQuestions.size === 0}
            >
              <Square className="mr-2 h-4 w-4" />
              Clear Selection
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Questions List */}
      <div className="space-y-2">
        {searching && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="mr-2 h-6 w-6 animate-spin" />
            <span>Searching questions...</span>
          </div>
        )}

        {!searching && questions.length === 0 && (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-16">
              <Search className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="mb-2 text-lg font-semibold">No questions found</h3>
              <p className="text-muted-foreground text-center">
                Try adjusting your search filters to find more questions
              </p>
            </CardContent>
          </Card>
        )}

        {!searching &&
          questions.map((question) => {
            const isInDeck = deckQuestionIds.has(question.id);
            const isSelected = selectedQuestions.has(question.id);

            return (
              <div
                key={question.id}
                className={`rounded-lg border transition-all duration-200 ${
                  isSelected
                    ? 'border-primary bg-primary/5 ring-primary/20 ring-1'
                    : isInDeck
                      ? 'border-muted bg-muted/30'
                      : 'bg-card hover:border-muted-foreground/20'
                }`}
              >
                <div className="flex gap-2 p-3">
                  {/* Selection controls - compact side column */}
                  <div className="flex min-w-fit flex-col items-center gap-1 pt-0.5">
                    <Checkbox
                      checked={isSelected || isInDeck}
                      onCheckedChange={() => !isInDeck && toggleQuestionSelection(question.id)}
                      disabled={isInDeck}
                      className={`${
                        isInDeck
                          ? 'data-[state=checked]:border-green-500 data-[state=checked]:bg-green-500 dark:data-[state=checked]:border-green-400 dark:data-[state=checked]:bg-green-400'
                          : 'data-[state=checked]:bg-primary data-[state=checked]:border-primary'
                      }`}
                    />
                  </div>

                  {/* Question content */}
                  <div className="min-w-0 flex-1">
                    <QuestionCard
                      hideNavigation={true}
                      questionNumber={question.number}
                      totalQuestions={1}
                      examBoard={question.exam.examBoard || undefined}
                      institution={question.exam.name}
                      year={question.exam.year || undefined}
                      position={question.position?.name}
                      specialization={question.specialization?.name}
                      subject={question.subject?.name || 'Unknown Subject'}
                      topic={question.topic?.name || 'No Topic'}
                      questionText={question.stem}
                      associatedText={question.associatedText}
                      options={question.options.map((option) => ({
                        letter: option.optionLetter,
                        text: option.text,
                      }))}
                      correctAnswer={question.options.find((opt) => opt.isCorrect)?.optionLetter}
                    />
                  </div>
                </div>
              </div>
            );
          })}

        {/* Pagination */}
        {!searching && totalPages > 1 && (
          <div className="flex items-center justify-between pt-4">
            <div className="text-muted-foreground text-sm">
              Showing {questions.length} of {totalQuestions} questions
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.max(1, p - 1))}
                disabled={page === 1 || searching}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <span className="text-sm">
                Page {page} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                disabled={page === totalPages || searching}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
