'use client';

import { useState, useEffect, use, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, ArrowLeft, Plus, Trash2, Search } from 'lucide-react';
import { toast } from 'sonner';
import { QuestionCard } from '@/components/question-card';

interface Question {
  id: string;
  number: number;
  stem: string;
  associatedText: string | null;
  options: Array<{
    id: string;
    optionLetter: string;
    text: string;
    isCorrect: boolean;
  }>;
  exam: {
    name: string;
    examBoard: string | null;
    year: number | null;
  };
}

interface DeckQuestion {
  deckCardId: string;
  addedAt: string;
  question: Question;
}

interface Deck {
  id: string;
  title: string;
  description: string | null;
  isActive: boolean;
}

export default function DeckDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const { id } = use(params);
  const [deck, setDeck] = useState<Deck | null>(null);
  const [questions, setQuestions] = useState<DeckQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editedDeck, setEditedDeck] = useState<Deck | null>(null);
  const [selectedQuestions, setSelectedQuestions] = useState<Set<string>>(new Set());

  const fetchDeckData = useCallback(async () => {
    try {
      const response = await fetch(`/api/decks/${id}/questions`);

      if (!response.ok) {
        throw new Error('Failed to fetch deck data');
      }

      const data = await response.json();

      setDeck(data.deck);
      setEditedDeck(data.deck);
      setQuestions(data.questions);
    } catch {
      toast.error('Failed to load deck');
      router.push('/decks');
    } finally {
      setLoading(false);
    }
  }, [id, router]);

  useEffect(() => {
    fetchDeckData();
  }, [fetchDeckData]);

  const saveDeck = async () => {
    if (!editedDeck || !editedDeck.title.trim()) {
      toast.error('Title is required');
      return;
    }

    setSaving(true);
    try {
      const response = await fetch(`/api/decks/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editedDeck),
      });

      if (!response.ok) throw new Error('Failed to update deck');

      const updatedDeck = await response.json();
      setDeck(updatedDeck);
      setEditMode(false);
      toast.success('Deck updated successfully');
    } catch {
      toast.error('Failed to update deck');
    } finally {
      setSaving(false);
    }
  };

  const removeQuestions = async () => {
    if (selectedQuestions.size === 0) return;

    try {
      const questionIds = questions
        .filter((q) => selectedQuestions.has(q.deckCardId))
        .map((q) => q.question.id);

      const response = await fetch(`/api/decks/${id}/questions`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ questionIds }),
      });

      if (!response.ok) throw new Error('Failed to remove questions');

      setQuestions(questions.filter((q) => !selectedQuestions.has(q.deckCardId)));
      setSelectedQuestions(new Set());
      toast.success('Questions removed successfully');
    } catch {
      toast.error('Failed to remove questions');
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!deck) return null;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Button variant="ghost" onClick={() => router.push('/decks')} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Decks
        </Button>

        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                {editMode ? (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Title</Label>
                      <Input
                        id="title"
                        value={editedDeck?.title || ''}
                        onChange={(e) => setEditedDeck({ ...editedDeck!, title: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        value={editedDeck?.description || ''}
                        onChange={(e) =>
                          setEditedDeck({ ...editedDeck!, description: e.target.value })
                        }
                        rows={3}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="active"
                        checked={editedDeck?.isActive || false}
                        onCheckedChange={(checked) =>
                          setEditedDeck({ ...editedDeck!, isActive: checked })
                        }
                      />
                      <Label htmlFor="active">Active</Label>
                    </div>
                  </div>
                ) : (
                  <>
                    <CardTitle className="text-2xl">{deck.title}</CardTitle>
                    {deck.description && (
                      <CardDescription className="mt-2">{deck.description}</CardDescription>
                    )}
                    <div className="mt-4 flex items-center gap-2">
                      <Badge variant={deck.isActive ? 'default' : 'secondary'}>
                        {deck.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      <Badge variant="outline">{questions.length} questions</Badge>
                    </div>
                  </>
                )}
              </div>
              <div className="flex gap-2">
                {editMode ? (
                  <>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setEditMode(false);
                        setEditedDeck(deck);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button size="sm" onClick={saveDeck} disabled={saving}>
                      {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Save
                    </Button>
                  </>
                ) : (
                  <Button size="sm" variant="outline" onClick={() => setEditMode(true)}>
                    Edit
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">Questions</h2>
          <div className="flex gap-2">
            {selectedQuestions.size > 0 && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Remove Selected ({selectedQuestions.size})
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Remove Questions</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to remove {selectedQuestions.size} questions from this
                      deck?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={removeQuestions}>Remove</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
            <Button size="sm" onClick={() => router.push(`/decks/${id}/add-questions`)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Questions
            </Button>
          </div>
        </div>

        {questions.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-16">
              <Search className="text-muted-foreground mb-4 h-12 w-12" />
              <h3 className="mb-2 text-lg font-semibold">No questions yet</h3>
              <p className="text-muted-foreground mb-4 text-center">
                Add questions to this deck to start studying
              </p>
              <Button onClick={() => router.push(`/decks/${id}/add-questions`)}>
                <Plus className="mr-2 h-4 w-4" />
                Browse Questions
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-2">
            {questions.map((item) => (
              <div
                key={item.deckCardId}
                className="bg-card flex items-start gap-2 rounded-lg border p-3"
              >
                <Checkbox
                  checked={selectedQuestions.has(item.deckCardId)}
                  onCheckedChange={(checked) => {
                    const newSelected = new Set(selectedQuestions);
                    if (checked) {
                      newSelected.add(item.deckCardId);
                    } else {
                      newSelected.delete(item.deckCardId);
                    }
                    setSelectedQuestions(newSelected);
                  }}
                />
                <div className="flex-1">
                  <QuestionCard
                    hideNavigation={true}
                    questionNumber={item.question.number}
                    totalQuestions={1}
                    examBoard={item.question.exam.examBoard || undefined}
                    institution={item.question.exam.name}
                    year={item.question.exam.year || undefined}
                    subject="Subject" // Default value, could be enhanced later
                    topic="Topic" // Default value, could be enhanced later
                    questionText={item.question.stem}
                    associatedText={item.question.associatedText}
                    options={item.question.options.map((option) => ({
                      letter: option.optionLetter,
                      text: option.text,
                    }))}
                    correctAnswer={item.question.options.find((opt) => opt.isCorrect)?.optionLetter}
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
