'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import {
  BookOpen,
  TrendingUp,
  Building2,
  Calendar,
  ArrowLeft,
  FileText,
  AlertCircle,
} from 'lucide-react';
import { QuestionCard } from '@/components/question-card';
import { QuestionCardSkeleton } from '@/components/question-card-skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import type { Question } from '@/types/question';

interface ExamBoardDetail {
  id: string;
  name: string;
  fullName: string;
  code?: string;
  totalQuestions: number;
  totalExams: number;
  totalInstitutions: number;
  questionTypes: {
    type: string;
    count: number;
    percentage: number;
  }[];
  topInstitutions: {
    id: string;
    name: string;
    code?: string;
    questionCount: number;
    lastExam: string;
  }[];
  topSubjects: {
    id: string;
    name: string;
    questionCount: number;
    percentage: number;
  }[];
  yearlyStatistics: {
    year: number;
    questionCount: number;
    examCount: number;
  }[];
  recentExams: {
    id: string;
    institution: string;
    institutionId: string;
    institutionCode?: string;
    year: number;
    questionCount: number;
  }[];
}

export default function ExamBoardDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [examBoard, setExamBoard] = useState<ExamBoardDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(false);

  useEffect(() => {
    const fetchExamBoard = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`/api/exam-boards/${params.id}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Banca não encontrada');
          } else {
            throw new Error('Failed to fetch exam board');
          }
          return;
        }

        const data = await response.json();
        setExamBoard(data);
      } catch (error) {
        console.error('Error fetching exam board:', error);
        setError('Erro ao carregar dados da banca');
      } finally {
        setLoading(false);
      }
    };

    fetchExamBoard();
  }, [params.id]);

  const fetchQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const response = await fetch(
        `/api/questions?examBoard=${examBoard?.code || examBoard?.name}&limit=5`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch questions');
      }

      const data = await response.json();
      setQuestions(data.questions || []);
    } catch (error) {
      console.error('Error fetching questions:', error);
    } finally {
      setLoadingQuestions(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Skeleton className="mb-2 h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error || !examBoard) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Alert className="mx-auto mb-4 max-w-md">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error || 'Banca não encontrada'}</AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/exam-boards')}>Voltar para Bancas</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/exam-boards')}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar para Bancas
        </Button>

        <div className="flex items-start justify-between">
          <div>
            <h1 className="mb-2 text-3xl font-bold">
              {examBoard.name} {examBoard.fullName !== examBoard.name && `- ${examBoard.fullName}`}
            </h1>
            <p className="text-muted-foreground max-w-3xl">
              Uma das principais bancas examinadoras do Brasil, responsável por{' '}
              {examBoard.totalExams} concursos e mais de{' '}
              {examBoard.totalQuestions.toLocaleString('pt-BR')} questões em nosso banco de dados.
            </p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <BookOpen className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">
                {examBoard.totalQuestions.toLocaleString('pt-BR')}
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Concursos Realizados</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <FileText className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{examBoard.totalExams}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Instituições Atendidas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Building2 className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{examBoard.totalInstitutions}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Provas Realizadas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <TrendingUp className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{examBoard.totalExams}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="institutions">Instituições</TabsTrigger>
          <TabsTrigger value="subjects">Matérias</TabsTrigger>
          <TabsTrigger value="statistics">Estatísticas</TabsTrigger>
          <TabsTrigger value="questions">Questões</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Question Types */}
          <Card>
            <CardHeader>
              <CardTitle>Tipos de Questão</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {examBoard.questionTypes.map((type) => (
                  <div key={type.type}>
                    <div className="mb-1 flex items-center justify-between">
                      <span className="text-sm">{type.type}</span>
                      <span className="text-sm font-medium">
                        {type.count.toLocaleString('pt-BR')} ({type.percentage}%)
                      </span>
                    </div>
                    <div className="bg-muted h-3 overflow-hidden rounded-full">
                      <div className="bg-primary h-full" style={{ width: `${type.percentage}%` }} />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Exams */}
          <Card>
            <CardHeader>
              <CardTitle>Concursos Recentes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {examBoard.recentExams.map((exam) => (
                  <div
                    key={exam.id}
                    className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-3"
                    onClick={() => router.push(`/institutions/${exam.institutionId}`)}
                  >
                    <div>
                      <p className="font-medium">{exam.institution}</p>
                      <p className="text-muted-foreground text-sm">{exam.year}</p>
                    </div>
                    <Badge variant="secondary">{exam.questionCount} questões</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="institutions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Principais Instituições</CardTitle>
              <CardDescription>
                Instituições com mais questões aplicadas por esta banca
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {examBoard.topInstitutions.map((institution) => (
                  <div
                    key={institution.id}
                    className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-4"
                    onClick={() => router.push(`/institutions/${institution.id}`)}
                  >
                    <div className="flex items-center gap-3">
                      <Building2 className="text-muted-foreground h-5 w-5" />
                      <div>
                        <p className="font-medium">{institution.name}</p>
                        <p className="text-muted-foreground text-sm">
                          Último concurso: {institution.lastExam}
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline">
                      {institution.questionCount.toLocaleString('pt-BR')} questões
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Matérias Mais Cobradas</CardTitle>
              <CardDescription>Distribuição de questões por matéria</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {examBoard.topSubjects.map((subject) => (
                  <div
                    key={subject.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/subjects/${subject.id}`)}
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">{subject.name}</span>
                      <span className="text-muted-foreground text-sm">
                        {subject.questionCount.toLocaleString('pt-BR')} ({subject.percentage}%)
                      </span>
                    </div>
                    <div className="bg-muted h-2 overflow-hidden rounded-full">
                      <div
                        className="bg-primary/60 h-full"
                        style={{ width: `${subject.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Evolução Anual</CardTitle>
              <CardDescription>Número de questões e concursos por ano</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {examBoard.yearlyStatistics.map((stat) => (
                  <div key={stat.year} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Calendar className="text-muted-foreground h-4 w-4" />
                      <span className="font-medium">{stat.year}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      <Badge variant="secondary">
                        {stat.questionCount.toLocaleString('pt-BR')} questões
                      </Badge>
                      <Badge variant="outline">{stat.examCount} concursos</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          {questions.length === 0 && !loadingQuestions ? (
            <Card>
              <CardContent className="py-12 text-center">
                <BookOpen className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">Explore Questões da {examBoard.name}</h3>
                <p className="text-muted-foreground mb-4">
                  Veja exemplos de questões aplicadas por esta banca
                </p>
                <Button onClick={fetchQuestions}>Ver Questões de Exemplo</Button>
              </CardContent>
            </Card>
          ) : loadingQuestions ? (
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <QuestionCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="space-y-6">
              {questions.map((question) => (
                <QuestionCard
                  key={question.id}
                  questionNumber={question.number}
                  examBoard={question.exam.examBoard || undefined}
                  examBoardId={question.exam.examBoardId}
                  institution={question.institution?.name || question.exam.name}
                  institutionId={question.institution?.id}
                  year={question.exam.year || undefined}
                  position={question.position?.name}
                  positionId={question.position?.id}
                  specialization={question.specialization?.name}
                  specializationId={question.specialization?.id}
                  subject={question.subject?.name || ''}
                  subjectId={question.subject?.id}
                  topic={question.topic?.name || ''}
                  topicId={question.topic?.id}
                  questionText={question.stem}
                  associatedText={question.associatedText}
                  options={question.options.map((opt) => ({
                    letter: opt.optionLetter,
                    text: opt.text,
                  }))}
                  correctAnswer={question.options.find((opt) => opt.isCorrect)?.optionLetter}
                  hideNavigation
                  totalQuestions={questions.length}
                  commentCount={question.commentCount}
                />
              ))}
              {questions.length > 0 && (
                <div className="pt-4 text-center">
                  <Button
                    variant="outline"
                    onClick={() =>
                      router.push(`/questions?examBoard=${examBoard.code || examBoard.name}`)
                    }
                  >
                    Ver Mais Questões
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
