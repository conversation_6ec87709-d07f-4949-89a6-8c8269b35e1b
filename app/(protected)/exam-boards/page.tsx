'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { GraduationCap, BookOpen, Users, TrendingUp, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';

interface ExamBoard {
  id: string;
  name: string;
  questionCount: number;
  institutionCount: number;
  yearRange: {
    min: number;
    max: number;
  };
  popularSubjects: string[];
  difficultyAverage: number;
  successRate: number;
}

// Mock data - replace with actual API call
const mockExamBoards: ExamBoard[] = [
  {
    id: 'fgv',
    name: 'FGV',
    questionCount: 15420,
    institutionCount: 87,
    yearRange: { min: 2010, max: 2024 },
    popularSubjects: ['Língua Portuguesa', '<PERSON><PERSON>ito Administrativo', 'Matemática'],
    difficultyAverage: 3.7,
    successRate: 42.5,
  },
  {
    id: 'cespe',
    name: 'CESPE/CEBRASPE',
    questionCount: 28350,
    institutionCount: 156,
    yearRange: { min: 2008, max: 2024 },
    popularSubjects: ['Língua Portuguesa', 'Direito Constitucional', 'Informática'],
    difficultyAverage: 4.1,
    successRate: 38.2,
  },
  {
    id: 'fcc',
    name: 'FCC',
    questionCount: 19800,
    institutionCount: 112,
    yearRange: { min: 2009, max: 2024 },
    popularSubjects: ['Língua Portuguesa', 'Direito Administrativo', 'Raciocínio Lógico'],
    difficultyAverage: 3.5,
    successRate: 45.8,
  },
  {
    id: 'vunesp',
    name: 'VUNESP',
    questionCount: 12600,
    institutionCount: 98,
    yearRange: { min: 2011, max: 2024 },
    popularSubjects: ['Língua Portuguesa', 'Matemática', 'Conhecimentos Gerais'],
    difficultyAverage: 3.3,
    successRate: 48.3,
  },
  {
    id: 'idecan',
    name: 'IDECAN',
    questionCount: 8900,
    institutionCount: 65,
    yearRange: { min: 2012, max: 2024 },
    popularSubjects: ['Língua Portuguesa', 'Direito Administrativo', 'Informática'],
    difficultyAverage: 3.4,
    successRate: 46.7,
  },
  {
    id: 'quadrix',
    name: 'QUADRIX',
    questionCount: 7200,
    institutionCount: 52,
    yearRange: { min: 2013, max: 2024 },
    popularSubjects: ['Língua Portuguesa', 'Legislação', 'Conhecimentos Específicos'],
    difficultyAverage: 3.6,
    successRate: 44.1,
  },
];

export default function ExamBoardsPage() {
  const router = useRouter();
  const [examBoards, setExamBoards] = useState<ExamBoard[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setExamBoards(mockExamBoards);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredExamBoards = examBoards.filter((board) =>
    board.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const totalQuestions = examBoards.reduce((sum, board) => sum + board.questionCount, 0);
  const totalInstitutions = examBoards.reduce((sum, board) => sum + board.institutionCount, 0);
  const avgSuccessRate =
    examBoards.reduce((sum, board) => sum + board.successRate, 0) / examBoards.length;

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Bancas Examinadoras</h1>
          <p className="text-muted-foreground mt-2">
            Explore estatísticas e questões por banca examinadora
          </p>
        </div>

        {/* Loading skeleton for stats */}
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Loading skeleton for grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="mt-2 h-4 w-48" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-5/6" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Bancas Examinadoras</h1>
        <p className="text-muted-foreground mt-2">
          Explore estatísticas e questões por banca examinadora
        </p>
      </div>

      {/* Overall Statistics */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Bancas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{examBoards.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalQuestions.toLocaleString('pt-BR')}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Instituições Atendidas</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalInstitutions}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Taxa de Acerto Média</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgSuccessRate.toFixed(1)}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
          <Input
            placeholder="Buscar banca..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Exam Boards Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredExamBoards.map((board) => (
          <Card
            key={board.id}
            className="cursor-pointer transition-shadow hover:shadow-lg"
            onClick={() => router.push(`/exam-boards/${board.id}`)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle>{board.name}</CardTitle>
                  <CardDescription className="mt-1">
                    {board.yearRange.min} - {board.yearRange.max}
                  </CardDescription>
                </div>
                <Badge variant="secondary">{board.questionCount.toLocaleString('pt-BR')}</Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Statistics */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <BookOpen className="text-muted-foreground h-4 w-4" />
                  <span className="text-muted-foreground">Questões</span>
                </div>
                <div className="font-medium">{board.questionCount.toLocaleString('pt-BR')}</div>

                <div className="flex items-center gap-2">
                  <Users className="text-muted-foreground h-4 w-4" />
                  <span className="text-muted-foreground">Instituições</span>
                </div>
                <div className="font-medium">{board.institutionCount}</div>

                <div className="flex items-center gap-2">
                  <TrendingUp className="text-muted-foreground h-4 w-4" />
                  <span className="text-muted-foreground">Taxa de Acerto</span>
                </div>
                <div className="font-medium">{board.successRate}%</div>

                <div className="flex items-center gap-2">
                  <GraduationCap className="text-muted-foreground h-4 w-4" />
                  <span className="text-muted-foreground">Dificuldade</span>
                </div>
                <div className="font-medium">{board.difficultyAverage.toFixed(1)}/5</div>
              </div>

              {/* Popular Subjects */}
              <div>
                <p className="text-muted-foreground mb-2 text-xs">Matérias Populares</p>
                <div className="flex flex-wrap gap-1">
                  {board.popularSubjects.map((subject) => (
                    <Badge key={subject} variant="outline" className="text-xs">
                      {subject}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredExamBoards.length === 0 && (
        <div className="py-12 text-center">
          <p className="text-muted-foreground">Nenhuma banca encontrada</p>
        </div>
      )}
    </div>
  );
}
