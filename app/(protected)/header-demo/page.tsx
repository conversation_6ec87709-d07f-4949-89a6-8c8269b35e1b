'use client';

import { useState } from 'react';
import {
  BarChart3,
  MessageSquare,
  Settings,
  GraduationCap,
  Building2,
  Calendar,
  Briefcase,
  BookOpen,
  Tag,
  ChevronRight,
} from 'lucide-react';
import { AnimatedBadge } from '@/components/animated-badge';
import { cn } from '@/lib/utils';

// Sample data for demonstration
const sampleData = {
  examBoard: 'FGV',
  institution: 'Tribunal de Contas da União (TCU)',
  year: 2022,
  position: 'Auditor Federal de Controle Externo',
  specialization: 'Controle Externo',
  subject: 'Língua Portuguesa',
  topic: null, // Will show "Sem Classificação"
  commentCount: 5,
};

// Action buttons component (shared across all designs)
function ActionButtons({ commentCount }: { commentCount: number }) {
  const [showStatistics, setShowStatistics] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [showAdminMenu, setShowAdminMenu] = useState(false);

  return (
    <div className="flex items-center gap-1">
      <button
        onClick={() => setShowStatistics(!showStatistics)}
        className={`hover:bg-accent rounded-md p-1.5 transition-all duration-200 sm:p-2 ${
          showStatistics ? 'bg-accent' : ''
        }`}
        title="Estatísticas"
      >
        <BarChart3
          className={`h-4 w-4 transition-all duration-200 sm:h-5 sm:w-5 ${
            showStatistics ? 'text-foreground scale-110' : 'text-muted-foreground scale-100'
          }`}
        />
      </button>
      <button
        onClick={() => setShowComments(!showComments)}
        className={`hover:bg-accent relative rounded-md p-1.5 transition-all duration-200 sm:p-2 ${
          showComments ? 'bg-accent' : ''
        }`}
        title="Discussão"
      >
        <MessageSquare
          className={`h-4 w-4 transition-all duration-200 sm:h-5 sm:w-5 ${
            showComments ? 'text-foreground scale-110' : 'text-muted-foreground scale-100'
          }`}
        />
        {commentCount > 0 && <AnimatedBadge value={commentCount} />}
      </button>
      <button
        onClick={() => setShowAdminMenu(!showAdminMenu)}
        className={`hover:bg-accent rounded-md p-1.5 transition-all duration-200 sm:p-2 ${
          showAdminMenu ? 'bg-accent' : ''
        }`}
        title="Administrar Questão"
      >
        <Settings
          className={`h-4 w-4 transition-all duration-200 sm:h-5 sm:w-5 ${
            showAdminMenu ? 'text-foreground scale-110' : 'text-muted-foreground scale-100'
          }`}
        />
      </button>
    </div>
  );
}

// Design Option 1: Enhanced Current Design (Compact)
function HeaderOption1() {
  return (
    <div className="border-border bg-background overflow-hidden rounded-lg border shadow-lg">
      {/* Progress Bar */}
      <div className="bg-muted h-1">
        <div
          className="h-full bg-black/70 transition-all duration-300 ease-out dark:bg-white/70"
          style={{ width: '60%' }}
        />
      </div>

      {/* Header */}
      <div className="border-border border-b">
        <div className="flex items-center justify-between p-2.5 sm:p-3">
          <div className="flex-1 space-y-1">
            {/* All information in compact rows */}
            <div className="flex items-center gap-1.5 text-[11px] sm:text-xs">
              <GraduationCap className="text-primary h-3 w-3" />
              <span className="text-foreground/85">
                {sampleData.examBoard} • {sampleData.institution} • {sampleData.year}
              </span>
            </div>

            <div className="flex items-center gap-1.5 text-[11px] sm:text-xs">
              <Briefcase className="text-secondary-foreground h-3 w-3" />
              <span className="text-foreground/85">
                {sampleData.position} • {sampleData.specialization}
              </span>
            </div>

            <div className="flex items-center gap-1.5 text-[11px] sm:text-xs">
              <BookOpen className="h-3 w-3 text-green-600 dark:text-green-500" />
              <span className="text-foreground/85">
                <span className="font-medium">{sampleData.subject}</span> •{' '}
                <span className="text-muted-foreground">
                  {sampleData.topic || 'Sem Classificação'}
                </span>
              </span>
            </div>
          </div>
          <ActionButtons commentCount={sampleData.commentCount} />
        </div>
      </div>
    </div>
  );
}

// Design Option 2: Compact Card Layout
function HeaderOption2() {
  return (
    <div className="border-border bg-background overflow-hidden rounded-lg border shadow-lg">
      {/* Progress Bar */}
      <div className="bg-muted h-1.5">
        <div
          className="h-full bg-black/70 transition-all duration-300 ease-out dark:bg-white/70"
          style={{ width: '60%' }}
        />
      </div>

      {/* Header */}
      <div className="border-border border-b">
        <div className="flex flex-col gap-3 p-3 sm:p-4">
          {/* Action buttons on mobile - top right */}
          <div className="flex items-start justify-between md:hidden">
            <div className="flex-1" />
            <ActionButtons commentCount={sampleData.commentCount} />
          </div>

          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex flex-wrap items-start gap-1.5 sm:gap-2">
                {/* Exam Board Badge */}
                <div className="bg-primary/10 text-primary border-primary/20 inline-flex items-center gap-1 rounded-full border px-2 py-1 text-[10px] font-medium sm:gap-1.5 sm:px-3 sm:py-1.5 sm:text-xs">
                  <GraduationCap className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                  {sampleData.examBoard}
                </div>

                {/* Institution Badge */}
                <div className="bg-secondary/10 text-secondary-foreground border-secondary/20 inline-flex items-center gap-1 rounded-full border px-2 py-1 text-[10px] sm:gap-1.5 sm:px-3 sm:py-1.5 sm:text-xs">
                  <Building2 className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                  <span className="max-w-[120px] truncate sm:max-w-none">
                    {sampleData.institution}
                  </span>
                </div>

                {/* Year Badge */}
                <div className="bg-accent text-accent-foreground border-accent inline-flex items-center gap-1 rounded-full border px-2 py-1 text-[10px] font-medium sm:gap-1.5 sm:px-3 sm:py-1.5 sm:text-xs">
                  <Calendar className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                  {sampleData.year}
                </div>

                {/* Divider - hidden on mobile */}
                <div className="bg-border hidden h-4 w-px lg:block" />

                {/* Position Badge */}
                <div className="bg-muted text-foreground border-border inline-flex items-center gap-1 rounded-full border px-2 py-1 text-[10px] sm:gap-1.5 sm:px-3 sm:py-1.5 sm:text-xs">
                  <Briefcase className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                  <span className="max-w-[140px] truncate sm:max-w-none">
                    {sampleData.position}
                  </span>
                </div>

                {/* Specialization Badge */}
                {sampleData.specialization && (
                  <div className="bg-muted/50 text-muted-foreground inline-flex items-center gap-1 rounded-full px-2 py-1 text-[10px] sm:gap-1.5 sm:px-3 sm:py-1.5 sm:text-xs">
                    <Tag className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                    <span className="max-w-[100px] truncate sm:max-w-none">
                      {sampleData.specialization}
                    </span>
                  </div>
                )}

                {/* Subject & Topic on new line */}
                <div className="flex w-full flex-wrap items-center gap-1.5 pt-1 sm:gap-2 sm:pt-2">
                  <div className="inline-flex items-center gap-1 rounded-md border border-green-500/20 bg-green-500/10 px-2 py-1 text-[10px] font-medium text-green-700 sm:gap-1.5 sm:px-3 sm:py-1.5 sm:text-xs dark:text-green-400">
                    <BookOpen className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                    {sampleData.subject}
                  </div>
                  <ChevronRight className="text-muted-foreground h-3 w-3 sm:h-3.5 sm:w-3.5" />
                  <div className="text-muted-foreground inline-flex items-center gap-1 text-[10px] sm:gap-1.5 sm:text-xs">
                    {sampleData.topic || 'Sem Classificação'}
                  </div>
                </div>
              </div>
            </div>
            {/* Action buttons on desktop */}
            <div className="hidden md:block">
              <ActionButtons commentCount={sampleData.commentCount} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Design Option 3: Structured Grid Layout
function HeaderOption3() {
  return (
    <div className="border-border bg-background overflow-hidden rounded-lg border shadow-lg">
      {/* Progress Bar */}
      <div className="bg-muted h-1.5">
        <div
          className="h-full bg-black/70 transition-all duration-300 ease-out dark:bg-white/70"
          style={{ width: '60%' }}
        />
      </div>

      {/* Header */}
      <div className="border-border border-b">
        <div className="flex flex-col gap-3 p-3 sm:p-4">
          {/* Action buttons on mobile - top right */}
          <div className="flex items-start justify-between md:hidden">
            <div className="flex-1" />
            <ActionButtons commentCount={sampleData.commentCount} />
          </div>

          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="grid gap-3 sm:grid-cols-2 sm:gap-4 lg:grid-cols-3">
                {/* Exam Information Card */}
                <div className="bg-muted/30 border-l-primary border-l-4 p-2.5 sm:p-3">
                  <div className="mb-1 flex items-center gap-1.5 sm:mb-1.5 sm:gap-2">
                    <GraduationCap className="text-primary h-3.5 w-3.5 sm:h-4 sm:w-4" />
                    <h4 className="text-muted-foreground text-[10px] font-semibold tracking-wider uppercase sm:text-xs">
                      Concurso
                    </h4>
                  </div>
                  <p className="text-xs font-medium sm:text-sm">{sampleData.examBoard}</p>
                  <p className="text-muted-foreground line-clamp-2 text-[10px] sm:text-xs">
                    {sampleData.institution}
                  </p>
                  <p className="text-foreground/70 mt-0.5 text-[10px] font-medium sm:mt-1 sm:text-xs">
                    {sampleData.year}
                  </p>
                </div>

                {/* Position Information Card */}
                <div className="bg-muted/30 border-l-secondary border-l-4 p-2.5 sm:p-3">
                  <div className="mb-1 flex items-center gap-1.5 sm:mb-1.5 sm:gap-2">
                    <Briefcase className="text-secondary-foreground h-3.5 w-3.5 sm:h-4 sm:w-4" />
                    <h4 className="text-muted-foreground text-[10px] font-semibold tracking-wider uppercase sm:text-xs">
                      Cargo
                    </h4>
                  </div>
                  <p className="line-clamp-2 text-xs font-medium sm:text-sm">
                    {sampleData.position}
                  </p>
                  {sampleData.specialization && (
                    <p className="text-muted-foreground mt-0.5 line-clamp-1 text-[10px] sm:mt-1 sm:text-xs">
                      {sampleData.specialization}
                    </p>
                  )}
                </div>

                {/* Subject Information Card */}
                <div className="bg-muted/30 border-l-4 border-l-green-600 p-2.5 sm:col-span-2 sm:p-3 lg:col-span-1 dark:border-l-green-500">
                  <div className="mb-1 flex items-center gap-1.5 sm:mb-1.5 sm:gap-2">
                    <BookOpen className="h-3.5 w-3.5 text-green-600 sm:h-4 sm:w-4 dark:text-green-500" />
                    <h4 className="text-muted-foreground text-[10px] font-semibold tracking-wider uppercase sm:text-xs">
                      Questão
                    </h4>
                  </div>
                  <p className="text-xs font-medium sm:text-sm">{sampleData.subject}</p>
                  <p className="text-muted-foreground mt-0.5 text-[10px] sm:mt-1 sm:text-xs">
                    {sampleData.topic || 'Sem Classificação'}
                  </p>
                </div>
              </div>
            </div>
            {/* Action buttons on desktop */}
            <div className="ml-4 hidden md:block">
              <ActionButtons commentCount={sampleData.commentCount} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function HeaderDemoPage() {
  const [selectedOption, setSelectedOption] = useState<number | null>(null);

  return (
    <div className="min-h-screen p-4 sm:p-6 lg:p-8">
      <div className="mx-auto max-w-7xl">
        <div className="mb-6 text-center sm:mb-8">
          <h1 className="mb-2 text-2xl font-bold sm:mb-4 sm:text-3xl">
            Question Header Design Options
          </h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Choose your preferred header design for the question cards
          </p>
        </div>

        <div className="space-y-6 sm:space-y-8">
          {/* Option 1 */}
          <div className="space-y-3 sm:space-y-4">
            <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h2 className="text-lg font-semibold sm:text-xl">
                  Option 1: Compact Enhanced Design
                </h2>
                <p className="text-muted-foreground text-xs sm:text-sm">
                  Ultra-compact layout with icons and minimal spacing
                </p>
              </div>
              <button
                onClick={() => setSelectedOption(1)}
                className={cn(
                  'rounded-lg px-3 py-1.5 text-xs font-medium transition-colors sm:px-4 sm:py-2 sm:text-sm',
                  selectedOption === 1
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                )}
              >
                {selectedOption === 1 ? 'Selected' : 'Select This'}
              </button>
            </div>
            <HeaderOption1 />
          </div>

          {/* Option 2 */}
          <div className="space-y-3 sm:space-y-4">
            <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h2 className="text-lg font-semibold sm:text-xl">Option 2: Compact Card Layout</h2>
                <p className="text-muted-foreground text-xs sm:text-sm">
                  Modern pill/badge design with color coding
                </p>
              </div>
              <button
                onClick={() => setSelectedOption(2)}
                className={cn(
                  'rounded-lg px-3 py-1.5 text-xs font-medium transition-colors sm:px-4 sm:py-2 sm:text-sm',
                  selectedOption === 2
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                )}
              >
                {selectedOption === 2 ? 'Selected' : 'Select This'}
              </button>
            </div>
            <HeaderOption2 />
          </div>

          {/* Option 3 */}
          <div className="space-y-3 sm:space-y-4">
            <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h2 className="text-lg font-semibold sm:text-xl">
                  Option 3: Structured Grid Layout
                </h2>
                <p className="text-muted-foreground text-xs sm:text-sm">
                  Card-based grid system with clear sections
                </p>
              </div>
              <button
                onClick={() => setSelectedOption(3)}
                className={cn(
                  'rounded-lg px-3 py-1.5 text-xs font-medium transition-colors sm:px-4 sm:py-2 sm:text-sm',
                  selectedOption === 3
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                )}
              >
                {selectedOption === 3 ? 'Selected' : 'Select This'}
              </button>
            </div>
            <HeaderOption3 />
          </div>
        </div>

        {/* Selection Summary */}
        {selectedOption && (
          <div className="bg-accent/50 border-accent mt-12 rounded-lg border p-6 text-center">
            <p className="text-lg">
              You&apos;ve selected <strong>Option {selectedOption}</strong>
            </p>
            <p className="text-muted-foreground mt-2 text-sm">
              This design will be applied to all question cards in the application
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
