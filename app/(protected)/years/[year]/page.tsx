'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calendar, BookOpen, Building2, GraduationCap, ArrowLeft, AlertCircle } from 'lucide-react';
import { QuestionCard } from '@/components/question-card';
import { Question } from '@/types/question';
import { QuestionCardSkeleton } from '@/components/question-card-skeleton';

interface YearDetail {
  year: number;
  totalQuestions: number;
  totalExams: number;
  totalInstitutions: number;
  monthlyDistribution: {
    month: number;
    questionCount: number;
    examCount: number;
  }[];
  topExamBoards: {
    id: string;
    name: string;
    questionCount: number;
    percentage: number;
  }[];
  topInstitutions: {
    id: string;
    name: string;
    code?: string;
    acronym?: string;
    questionCount: number;
    examCount: number;
  }[];
  topPositions: {
    id: string;
    name: string;
    institution: string;
    questionCount: number;
  }[];
  subjectDistribution: {
    id: string;
    name: string;
    questionCount: number;
    percentage: number;
  }[];
  recentExams: {
    id: string;
    institution: string;
    institutionId: string;
    examBoard: string;
    examBoardId: string;
    position: string;
    positionId: string;
    questionCount: number;
  }[];
}

export default function YearDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [yearData, setYearData] = useState<YearDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loadingQuestions, setLoadingQuestions] = useState(false);

  useEffect(() => {
    const fetchYearData = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(`/api/years/${params.year}`);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Ano não encontrado');
          } else {
            throw new Error('Failed to fetch year data');
          }
          return;
        }

        const data = await response.json();
        setYearData(data);
      } catch (error) {
        console.error('Error fetching year data:', error);
        setError('Erro ao carregar dados do ano');
      } finally {
        setLoading(false);
      }
    };

    fetchYearData();
  }, [params.year]);

  const fetchQuestions = async () => {
    try {
      setLoadingQuestions(true);
      const response = await fetch(`/api/questions?year=${params.year}&limit=5`);

      if (!response.ok) {
        throw new Error('Failed to fetch questions');
      }

      const data = await response.json();
      setQuestions(data.questions || []);
    } catch (error) {
      console.error('Error fetching questions:', error);
    } finally {
      setLoadingQuestions(false);
    }
  };

  const getMonthName = (month: number) => {
    const months = [
      'Jan',
      'Fev',
      'Mar',
      'Abr',
      'Mai',
      'Jun',
      'Jul',
      'Ago',
      'Set',
      'Out',
      'Nov',
      'Dez',
    ];
    return months[month - 1] || '';
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Skeleton className="mb-2 h-8 w-32" />
          <Skeleton className="h-4 w-64" />
        </div>
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (error || !yearData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Alert className="mx-auto mb-4 max-w-md">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error || 'Ano não encontrado'}</AlertDescription>
          </Alert>
          <Button onClick={() => router.push('/years')}>Voltar para Anos</Button>
        </div>
      </div>
    );
  }

  const maxMonthCount = Math.max(...yearData.monthlyDistribution.map((m) => m.questionCount));

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button variant="ghost" size="sm" onClick={() => router.push('/years')} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Voltar para Anos
        </Button>

        <div className="flex items-center gap-3">
          <Calendar className="text-muted-foreground h-8 w-8" />
          <h1 className="text-3xl font-bold">Ano {yearData.year}</h1>
        </div>
        <p className="text-muted-foreground mt-2">
          Estatísticas completas de concursos e questões do ano
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <BookOpen className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">
                {yearData.totalQuestions.toLocaleString('pt-BR')}
              </span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Concursos Realizados</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <GraduationCap className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{yearData.totalExams}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Instituições</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Building2 className="text-muted-foreground h-5 w-5" />
              <span className="text-2xl font-bold">{yearData.totalInstitutions}</span>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Questões/Concurso</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <span className="text-2xl font-bold">
                {yearData.totalExams > 0
                  ? Math.round(yearData.totalQuestions / yearData.totalExams)
                  : 0}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="exams">Principais Concursos</TabsTrigger>
          <TabsTrigger value="institutions">Instituições</TabsTrigger>
          <TabsTrigger value="subjects">Matérias</TabsTrigger>
          <TabsTrigger value="questions">Questões</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Monthly Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Distribuição Mensal</CardTitle>
              <CardDescription>Questões aplicadas ao longo do ano</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex h-48 items-end gap-1">
                  {yearData.monthlyDistribution.map((month) => {
                    const height =
                      maxMonthCount > 0 ? (month.questionCount / maxMonthCount) * 100 : 0;
                    return (
                      <div
                        key={month.month}
                        className="group flex flex-1 flex-col items-center gap-1"
                      >
                        <div className="mb-1 text-xs font-medium opacity-0 transition-opacity group-hover:opacity-100">
                          {month.questionCount.toLocaleString('pt-BR')}
                        </div>
                        <div
                          className="bg-primary/20 hover:bg-primary/30 relative w-full rounded-t transition-colors"
                          style={{ height: `${height}%` }}
                        >
                          <div className="bg-popover text-popover-foreground absolute -top-8 left-1/2 -translate-x-1/2 rounded px-2 py-1 text-xs whitespace-nowrap opacity-0 transition-opacity group-hover:opacity-100">
                            {month.examCount} concursos
                          </div>
                        </div>
                        <span className="text-muted-foreground text-xs">
                          {getMonthName(month.month)}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top Exam Boards */}
          <Card>
            <CardHeader>
              <CardTitle>Principais Bancas</CardTitle>
              <CardDescription>Bancas com mais questões no ano</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {yearData.topExamBoards.map((board) => (
                  <div
                    key={board.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/exam-boards/${board.id}`)}
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">{board.name}</span>
                      <span className="text-muted-foreground text-sm">
                        {board.questionCount.toLocaleString('pt-BR')} ({board.percentage}%)
                      </span>
                    </div>
                    <div className="bg-muted h-2 overflow-hidden rounded-full">
                      <div
                        className="bg-primary/60 h-full"
                        style={{ width: `${board.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="exams" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Concursos Recentes</CardTitle>
              <CardDescription>Principais concursos realizados no ano</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {yearData.recentExams.map((exam) => (
                  <div key={exam.id} className="rounded-lg border p-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <p
                          className="cursor-pointer text-lg font-medium hover:underline"
                          onClick={() => router.push(`/institutions/${exam.institutionId}`)}
                        >
                          {exam.institution}
                        </p>
                        <div className="mt-1 flex items-center gap-2">
                          <Badge
                            variant="secondary"
                            className="hover:bg-accent cursor-pointer"
                            onClick={() => router.push(`/exam-boards/${exam.examBoardId}`)}
                          >
                            {exam.examBoard}
                          </Badge>
                          <span className="text-muted-foreground text-sm">•</span>
                          <span
                            className="text-muted-foreground cursor-pointer text-sm hover:underline"
                            onClick={() => router.push(`/positions/${exam.positionId}`)}
                          >
                            {exam.position}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold">
                          {exam.questionCount.toLocaleString('pt-BR')}
                        </p>
                        <p className="text-muted-foreground text-xs">questões</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Positions */}
          <Card>
            <CardHeader>
              <CardTitle>Cargos com Mais Questões</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {yearData.topPositions.map((position) => (
                  <div
                    key={position.id}
                    className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-3"
                    onClick={() => router.push(`/positions/${position.id}`)}
                  >
                    <div>
                      <p className="font-medium">{position.name}</p>
                      <p className="text-muted-foreground text-sm">{position.institution}</p>
                    </div>
                    <Badge variant="outline">
                      {position.questionCount.toLocaleString('pt-BR')} questões
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="institutions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Instituições com Mais Concursos</CardTitle>
              <CardDescription>Órgãos que realizaram concursos no ano</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {yearData.topInstitutions.map((institution) => (
                  <div
                    key={institution.id}
                    className="hover:bg-accent flex cursor-pointer items-center justify-between rounded-lg border p-4"
                    onClick={() => router.push(`/institutions/${institution.id}`)}
                  >
                    <div className="flex items-center gap-3">
                      <Building2 className="text-muted-foreground h-5 w-5" />
                      <div>
                        <p className="font-medium">
                          {institution.acronym ? `${institution.acronym} - ` : ''}
                          {institution.name}
                        </p>
                        <p className="text-muted-foreground text-sm">
                          {institution.examCount}{' '}
                          {institution.examCount === 1 ? 'concurso' : 'concursos'}
                        </p>
                      </div>
                    </div>
                    <Badge variant="outline">
                      {institution.questionCount.toLocaleString('pt-BR')} questões
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Distribuição por Matéria</CardTitle>
              <CardDescription>Matérias mais cobradas no ano</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {yearData.subjectDistribution.map((subject) => (
                  <div
                    key={subject.id}
                    className="cursor-pointer"
                    onClick={() => router.push(`/subjects/${subject.id}`)}
                  >
                    <div className="mb-2 flex items-center justify-between">
                      <span className="font-medium">{subject.name}</span>
                      <span className="text-muted-foreground text-sm">
                        {subject.questionCount.toLocaleString('pt-BR')} ({subject.percentage}%)
                      </span>
                    </div>
                    <div className="bg-muted h-2 overflow-hidden rounded-full">
                      <div
                        className="bg-primary/60 h-full"
                        style={{ width: `${subject.percentage}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          {questions.length === 0 && !loadingQuestions ? (
            <Card>
              <CardContent className="py-12 text-center">
                <BookOpen className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">Questões de {yearData.year}</h3>
                <p className="text-muted-foreground mb-4">
                  Veja exemplos de questões aplicadas neste ano
                </p>
                <Button onClick={fetchQuestions}>Ver Questões de Exemplo</Button>
              </CardContent>
            </Card>
          ) : loadingQuestions ? (
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <QuestionCardSkeleton key={i} />
              ))}
            </div>
          ) : (
            <div className="space-y-6">
              {questions.map((question) => (
                <QuestionCard
                  key={question.id}
                  questionNumber={question.number}
                  examBoard={question.exam.examBoard || undefined}
                  examBoardId={question.exam.examBoardId}
                  institution={question.institution?.name || question.exam.name}
                  institutionId={question.institution?.id}
                  year={question.exam.year || undefined}
                  position={question.position?.name}
                  positionId={question.position?.id}
                  specialization={question.specialization?.name}
                  specializationId={question.specialization?.id}
                  subject={question.subject?.name || ''}
                  subjectId={question.subject?.id}
                  topic={question.topic?.name || ''}
                  topicId={question.topic?.id}
                  questionText={question.stem}
                  associatedText={question.associatedText}
                  options={question.options.map((opt) => ({
                    letter: opt.optionLetter,
                    text: opt.text,
                  }))}
                  correctAnswer={question.options.find((opt) => opt.isCorrect)?.optionLetter}
                  hideNavigation
                  totalQuestions={questions.length}
                  commentCount={question.commentCount}
                />
              ))}
              {questions.length > 0 && (
                <div className="pt-4 text-center">
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/questions?year=${yearData.year}`)}
                  >
                    Ver Mais Questões
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
