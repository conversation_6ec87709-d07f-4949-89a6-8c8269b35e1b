'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar, BookOpen, TrendingUp, <PERSON><PERSON><PERSON>, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';

interface YearStatistics {
  year: number;
  questionCount: number;
  examCount: number;
  institutionCount: number;
  topExamBoards: {
    name: string;
    count: number;
  }[];
  topInstitutions: {
    name: string;
    count: number;
  }[];
  monthlyDistribution: {
    month: string;
    count: number;
  }[];
  difficultyAverage: number;
  successRate: number;
}

// Mock data - replace with actual API call
const mockYears: YearStatistics[] = [
  {
    year: 2024,
    questionCount: 8950,
    examCount: 125,
    institutionCount: 87,
    topExamBoards: [
      { name: 'CESPE', count: 2850 },
      { name: 'FGV', count: 2100 },
      { name: 'FCC', count: 1950 },
    ],
    topInstitutions: [
      { name: 'TCU', count: 450 },
      { name: 'Receita Federal', count: 380 },
      { name: 'INSS', count: 320 },
    ],
    monthlyDistribution: [
      { month: 'Jan', count: 850 },
      { month: 'Fev', count: 920 },
      { month: 'Mar', count: 1100 },
      { month: 'Abr', count: 980 },
      { month: 'Mai', count: 1050 },
      { month: 'Jun', count: 890 },
      { month: 'Jul', count: 760 },
      { month: 'Ago', count: 820 },
      { month: 'Set', count: 950 },
      { month: 'Out', count: 630 },
    ],
    difficultyAverage: 3.6,
    successRate: 42.8,
  },
  {
    year: 2023,
    questionCount: 15680,
    examCount: 198,
    institutionCount: 112,
    topExamBoards: [
      { name: 'CESPE', count: 4200 },
      { name: 'FCC', count: 3800 },
      { name: 'FGV', count: 3500 },
    ],
    topInstitutions: [
      { name: 'Banco do Brasil', count: 680 },
      { name: 'TCU', count: 520 },
      { name: 'TRF2', count: 480 },
    ],
    monthlyDistribution: [
      { month: 'Jan', count: 1250 },
      { month: 'Fev', count: 1380 },
      { month: 'Mar', count: 1420 },
      { month: 'Abr', count: 1180 },
      { month: 'Mai', count: 1350 },
      { month: 'Jun', count: 1290 },
      { month: 'Jul', count: 1100 },
      { month: 'Ago', count: 1320 },
      { month: 'Set', count: 1450 },
      { month: 'Out', count: 1480 },
      { month: 'Nov', count: 1260 },
      { month: 'Dez', count: 1200 },
    ],
    difficultyAverage: 3.7,
    successRate: 41.5,
  },
  {
    year: 2022,
    questionCount: 14320,
    examCount: 176,
    institutionCount: 98,
    topExamBoards: [
      { name: 'FCC', count: 4100 },
      { name: 'CESPE', count: 3900 },
      { name: 'VUNESP', count: 2800 },
    ],
    topInstitutions: [
      { name: 'INSS', count: 620 },
      { name: 'Receita Federal', count: 580 },
      { name: 'Prefeitura SP', count: 450 },
    ],
    monthlyDistribution: [
      { month: 'Jan', count: 980 },
      { month: 'Fev', count: 1120 },
      { month: 'Mar', count: 1350 },
      { month: 'Abr', count: 1280 },
      { month: 'Mai', count: 1420 },
      { month: 'Jun', count: 1180 },
      { month: 'Jul', count: 920 },
      { month: 'Ago', count: 1150 },
      { month: 'Set', count: 1380 },
      { month: 'Out', count: 1420 },
      { month: 'Nov', count: 1180 },
      { month: 'Dez', count: 940 },
    ],
    difficultyAverage: 3.5,
    successRate: 43.2,
  },
];

export default function YearsPage() {
  const router = useRouter();
  const [years, setYears] = useState<YearStatistics[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setYears(mockYears);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredYears = years.filter((year) => year.year.toString().includes(searchQuery));

  const totalQuestions = years.reduce((sum, year) => sum + year.questionCount, 0);
  const totalExams = years.reduce((sum, year) => sum + year.examCount, 0);
  const avgSuccessRate = years.reduce((sum, year) => sum + year.successRate, 0) / years.length;

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Anos</h1>
          <p className="text-muted-foreground mt-2">Explore questões e estatísticas por ano</p>
        </div>

        {/* Loading skeleton for stats */}
        <div className="mb-8 grid gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-20" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Loading skeleton for grid */}
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Skeleton className="h-24 w-full" />
                  <Skeleton className="h-16 w-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Anos</h1>
        <p className="text-muted-foreground mt-2">Explore questões e estatísticas por ano</p>
      </div>

      {/* Overall Statistics */}
      <div className="mb-8 grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Anos Disponíveis</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{years.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Questões</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalQuestions.toLocaleString('pt-BR')}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total de Concursos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalExams}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Taxa de Acerto Média</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgSuccessRate.toFixed(1)}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
          <Input
            placeholder="Buscar ano..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Years List */}
      <div className="space-y-4">
        {filteredYears.map((yearData) => (
          <Card
            key={yearData.year}
            className="cursor-pointer transition-shadow hover:shadow-lg"
            onClick={() => router.push(`/years/${yearData.year}`)}
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Calendar className="text-muted-foreground h-5 w-5" />
                  <CardTitle>{yearData.year}</CardTitle>
                </div>
                <div className="flex gap-2">
                  <Badge variant="secondary">
                    {yearData.questionCount.toLocaleString('pt-BR')} questões
                  </Badge>
                  <Badge variant="outline">{yearData.examCount} concursos</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Monthly Distribution Chart */}
              <div>
                <p className="text-muted-foreground mb-3 text-sm">Distribuição Mensal</p>
                <div className="flex h-24 items-end gap-1">
                  {yearData.monthlyDistribution.map((month) => {
                    const maxCount = Math.max(...yearData.monthlyDistribution.map((m) => m.count));
                    const height = (month.count / maxCount) * 100;
                    return (
                      <div key={month.month} className="flex flex-1 flex-col items-center gap-1">
                        <div
                          className="bg-primary/20 hover:bg-primary/30 w-full rounded-t transition-colors"
                          style={{ height: `${height}%` }}
                          title={`${month.month}: ${month.count} questões`}
                        />
                        <span className="text-muted-foreground text-[10px]">{month.month}</span>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Statistics Grid */}
              <div className="grid grid-cols-2 gap-4 text-sm md:grid-cols-4">
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-2">
                    <BookOpen className="h-4 w-4" />
                    <span>Questões</span>
                  </div>
                  <div className="font-medium">
                    {yearData.questionCount.toLocaleString('pt-BR')}
                  </div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-2">
                    <BarChart className="h-4 w-4" />
                    <span>Instituições</span>
                  </div>
                  <div className="font-medium">{yearData.institutionCount}</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    <span>Taxa de Acerto</span>
                  </div>
                  <div className="font-medium">{yearData.successRate}%</div>
                </div>
                <div>
                  <div className="text-muted-foreground mb-1 flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>Dificuldade</span>
                  </div>
                  <div className="font-medium">{yearData.difficultyAverage.toFixed(1)}/5</div>
                </div>
              </div>

              {/* Top Exam Boards and Institutions */}
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <p className="text-muted-foreground mb-2 text-xs">Principais Bancas</p>
                  <div className="space-y-1">
                    {yearData.topExamBoards.map((board) => (
                      <div key={board.name} className="flex items-center justify-between">
                        <span className="text-sm">{board.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {board.count.toLocaleString('pt-BR')}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
                <div>
                  <p className="text-muted-foreground mb-2 text-xs">Principais Instituições</p>
                  <div className="space-y-1">
                    {yearData.topInstitutions.map((inst) => (
                      <div key={inst.name} className="flex items-center justify-between">
                        <span className="text-sm">{inst.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {inst.count.toLocaleString('pt-BR')}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredYears.length === 0 && (
        <div className="py-12 text-center">
          <p className="text-muted-foreground">Nenhum ano encontrado</p>
        </div>
      )}
    </div>
  );
}
