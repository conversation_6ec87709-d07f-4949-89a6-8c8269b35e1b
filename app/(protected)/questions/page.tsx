'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2, Search, ChevronLeft, ChevronRight } from 'lucide-react';
import { toast } from 'sonner';
import { debounce } from 'lodash';
import { QuestionCard } from '@/components/question-card';
import { QuestionListSkeleton } from '@/components/question-card-skeleton';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/contexts/auth-context';
import { useComments } from '@/hooks/use-comments';
import { useCommentCounts } from '@/contexts/comment-count-context';
import { Question } from '@/types/question';
import { ModelConfig } from '@/lib/openrouter/client';
import { useSidebarAwarePositioning } from '@/hooks/use-sidebar-aware-positioning';

interface Filters {
  examBoards: string[];
  years: number[];
  subjects: Array<{ id: string; name: string }>;
  exams: Array<{
    id: string;
    name: string;
    examBoard: string | null;
    year: number | null;
  }>;
}

interface FsrsInterval {
  rating: number;
  label: string;
  interval: string;
  emoji: string;
}

export default function QuestionsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { updateCommentCount } = useCommentCounts();
  const { sidebarOffset, contentWidth } = useSidebarAwarePositioning();

  const [questions, setQuestions] = useState<Question[]>([]);
  const [filters, setFilters] = useState<Filters | null>(null);
  const [loading, setLoading] = useState(true);
  const [searching, setSearching] = useState(false);
  const [pageChanging, setPageChanging] = useState(false);

  // Track FSRS intervals for each question
  const [questionIntervals, setQuestionIntervals] = useState<Map<string, FsrsInterval[]>>(
    new Map()
  );
  const [, setLoadingIntervals] = useState<Set<string>>(new Set());
  // Track seeds for each question to ensure consistency
  const [, setQuestionSeeds] = useState<Map<string, string>>(new Map());

  // Active settings for AI models
  const [activeSettings, setActiveSettings] = useState<{
    availableModels?: ModelConfig[];
    maxTokens?: number;
    temperature?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    reasoning?: {
      enabled?: boolean;
      effort?: 'low' | 'medium' | 'high';
      maxTokens?: number;
      exclude?: boolean;
    };
  } | null>(null);

  // Track which question's comments are currently being viewed
  const [activeCommentQuestionId, setActiveCommentQuestionId] = useState<string | null>(null);

  // Comments hook
  const { comments, handleVote, handleReply, handleNewComment, handleDelete, handleEdit } =
    useComments({
      questionId: activeCommentQuestionId,
      currentUserId: user?.id,
    });

  // Search parameters
  const [search, setSearch] = useState('');
  const [examBoard, setExamBoard] = useState('all');
  const [year, setYear] = useState('all');
  const [subjectId, setSubjectId] = useState('all');
  const [examId, setExamId] = useState('all');
  const [sortBy, setSortBy] = useState('number');
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalPages, setTotalPages] = useState(1);
  const [totalQuestions, setTotalQuestions] = useState(0);

  // Additional filters
  const [difficulty, setDifficulty] = useState('all');
  const [answered, setAnswered] = useState('all');
  const [questionType, setQuestionType] = useState('all');

  const fetchQuestions = useCallback(async () => {
    setSearching(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pageSize.toString(),
        sortBy,
        ...(search && { search }),
        ...(examBoard && examBoard !== 'all' && { examBoard }),
        ...(year && year !== 'all' && { year }),
        ...(subjectId && subjectId !== 'all' && { subjectId }),
        ...(examId && examId !== 'all' && { examId }),
      });

      const response = await fetch(`/api/questions?${params}`);
      if (!response.ok) throw new Error('Failed to fetch questions');

      const data = await response.json();
      setQuestions(data.questions);
      setFilters(data.filters);
      setTotalPages(data.pagination.totalPages);
      setTotalQuestions(data.pagination.total);

      // Initialize comment counts in context
      data.questions.forEach((question: Question) => {
        if (question.commentCount !== undefined) {
          updateCommentCount(question.id, question.commentCount);
        }
      });
    } catch {
      toast.error('Failed to load questions');
    } finally {
      setSearching(false);
      setLoading(false);
      setPageChanging(false);
    }
  }, [page, pageSize, search, examBoard, year, subjectId, examId, sortBy, updateCommentCount]);

  const fetchActiveSettings = useCallback(async () => {
    try {
      const response = await fetch('/api/model-settings/active');
      if (response.ok) {
        const settings = await response.json();
        setActiveSettings(settings);
      }
    } catch (error) {
      console.error('Error fetching active settings:', error);
    }
  }, []);

  const debouncedSearch = useCallback((value: string) => {
    const handler = debounce(() => {
      setPageChanging(true);
      setSearch(value);
      setPage(1);
    }, 500);
    handler();
  }, []);

  useEffect(() => {
    fetchQuestions();
    fetchActiveSettings();
  }, [fetchQuestions, fetchActiveSettings]);

  // Keyboard navigation support for pagination
  useEffect(() => {
    if (totalPages <= 1) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle arrow keys if no input/textarea is focused
      const activeElement = document.activeElement;
      if (
        activeElement &&
        (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')
      ) {
        return;
      }

      if (e.key === 'ArrowLeft' && page > 1) {
        e.preventDefault();
        setPageChanging(true);
        setPage(page - 1);
      } else if (e.key === 'ArrowRight' && page < totalPages) {
        e.preventDefault();
        setPageChanging(true);
        setPage(page + 1);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [page, totalPages]);

  // Format interval from days to human-readable format
  const formatInterval = (days: number) => {
    if (days < 0.001) {
      return '< 1 min';
    }
    if (days < 0.04) {
      const minutes = Math.round(days * 24 * 60);
      return `${minutes} min`;
    }
    if (days < 1) {
      const hours = Math.round(days * 24);
      return `${hours}h`;
    }
    if (days < 30) {
      const roundedDays = Math.round(days);
      return roundedDays === 1 ? '1 dia' : `${roundedDays} dias`;
    }
    if (days < 365) {
      const months = Math.round(days / 30);
      return months === 1 ? '1 mês' : `${months} meses`;
    }
    const years = Math.round(days / 365);
    return years === 1 ? '1 ano' : `${years} anos`;
  };

  // Fetch FSRS intervals for a question (without creating a card)
  const fetchQuestionIntervals = useCallback(
    async (questionId: string, selectedAnswer?: string) => {
      setLoadingIntervals((prev) => new Set(prev).add(questionId));

      // Generate seed based on current minute
      const now = new Date();
      const minuteTimestamp = Math.floor(now.getTime() / 60000) * 60000;
      const seed = `${questionId}-${minuteTimestamp}`;
      setQuestionSeeds((prev) => new Map(prev).set(questionId, seed));

      try {
        // Use POST with answer to get intervals and create/update card
        const response = await fetch(`/api/questions/${questionId}/answer`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            answer: selectedAnswer || null,
            seed,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Failed to fetch intervals:', response.status, errorData);
          throw new Error(errorData.error || 'Failed to fetch intervals');
        }

        const data = await response.json();

        // Convert raw scheduling options to formatted intervals
        if (data.schedulingOptions) {
          const formattedIntervals = [
            {
              rating: 1,
              label: 'Errei',
              interval: formatInterval(data.schedulingOptions.again.interval),
              emoji: '😵',
            },
            {
              rating: 2,
              label: 'Difícil',
              interval: formatInterval(data.schedulingOptions.hard.interval),
              emoji: '😓',
            },
            {
              rating: 3,
              label: 'Bom',
              interval: formatInterval(data.schedulingOptions.good.interval),
              emoji: '😊',
            },
            {
              rating: 4,
              label: 'Fácil',
              interval: formatInterval(data.schedulingOptions.easy.interval),
              emoji: '😎',
            },
          ];
          setQuestionIntervals((prev) => new Map(prev).set(questionId, formattedIntervals));
        }
      } catch (error) {
        console.error('Error fetching intervals:', error);
        // Set default intervals on error
        setQuestionIntervals((prev) =>
          new Map(prev).set(questionId, [
            { rating: 1, label: 'Errei', interval: '1m', emoji: '😵' },
            { rating: 2, label: 'Difícil', interval: '10m', emoji: '😓' },
            { rating: 3, label: 'Bom', interval: '1d', emoji: '😊' },
            { rating: 4, label: 'Fácil', interval: '4d', emoji: '😎' },
          ])
        );
      } finally {
        setLoadingIntervals((prev) => {
          const newSet = new Set(prev);
          newSet.delete(questionId);
          return newSet;
        });
      }
    },
    []
  );

  // Handle answer submission
  const handleAnswer = useCallback(async (questionId: string, answer: string) => {
    // Since we already fetched intervals in onBeforeSubmit, we don't need to submit again
    // Just log the answer for tracking
    console.log(`Question ${questionId} answered with: ${answer}`);
  }, []);

  // Handle review rating
  const handleReview = useCallback(async (questionId: string, rating: number) => {
    try {
      const response = await fetch(`/api/questions/${questionId}/answer`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ rating }),
      });

      if (!response.ok) throw new Error('Failed to submit review');

      toast.success('Review submitted');
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('Failed to submit review');
    }
  }, []);

  // Admin functions
  const handleAdminCopyJSON = useCallback(
    (questionId: string) => {
      const question = questions.find((q) => q.id === questionId);
      if (!question) return;

      const jsonData = {
        number: question.number,
        subject: question.subject?.name || '',
        topic: question.topic?.name || null,
        associated_text: question.associatedText || null,
        stem: question.stem,
        options: question.options.map((opt) => ({
          order: opt.optionLetter.charCodeAt(0) - 64, // Convert A,B,C... to 1,2,3...
          text: opt.text,
        })),
        images: [], // TODO: Add images support if needed
        correct_answer_order: question.options.findIndex((opt) => opt.isCorrect) + 1,
        is_null: false,
        change_reason: '',
      };

      navigator.clipboard.writeText(JSON.stringify(jsonData, null, 2));
      toast.success('Question JSON copied to clipboard');
    },
    [questions]
  );

  const handleAdminEdit = useCallback(
    (questionId: string) => {
      router.push(`/admin/questions?id=${questionId}`);
    },
    [router]
  );

  const handleAdminDelete = useCallback(async (questionId: string) => {
    if (!confirm('Tem certeza que deseja excluir esta questão? Esta ação não pode ser desfeita.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/questions/${questionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete question');
      }

      // Remove from local state
      setQuestions((prev) => prev.filter((q) => q.id !== questionId));

      toast.success('Question deleted successfully');
    } catch (error) {
      console.error('Error deleting question:', error);
      toast.error('Failed to delete question');
    }
  }, []);

  // Don't fetch intervals automatically - wait for user to click answer button

  if (loading) {
    return (
      <div className="w-full">
        <div>
          <div className="mx-auto max-w-5xl px-6 pt-6 pb-3">
            <div className="mb-6">
              <h1 className="mb-2 text-3xl font-bold">Navegar Questões</h1>
              <p className="text-muted-foreground">
                Pesquise e filtre questões de concursos públicos
              </p>
            </div>

            {/* Simple Filters Skeleton */}
            <Card className="mb-6">
              <CardHeader>
                <Skeleton className="h-6 w-20" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Skeleton className="h-10 w-full" />
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {[1, 2, 3, 4].map((i) => (
                      <Skeleton key={i} className="h-10 w-full" />
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Questions list skeleton */}
            <QuestionListSkeleton count={3} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div>
        <div className="mx-auto max-w-5xl px-6 pt-6 pb-3">
          <div className="mb-6">
            <h1 className="mb-2 text-3xl font-bold">Navegar Questões</h1>
            <p className="text-muted-foreground">
              Pesquise e filtre questões de concursos públicos
            </p>
          </div>

          {/* Filters at the top */}
          <Card className="mb-0">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Filtrar Questões</CardTitle>
                <div className="text-muted-foreground flex items-center gap-2 text-sm">
                  <span>
                    Mostrando {questions.length} de {totalQuestions} questões
                  </span>
                  {searching && <Loader2 className="h-4 w-4 animate-spin" />}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* First row of filters */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
                  <div className="lg:col-span-2">
                    <Label htmlFor="search" className="text-muted-foreground mb-1 text-xs">
                      Palavras-chave de Pesquisa
                    </Label>
                    <div className="relative">
                      <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                      <Input
                        id="search"
                        placeholder="Pesquisar nas questões..."
                        onChange={(e) => debouncedSearch(e.target.value)}
                        className="pl-9"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="examBoard" className="text-muted-foreground mb-1 text-xs">
                      Banca Examinadora
                    </Label>
                    <Select
                      value={examBoard}
                      onValueChange={(value: string) => {
                        setPageChanging(true);
                        setExamBoard(value);
                        setPage(1);
                      }}
                    >
                      <SelectTrigger id="examBoard">
                        <SelectValue placeholder="Todas as bancas" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todas as bancas</SelectItem>
                        {filters?.examBoards.map((board) => (
                          <SelectItem key={board} value={board}>
                            {board}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="institution" className="text-muted-foreground mb-1 text-xs">
                      Instituição
                    </Label>
                    <Select
                      value={examId}
                      onValueChange={(value: string) => {
                        setPageChanging(true);
                        setExamId(value);
                        setPage(1);
                      }}
                    >
                      <SelectTrigger id="institution">
                        <SelectValue placeholder="Todas as instituições" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todas as instituições</SelectItem>
                        {filters?.exams.map((exam) => (
                          <SelectItem key={exam.id} value={exam.id}>
                            {exam.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="year" className="text-muted-foreground mb-1 text-xs">
                      Ano
                    </Label>
                    <Select
                      value={year}
                      onValueChange={(value: string) => {
                        setPageChanging(true);
                        setYear(value);
                        setPage(1);
                      }}
                    >
                      <SelectTrigger id="year">
                        <SelectValue placeholder="Todos os anos" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todos os anos</SelectItem>
                        {filters?.years.map((y) => (
                          <SelectItem key={y} value={y.toString()}>
                            {y}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Second row of filters */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-6">
                  <div>
                    <Label htmlFor="subject" className="text-muted-foreground mb-1 text-xs">
                      Matéria
                    </Label>
                    <Select
                      value={subjectId}
                      onValueChange={(value: string) => {
                        setPageChanging(true);
                        setSubjectId(value);
                        setPage(1);
                      }}
                    >
                      <SelectTrigger id="subject">
                        <SelectValue placeholder="Todas as matérias" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todas as matérias</SelectItem>
                        {filters?.subjects.map((subject) => (
                          <SelectItem key={subject.id} value={subject.id}>
                            {subject.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="difficulty" className="text-muted-foreground mb-1 text-xs">
                      Dificuldade
                    </Label>
                    <Select
                      value={difficulty}
                      onValueChange={(value: string) => {
                        setPageChanging(true);
                        setDifficulty(value);
                        setPage(1);
                      }}
                    >
                      <SelectTrigger id="difficulty">
                        <SelectValue placeholder="Todos os níveis" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todos os níveis</SelectItem>
                        <SelectItem value="easy">Fácil</SelectItem>
                        <SelectItem value="medium">Médio</SelectItem>
                        <SelectItem value="hard">Difícil</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="status" className="text-muted-foreground mb-1 text-xs">
                      Situação
                    </Label>
                    <Select
                      value={answered}
                      onValueChange={(value: string) => {
                        setPageChanging(true);
                        setAnswered(value);
                        setPage(1);
                      }}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Todas as questões" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todas as questões</SelectItem>
                        <SelectItem value="answered">Respondidas</SelectItem>
                        <SelectItem value="unanswered">Não respondidas</SelectItem>
                        <SelectItem value="correct">Corretas</SelectItem>
                        <SelectItem value="incorrect">Incorretas</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="type" className="text-muted-foreground mb-1 text-xs">
                      Tipo de Questão
                    </Label>
                    <Select
                      value={questionType}
                      onValueChange={(value: string) => {
                        setPageChanging(true);
                        setQuestionType(value);
                        setPage(1);
                      }}
                    >
                      <SelectTrigger id="type">
                        <SelectValue placeholder="Todos os tipos" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todos os tipos</SelectItem>
                        <SelectItem value="multiple">Múltipla escolha</SelectItem>
                        <SelectItem value="truefalse">Verdadeiro/Falso</SelectItem>
                        <SelectItem value="essay">Dissertativa</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="sortBy" className="text-muted-foreground mb-1 text-xs">
                      Ordenar Por
                    </Label>
                    <Select
                      value={sortBy}
                      onValueChange={(value: string) => {
                        setPageChanging(true);
                        setSortBy(value);
                        setPage(1);
                      }}
                    >
                      <SelectTrigger id="sortBy">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="recent">Mais Recentes</SelectItem>
                        <SelectItem value="exam">Por Prova</SelectItem>
                        <SelectItem value="number">Número da Questão</SelectItem>
                        <SelectItem value="difficulty">Por Dificuldade</SelectItem>
                        <SelectItem value="mostAnswered">Mais Respondidas</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="pageSize" className="text-muted-foreground mb-1 text-xs">
                      Por Página
                    </Label>
                    <Select
                      value={pageSize.toString()}
                      onValueChange={(value: string) => {
                        setPageChanging(true);
                        setPageSize(parseInt(value));
                        setPage(1);
                      }}
                    >
                      <SelectTrigger id="pageSize">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10 questões</SelectItem>
                        <SelectItem value="20">20 questões</SelectItem>
                        <SelectItem value="50">50 questões</SelectItem>
                        <SelectItem value="100">100 questões</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Additional filter options */}
              <div className="mt-4 flex flex-wrap items-center gap-4 border-t pt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setPageChanging(true);
                    setSearch('');
                    setExamBoard('all');
                    setYear('all');
                    setSubjectId('all');
                    setExamId('all');
                    setSortBy('number');
                    setPageSize(20);
                    setDifficulty('all');
                    setAnswered('all');
                    setQuestionType('all');
                    setPage(1);
                  }}
                >
                  Limpar Filtros
                </Button>

                <div className="flex items-center gap-2 text-sm">
                  <span className="text-muted-foreground">Filtros rápidos:</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setPageChanging(true);
                      setYear(new Date().getFullYear().toString());
                      setPage(1);
                    }}
                  >
                    Ano Atual
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setPageChanging(true);
                      setSortBy('recent');
                      setPage(1);
                    }}
                  >
                    Mais Recentes
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Questions List */}
        <div className="space-y-0">
          {(searching || pageChanging) && (
            <div className="mx-auto max-w-5xl px-6 pb-3">
              <QuestionListSkeleton count={3} />
            </div>
          )}

          {!searching && !pageChanging && questions.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-16">
                <Search className="text-muted-foreground mb-4 h-12 w-12" />
                <h3 className="mb-2 text-lg font-semibold">Nenhuma questão encontrada</h3>
                <p className="text-muted-foreground text-center">
                  Tente ajustar seus filtros ou termos de pesquisa
                </p>
              </CardContent>
            </Card>
          ) : (
            !searching &&
            !pageChanging && (
              <>
                <div className="space-y-0">
                  {questions.map((question, index) => (
                    <div key={question.id} className="mx-auto max-w-5xl px-6 pb-3">
                      <QuestionCard
                        hideNavigation={true}
                        questionNumber={index + 1}
                        totalQuestions={questions.length}
                        examBoard={question.exam.examBoard || undefined}
                        examBoardId={question.exam.examBoardId}
                        institution={question.institution?.name || question.exam.name}
                        institutionId={question.institution?.id}
                        year={question.exam.year || undefined}
                        position={question.position?.name || undefined}
                        positionId={question.position?.id}
                        specialization={question.specialization?.name || undefined}
                        specializationId={question.specialization?.id}
                        subject={question.subject?.name || 'Sem Classificação'}
                        subjectId={question.subject?.id}
                        topic={question.topic?.name || 'Sem Classificação'}
                        topicId={question.topic?.id}
                        questionText={question.stem}
                        associatedText={question.associatedText}
                        associatedTextReferences={question.associatedTextReferences}
                        options={question.options.map((opt) => ({
                          letter: opt.optionLetter,
                          text: opt.text,
                        }))}
                        correctAnswer={question.options.find((opt) => opt.isCorrect)?.optionLetter}
                        fsrsIntervals={questionIntervals.get(question.id)}
                        onBeforeSubmit={async (selectedAnswer) => {
                          // Fetch intervals when user clicks "Resolver Questão"
                          if (!questionIntervals.has(question.id)) {
                            await fetchQuestionIntervals(question.id, selectedAnswer);
                          }
                          // Set this question as active for comments when submitting
                          setActiveCommentQuestionId(question.id);
                        }}
                        onAnswer={(answer) => {
                          handleAnswer(question.id, answer);
                        }}
                        onReview={(rating) => {
                          handleReview(question.id, rating);
                        }}
                        // Comments props - only show comments for the active question
                        comments={activeCommentQuestionId === question.id ? comments : []}
                        commentCount={question.commentCount}
                        currentUserId={user?.id}
                        onCommentVote={
                          activeCommentQuestionId === question.id ? handleVote : undefined
                        }
                        onCommentReply={
                          activeCommentQuestionId === question.id ? handleReply : undefined
                        }
                        onNewComment={
                          activeCommentQuestionId === question.id ? handleNewComment : undefined
                        }
                        onCommentDelete={
                          activeCommentQuestionId === question.id ? handleDelete : undefined
                        }
                        onCommentEdit={
                          activeCommentQuestionId === question.id ? handleEdit : undefined
                        }
                        onCommentsToggle={(show) => {
                          if (show) {
                            setActiveCommentQuestionId(question.id);
                          }
                        }}
                        // Admin props
                        userRole={user?.role}
                        questionId={question.id}
                        onAdminCopyJSON={handleAdminCopyJSON}
                        onAdminEdit={handleAdminEdit}
                        onAdminDelete={handleAdminDelete}
                        // AI Assistant props
                        showAIAssistant={true}
                        availableModels={activeSettings?.availableModels}
                        maxTokens={activeSettings?.maxTokens}
                        temperature={activeSettings?.temperature}
                        topP={activeSettings?.topP}
                        frequencyPenalty={activeSettings?.frequencyPenalty}
                        presencePenalty={activeSettings?.presencePenalty}
                        reasoning={activeSettings?.reasoning}
                      />
                    </div>
                  ))}
                </div>

                {/* Add margin bottom to account for floating pagination */}
                <div className="h-20" />
              </>
            )
          )}
        </div>
      </div>

      {/* Floating Pagination - positioned relative to content area */}
      {totalPages > 1 && (
        <div 
          className="pointer-events-none fixed bottom-6 z-20"
          style={{
            left: `${sidebarOffset}px`,
            width: contentWidth ? `${contentWidth}px` : '100%',
            transition: 'left 0.3s ease-out, width 0.3s ease-out'
          }}
        >
          <div className="mx-auto max-w-5xl px-6">
            <div className="flex items-center justify-between">
              {/* Previous Button */}
              <button
                onClick={() => {
                  setPageChanging(true);
                  setPage(Math.max(1, page - 1));
                }}
                disabled={page === 1}
                className={`bg-background border-border pointer-events-auto rounded-full border p-3 shadow-lg transition-all duration-300 ${
                  page === 1 ? 'cursor-not-allowed opacity-50' : 'hover:bg-accent hover:scale-110'
                }`}
                aria-label="Página anterior"
              >
                <ChevronLeft className="text-foreground h-5 w-5" />
              </button>

              {/* Page Indicator */}
              <div className="bg-background/90 border-border text-muted-foreground pointer-events-auto rounded-full border px-4 py-2 text-sm shadow-lg backdrop-blur-sm">
                Página {page} de {totalPages}
              </div>

              {/* Next Button */}
              <button
                onClick={() => {
                  setPageChanging(true);
                  setPage(Math.min(totalPages, page + 1));
                }}
                disabled={page === totalPages}
                className={`bg-background border-border pointer-events-auto rounded-full border p-3 shadow-lg transition-all duration-300 ${
                  page === totalPages
                    ? 'cursor-not-allowed opacity-50'
                    : 'hover:bg-accent hover:scale-110'
                }`}
                aria-label="Próxima página"
              >
                <ChevronRight className="text-foreground h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
