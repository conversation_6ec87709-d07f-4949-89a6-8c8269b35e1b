'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/hooks/use-auth';
import {
  Plus,
  Trash2,
  Edit,
  Save,
  X,
  Brain,
  Lightbulb,
  Map,
  HelpCircle,
  BookOpen,
  Globe,
  Sparkles,
  ChevronUp,
  ChevronDown,
  Loader2,
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

// Available icons
const AVAILABLE_ICONS = [
  { value: 'Brain', label: 'Brain', icon: Brain },
  { value: 'Lightbulb', label: 'Lightbulb', icon: Lightbulb },
  { value: 'Map', label: 'Map', icon: Map },
  { value: 'HelpCircle', label: 'Help Circle', icon: HelpCircle },
  { value: 'BookOpen', label: 'Book Open', icon: BookOpen },
  { value: 'Globe', label: 'Globe', icon: Globe },
  { value: 'Sparkles', label: 'Sparkles', icon: Sparkles },
];

interface PromptTemplate {
  id: string;
  name: string;
  description?: string;
  feature: string;
  promptType: string;
  promptContent: string;
  modelName: string;
  isDefault: boolean;
  isSystem: boolean;
  isEnabled: boolean;
  icon?: string;
  displayOrder?: number;
  metadata?: Record<string, unknown>;
}

export default function AdminAIPromptsPage() {
  const { hasRole } = useAuth();
  const [prompts, setPrompts] = useState<PromptTemplate[]>([]);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<Partial<PromptTemplate>>({});
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadPrompts();
  }, []);

  const loadPrompts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/prompts?feature=ai_study_assistant');
      if (response.ok) {
        const data = await response.json();
        setPrompts(data.templates || []);
      } else {
        toast.error('Failed to load prompts');
      }
    } catch (error) {
      console.error('Error loading prompts:', error);
      toast.error('Error loading prompts');
    } finally {
      setLoading(false);
    }
  };

  const savePrompt = async (prompt: Partial<PromptTemplate>, isNew: boolean) => {
    setSaving(true);
    try {
      const url = isNew ? '/api/admin/prompts' : `/api/admin/prompts/${prompt.id}`;

      const method = isNew ? 'POST' : 'PUT';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...prompt,
          feature: 'ai_study_assistant',
          promptType: 'sample_prompts',
        }),
      });

      if (response.ok) {
        toast.success(isNew ? 'Prompt created successfully' : 'Prompt updated successfully');
        await loadPrompts();
        setEditingId(null);
        setEditForm({});
        setIsAddingNew(false);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to save prompt');
      }
    } catch (error) {
      console.error('Error saving prompt:', error);
      toast.error('Error saving prompt');
    } finally {
      setSaving(false);
    }
  };

  const deletePrompt = async (id: string) => {
    if (!confirm('Are you sure you want to delete this prompt?')) return;

    try {
      const response = await fetch(`/api/admin/prompts/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('Prompt deleted successfully');
        await loadPrompts();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to delete prompt');
      }
    } catch (error) {
      console.error('Error deleting prompt:', error);
      toast.error('Error deleting prompt');
    }
  };

  const startEdit = (prompt: PromptTemplate) => {
    setEditingId(prompt.id);
    setEditForm(prompt);
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditForm({});
    if (isAddingNew) {
      setIsAddingNew(false);
    }
  };

  const saveEdit = () => {
    if (!editingId) return;
    savePrompt(editForm, isAddingNew);
  };

  const toggleEnabled = async (prompt: PromptTemplate) => {
    await savePrompt({ ...prompt, isEnabled: !prompt.isEnabled }, false);
  };

  const updateOrder = async (prompt: PromptTemplate, newOrder: number) => {
    await savePrompt({ ...prompt, displayOrder: newOrder }, false);
  };

  const addNewPrompt = () => {
    const newPrompt: Partial<PromptTemplate> = {
      name: '',
      promptContent: '',
      feature: 'ai_study_assistant',
      promptType: 'sample_prompts',
      modelName: 'gemini-2.5-flash',
      isEnabled: true,
      icon: 'Sparkles',
      displayOrder: prompts.length,
    };
    setEditForm(newPrompt);
    setEditingId('new');
    setIsAddingNew(true);
  };

  if (!hasRole('admin')) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card>
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>You need admin privileges to access this page.</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-6xl p-6">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold">AI Study Assistant Prompts</h1>
        <div className="flex gap-2">
          <Link href="/admin/model-settings">
            <Button variant="outline">Model Settings</Button>
          </Link>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage Prompt Suggestions</CardTitle>
          <CardDescription>
            Configure the prompt suggestions that appear in the AI Study Assistant. Drag to reorder,
            toggle to enable/disable, or create custom prompts.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="space-y-4">
              {prompts
                .sort((a, b) => (a.displayOrder ?? 0) - (b.displayOrder ?? 0))
                .map((prompt, index) => {
                  const IconComponent =
                    AVAILABLE_ICONS.find((i) => i.value === prompt.icon)?.icon || Sparkles;
                  const isEditing = editingId === prompt.id;

                  return (
                    <div
                      key={prompt.id}
                      className={`rounded-lg border p-4 ${!prompt.isEnabled ? 'opacity-50' : ''}`}
                    >
                      {isEditing ? (
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor={`icon-${prompt.id}`}>Icon</Label>
                              <Select
                                value={editForm.icon}
                                onValueChange={(value) => setEditForm({ ...editForm, icon: value })}
                              >
                                <SelectTrigger id={`icon-${prompt.id}`}>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  {AVAILABLE_ICONS.map((icon) => {
                                    const Icon = icon.icon;
                                    return (
                                      <SelectItem key={icon.value} value={icon.value}>
                                        <div className="flex items-center gap-2">
                                          <Icon className="h-4 w-4" />
                                          <span>{icon.label}</span>
                                        </div>
                                      </SelectItem>
                                    );
                                  })}
                                </SelectContent>
                              </Select>
                            </div>
                            <div>
                              <Label htmlFor={`label-${prompt.id}`}>Label</Label>
                              <Input
                                id={`label-${prompt.id}`}
                                value={editForm.name || ''}
                                onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                                placeholder="Button label"
                              />
                            </div>
                          </div>
                          <div>
                            <Label htmlFor={`prompt-${prompt.id}`}>Prompt Text</Label>
                            <Textarea
                              id={`prompt-${prompt.id}`}
                              value={editForm.promptContent || ''}
                              onChange={(e) =>
                                setEditForm({ ...editForm, promptContent: e.target.value })
                              }
                              placeholder="Enter the full prompt text..."
                              rows={4}
                            />
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="flex-1">
                              <Label htmlFor={`order-${prompt.id}`}>Display Order</Label>
                              <Input
                                id={`order-${prompt.id}`}
                                type="number"
                                value={editForm.displayOrder || 0}
                                onChange={(e) =>
                                  setEditForm({
                                    ...editForm,
                                    displayOrder: parseInt(e.target.value),
                                  })
                                }
                              />
                            </div>
                            <div className="flex items-center gap-2">
                              <Label htmlFor={`enabled-${prompt.id}`}>Enabled</Label>
                              <Switch
                                id={`enabled-${prompt.id}`}
                                checked={editForm.isEnabled}
                                onCheckedChange={(checked) =>
                                  setEditForm({ ...editForm, isEnabled: checked })
                                }
                              />
                            </div>
                          </div>
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={cancelEdit}
                              disabled={saving}
                            >
                              <X className="mr-1 h-4 w-4" />
                              Cancel
                            </Button>
                            <Button
                              size="sm"
                              onClick={saveEdit}
                              disabled={!editForm.name || !editForm.promptContent || saving}
                            >
                              {saving ? (
                                <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                              ) : (
                                <Save className="mr-1 h-4 w-4" />
                              )}
                              Save
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => updateOrder(prompt, (prompt.displayOrder ?? 0) - 1)}
                                disabled={index === 0}
                              >
                                <ChevronUp className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8"
                                onClick={() => updateOrder(prompt, (prompt.displayOrder ?? 0) + 1)}
                                disabled={index === prompts.length - 1}
                              >
                                <ChevronDown className="h-4 w-4" />
                              </Button>
                            </div>
                            <IconComponent className="h-5 w-5" />
                            <div>
                              <p className="font-medium">{prompt.name}</p>
                              <p className="text-muted-foreground line-clamp-2 text-sm">
                                {prompt.promptContent}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant={prompt.isEnabled ? 'default' : 'secondary'}
                              size="sm"
                              onClick={() => toggleEnabled(prompt)}
                              disabled={prompt.isSystem}
                            >
                              {prompt.isEnabled ? 'Enabled' : 'Disabled'}
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => startEdit(prompt)}
                              disabled={prompt.isSystem}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => deletePrompt(prompt.id)}
                              disabled={prompt.isSystem}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}

              <Button
                variant="outline"
                className="w-full"
                onClick={addNewPrompt}
                disabled={isAddingNew}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Custom Prompt
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Preview</CardTitle>
          <CardDescription>
            This is how the enabled prompts will appear in the AI Study Assistant
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/20 rounded-lg border p-4">
            {/* <p className="text-muted-foreground mb-2 text-xs">
              Escolha uma ajuda de estudo ou faça sua própria pergunta:
            </p> */}
            <div className="flex gap-2 overflow-x-auto pb-1">
              {prompts
                .filter((p) => p.isEnabled)
                .sort((a, b) => (a.displayOrder ?? 0) - (b.displayOrder ?? 0))
                .map((prompt) => {
                  const IconComponent =
                    AVAILABLE_ICONS.find((i) => i.value === prompt.icon)?.icon || Sparkles;
                  return (
                    <Button
                      key={prompt.id}
                      variant="ghost"
                      size="sm"
                      className="h-8 flex-shrink-0 gap-1.5 px-3 py-1 whitespace-nowrap"
                      disabled
                    >
                      <IconComponent className="h-3.5 w-3.5 shrink-0" />
                      <span className="text-xs">{prompt.name}</span>
                    </Button>
                  );
                })}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
