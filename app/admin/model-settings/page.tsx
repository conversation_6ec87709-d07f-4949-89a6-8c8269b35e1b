'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/use-auth';
import { Plus, Trash2 } from 'lucide-react';
import { ModelConfig } from '@/lib/openrouter/client';
import { toast } from 'sonner';
import Link from 'next/link';

// Default models for initial setup
const INITIAL_MODELS: ModelConfig[] = [
  { value: 'openai/gpt-4o-mini', label: 'GPT-4o Mini', category: 'OpenAI', enabled: true },
  {
    value: 'anthropic/claude-3.5-haiku',
    label: 'Claude 3.5 Haiku',
    category: 'Anthropic',
    enabled: true,
  },
  {
    value: 'google/gemini-2.5-flash-lite',
    label: 'Gemini 2.5 Flash Lite',
    category: 'Google',
    enabled: true,
  },
  { value: 'openai/gpt-4o', label: 'GPT-4o', category: 'OpenAI', enabled: false },
  {
    value: 'anthropic/claude-3.5-sonnet',
    label: 'Claude 3.5 Sonnet',
    category: 'Anthropic',
    enabled: false,
  },
];

export default function AdminModelSettingsPage() {
  const { hasRole } = useAuth();
  const [loading, setLoading] = useState(false);
  const [hasActiveSettings, setHasActiveSettings] = useState(false);
  const [currentSettingsId, setCurrentSettingsId] = useState<string | null>(null);

  useEffect(() => {
    checkActiveSettings();
  }, []);

  const checkActiveSettings = async () => {
    try {
      const response = await fetch('/api/model-settings/active');
      if (response.ok) {
        const settings = await response.json();
        // Only consider settings active if they exist and have models
        if (settings && settings.id) {
          setCurrentSettingsId(settings.id);
          if (settings.availableModels && settings.availableModels.length > 0) {
            setHasActiveSettings(true);
          } else {
            setHasActiveSettings(false);
          }
        }
      }
    } catch (error) {
      console.error('Error checking active settings:', error);
    }
  };

  const resetSettings = async () => {
    if (!currentSettingsId) return;

    setLoading(true);
    try {
      // Delete current settings
      const deleteResponse = await fetch(`/api/admin/model-settings/${currentSettingsId}`, {
        method: 'DELETE',
      });

      if (deleteResponse.ok) {
        // Clear state
        setCurrentSettingsId(null);
        setHasActiveSettings(false);
        toast.success('Settings cleared. You can now initialize new settings.');
      } else {
        toast.error('Failed to clear settings');
      }
    } catch (error) {
      console.error('Error clearing settings:', error);
      toast.error('Error clearing settings');
    } finally {
      setLoading(false);
    }
  };

  const initializeSettings = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/model-settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: 'Default Settings',
          systemPrompt:
            'You are a helpful AI assistant. You help users with their questions and tasks.',
          defaultModel: 'google/gemini-2.5-flash-lite',
          temperature: 0.7,
          maxTokens: 1000,
          topP: 1,
          frequencyPenalty: 0,
          presencePenalty: 0,
          reasoning: {
            enabled: true,
            effort: 'medium',
            maxTokens: 1000,
          },
          availableModels: INITIAL_MODELS,
          isActive: true,
        }),
      });

      if (response.ok) {
        toast.success('Model settings initialized successfully');
        checkActiveSettings(); // Refresh the state
      } else {
        toast.error('Failed to initialize settings');
      }
    } catch (error) {
      console.error('Error initializing settings:', error);
      toast.error('Error initializing settings');
    } finally {
      setLoading(false);
    }
  };

  if (!hasRole('admin')) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card>
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>You need admin privileges to access this page.</CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-4xl p-6">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Model Settings Administration</h1>
        <Link href="/chat">
          <Button variant="outline">Back to Chat</Button>
        </Link>
      </div>

      {!hasActiveSettings ? (
        <Card>
          <CardHeader>
            <CardTitle>Initialize Model Settings</CardTitle>
            <CardDescription>
              {currentSettingsId
                ? 'Current settings have no enabled models. You can reset and initialize new settings.'
                : 'No active model settings found. Click the button below to initialize with default models.'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {currentSettingsId && (
                <Button
                  onClick={resetSettings}
                  disabled={loading}
                  variant="destructive"
                  size="lg"
                  className="w-full"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  {loading ? 'Resetting...' : 'Reset Current Settings'}
                </Button>
              )}
              <Button onClick={initializeSettings} disabled={loading} size="lg" className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                {loading ? 'Initializing...' : 'Initialize Model Settings'}
              </Button>
              <p className="text-muted-foreground text-sm">
                This will create model settings with{' '}
                {INITIAL_MODELS.filter((m) => m.enabled).length} enabled models including Gemini 2.5
                Flash Lite as the default.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Model Settings Active</CardTitle>
            <CardDescription>
              Model settings are already configured. Use the chat interface settings to manage
              models.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-sm">
              To manage models, system prompts, and parameters:
            </p>
            <ol className="mt-4 list-inside list-decimal space-y-2">
              <li>
                Go to the chat interface at <code>/chat</code>
              </li>
              <li>Click the settings icon (⚙️) in the chat header</li>
              <li>Navigate to the Models tab to manage available models</li>
              <li>Use other tabs to configure prompts and parameters</li>
            </ol>
            <div className="mt-6">
              <Link href="/chat">
                <Button className="w-full">Go to Chat & Configure Models</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>About Model Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="mb-2 font-semibold">How it works:</h3>
            <ul className="text-muted-foreground list-inside list-disc space-y-1 text-sm">
              <li>Only models you enable will be available to users</li>
              <li>You can add custom models with any OpenRouter-compatible model ID</li>
              <li>Set a default model that will be pre-selected for new conversations</li>
              <li>All settings apply globally to all users</li>
            </ul>
          </div>

          <div>
            <h3 className="mb-2 font-semibold">Initial Models:</h3>
            <p className="text-muted-foreground text-sm">
              The system starts with {INITIAL_MODELS.length} pre-configured models from various
              providers including OpenAI, Anthropic, Google, and others. Only a few are enabled by
              default.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
