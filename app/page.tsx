'use client';

import { useRouter } from 'next/navigation';
import { useSession, signIn } from 'next-auth/react';
import { ThemeToggle } from '@/components/theme-toggle';
import { useEffect } from 'react';

export default function Home() {
  const router = useRouter();
  const { data: session, status } = useSession();

  useEffect(() => {
    // If user is already logged in, redirect to dashboard
    if (session && status === 'authenticated') {
      router.push('/dashboard');
    }
  }, [session, status, router]);

  const handleGoogleSignIn = async () => {
    await signIn('google', { callbackUrl: '/dashboard' });
  };

  if (status === 'loading') {
    return (
      <div className="bg-background flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="border-primary mx-auto h-12 w-12 animate-spin rounded-full border-b-2"></div>
          <p className="text-muted-foreground mt-4">Carregando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background relative min-h-screen">
      {/* Theme toggle in top right */}
      <div className="absolute top-4 right-4">
        <ThemeToggle iconOnly />
      </div>

      {/* Main content */}
      <div className="flex min-h-screen flex-col items-center justify-center px-4">
        <div className="mx-auto max-w-md text-center">
          {/* Site title */}
          <h1 className="text-foreground mb-6 text-5xl font-bold tracking-tight">Memo</h1>
          <p className="text-muted-foreground mb-[30px] text-lg">
            Seu aplicativo pessoal de memorização seguro
          </p>

          {/* Google Sign In/Up button */}
          <button
            onClick={handleGoogleSignIn}
            className="border-input bg-background text-foreground hover:bg-accent hover:text-accent-foreground inline-flex items-center justify-center rounded-md border px-8 py-3 text-base font-medium transition-colors"
          >
            <svg className="mr-3 h-5 w-5" viewBox="0 0 24 24">
              <path
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                fill="#4285F4"
              />
              <path
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                fill="#34A853"
              />
              <path
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                fill="#FBBC05"
              />
              <path
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                fill="#EA4335"
              />
            </svg>
            Continuar com Google
          </button>

          {/* Alternative login options */}
          <div className="mt-8 space-y-2">
            <p className="text-muted-foreground text-sm">Ou faça login com email</p>
            <div className="flex justify-center gap-4">
              <a href="/auth/signin" className="text-primary text-sm font-medium hover:underline">
                Entrar
              </a>
              <span className="text-muted-foreground">•</span>
              <a href="/auth/signup" className="text-primary text-sm font-medium hover:underline">
                Criar conta
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
