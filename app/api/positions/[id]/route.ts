import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db/client';
import {
  positions,
  specializations,
  civilServiceExams,
  civilServiceTests,
  institutions,
  examBoards,
  questions,
  subjects,
} from '@/db/schema';
import { eq, sql, desc, and } from 'drizzle-orm';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = await params;

  try {
    // Get position details
    const positionResult = await db
      .select({
        id: positions.id,
        name: positions.name,
        code: positions.code,
      })
      .from(positions)
      .where(eq(positions.id, id))
      .limit(1);

    if (positionResult.length === 0) {
      return NextResponse.json({ error: 'Position not found' }, { status: 404 });
    }

    const position = positionResult[0];

    // Get statistics
    const [stats] = await db
      .select({
        totalQuestions: sql<number>`count(distinct ${questions.id})`,
        totalExams: sql<number>`count(distinct ${civilServiceExams.id})`,
        totalInstitutions: sql<number>`count(distinct ${institutions.id})`,
        totalSpecializations: sql<number>`count(distinct ${specializations.id})`,
      })
      .from(positions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.positionId, positions.id))
      .leftJoin(specializations, eq(civilServiceExams.specializationId, specializations.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .where(eq(positions.id, id));

    // Get specializations for this position
    const specializationsList = await db
      .select({
        id: specializations.id,
        name: specializations.name,
        code: specializations.code,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
      })
      .from(specializations)
      .innerJoin(civilServiceExams, eq(civilServiceExams.specializationId, specializations.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(specializations.positionId, id))
      .groupBy(specializations.id, specializations.name, specializations.code)
      .orderBy(desc(sql`count(distinct ${questions.id})`));

    // Get top institutions that hire for this position
    const topInstitutions = await db
      .select({
        id: institutions.id,
        name: institutions.name,
        code: institutions.code,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        lastExam: sql<number>`max(${civilServiceExams.year})`,
      })
      .from(positions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(positions.id, id))
      .groupBy(institutions.id, institutions.name, institutions.code)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Get subject distribution
    const subjectDistribution = await db
      .select({
        id: subjects.id,
        name: subjects.name,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(positions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(subjects, eq(questions.subjectId, subjects.id))
      .where(eq(positions.id, id))
      .groupBy(subjects.id, subjects.name)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    const totalQuestionsForSubjects = subjectDistribution.reduce(
      (sum, subject) => sum + subject.questionCount,
      0
    );
    const subjectDistributionWithPercentage = subjectDistribution.map((subject) => ({
      ...subject,
      percentage:
        totalQuestionsForSubjects > 0
          ? Math.round((subject.questionCount / totalQuestionsForSubjects) * 1000) / 10
          : 0,
    }));

    // Get exam history
    const currentYear = new Date().getFullYear();
    const examHistory = await db
      .select({
        year: civilServiceExams.year,
        institutionId: institutions.id,
        institutionName: institutions.name,
        institutionCode: institutions.code,
        examBoardId: examBoards.id,
        examBoardName: examBoards.name,
        examBoardCode: examBoards.code,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(positions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(and(eq(positions.id, id), sql`${civilServiceExams.year} >= ${currentYear - 5}`))
      .groupBy(
        civilServiceExams.year,
        institutions.id,
        institutions.name,
        institutions.code,
        examBoards.id,
        examBoards.name,
        examBoards.code
      )
      .orderBy(desc(civilServiceExams.year), desc(sql`count(distinct ${questions.id})`))
      .limit(20);

    // Get competition trend (yearly statistics)
    const competitionTrend = await db
      .select({
        year: civilServiceExams.year,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(positions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(and(eq(positions.id, id), sql`${civilServiceExams.year} >= ${currentYear - 5}`))
      .groupBy(civilServiceExams.year)
      .orderBy(desc(civilServiceExams.year));

    // Format response
    const response = {
      id: position.id,
      name: position.name,
      code: position.code,
      totalQuestions: stats?.totalQuestions || 0,
      totalExams: stats?.totalExams || 0,
      totalInstitutions: stats?.totalInstitutions || 0,
      specializations: specializationsList.map((spec) => ({
        id: spec.id,
        name: spec.name,
        code: spec.code,
        questionCount: spec.questionCount,
        examCount: spec.examCount,
      })),
      topInstitutions: topInstitutions.map((inst) => ({
        id: inst.id,
        name: inst.name,
        code: inst.code,
        acronym: inst.code,
        questionCount: inst.questionCount,
        lastExam: inst.lastExam.toString(),
      })),
      subjectDistribution: subjectDistributionWithPercentage,
      examHistory: examHistory.map((exam) => ({
        year: exam.year,
        institution: exam.institutionName,
        institutionCode: exam.institutionCode,
        examBoard: exam.examBoardCode || exam.examBoardName,
        questionCount: exam.questionCount,
      })),
      competitionTrend: competitionTrend.map((trend) => ({
        year: trend.year,
        questionCount: trend.questionCount,
        examCount: trend.examCount,
      })),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching position:', error);
    return NextResponse.json({ error: 'Failed to fetch position' }, { status: 500 });
  }
}
