import { NextRequest, NextResponse } from 'next/server';
import { requireRole, assignRole, getUserRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { z } from 'zod';

const updateRoleSchema = z.object({
  role: z.enum([
    ROLES.STUDENT_FREE,
    ROLES.STUDENT_PREMIUM,
    ROLES.TEACHER,
    ROLES.CONTENT_CURATOR,
    ROLES.ADMIN,
    ROLES.SUPPORT,
  ] as const),
});

// Remove this interface and use inline type as Next.js 15 expects

/**
 * GET /api/admin/users/[userId]/role
 * Get user's current role
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ userId: string }> }) {
  // Only admins can view user roles
  const roleCheck = await requireRole(req, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const { userId } = await params;

  try {
    const role = await getUserRole(userId);

    if (!role) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ userId, role });
  } catch (error) {
    console.error('Error fetching user role:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PUT /api/admin/users/[userId]/role
 * Update user's role
 */
export async function PUT(req: NextRequest, { params }: { params: Promise<{ userId: string }> }) {
  // Only admins can update user roles
  const roleCheck = await requireRole(req, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const { userId } = await params;

  try {
    const body = await req.json();
    const validatedData = updateRoleSchema.parse(body);

    await assignRole(userId, validatedData.role);

    return NextResponse.json({
      message: 'Role updated successfully',
      userId,
      role: validatedData.role,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input', details: error.issues }, { status: 400 });
    }

    console.error('Error updating user role:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
