import { NextRequest, NextResponse } from 'next/server';
import { requireRole, grantFeature, revokeFeature } from '@/lib/auth/rbac';
import { ROLES, FEATURES, Feature } from '@/lib/auth/features';
import { auth } from '@/auth';
import { db } from '@/db';
import { userFeatures } from '@/db/schema';
import { eq, and, or, isNull, gte } from 'drizzle-orm';
import { z } from 'zod';

const grantFeatureSchema = z.object({
  feature: z.string(),
  expiresAt: z.string().datetime().optional(),
});

// Remove this interface and use inline type as Next.js 15 expects

/**
 * GET /api/admin/users/[userId]/features
 * Get user's current features
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ userId: string }> }) {
  // Only admins can view user features
  const roleCheck = await requireRole(req, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const { userId } = await params;

  try {
    const now = new Date();
    const features = await db
      .select()
      .from(userFeatures)
      .where(
        and(
          eq(userFeatures.userId, userId),
          or(isNull(userFeatures.expiresAt), gte(userFeatures.expiresAt, now))
        )
      );

    return NextResponse.json({ userId, features });
  } catch (error) {
    console.error('Error fetching user features:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/admin/users/[userId]/features
 * Grant a feature to a user
 */
export async function POST(req: NextRequest, { params }: { params: Promise<{ userId: string }> }) {
  // Only admins can grant features
  const roleCheck = await requireRole(req, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const session = await auth();
  const { userId } = await params;

  try {
    const body = await req.json();
    const validatedData = grantFeatureSchema.parse(body);

    // Validate feature exists
    if (!Object.values(FEATURES).includes(validatedData.feature as Feature)) {
      return NextResponse.json(
        { error: 'Invalid feature', availableFeatures: Object.keys(FEATURES) },
        { status: 400 }
      );
    }

    const expiresAt = validatedData.expiresAt ? new Date(validatedData.expiresAt) : undefined;

    await grantFeature(userId, validatedData.feature as Feature, session!.user.id, expiresAt);

    return NextResponse.json({
      message: 'Feature granted successfully',
      userId,
      feature: validatedData.feature,
      expiresAt,
      grantedBy: session!.user.email,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input', details: error.issues }, { status: 400 });
    }

    console.error('Error granting feature:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE /api/admin/users/[userId]/features/[feature]
 * Revoke a feature from a user
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  // Only admins can revoke features
  const roleCheck = await requireRole(req, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const { userId } = await params;
  const url = new URL(req.url);
  const feature = url.pathname.split('/').pop();

  if (!feature || !Object.values(FEATURES).includes(feature as Feature)) {
    return NextResponse.json({ error: 'Invalid feature' }, { status: 400 });
  }

  try {
    await revokeFeature(userId, feature as Feature);

    return NextResponse.json({
      message: 'Feature revoked successfully',
      userId,
      feature,
    });
  } catch (error) {
    console.error('Error revoking feature:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
