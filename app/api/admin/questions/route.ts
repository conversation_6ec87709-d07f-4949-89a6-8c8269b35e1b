import { auth } from '@/auth';
import { db } from '@/db';
import { questions } from '@/db/schema/questions';
import {
  civilServiceTests,
  civilServiceExams,
  examBoards,
  institutions,
  subjects,
  topics,
  positions,
  specializations,
} from '@/db/schema/exams';
import { questionOptions, questionKeys } from '@/db/schema/questions';
import { questionAssociatedTexts } from '@/db/schema/question-texts';
import { and, eq, like, or, sql, asc, inArray } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';
// import { addOptionLettersWithCorrect } from '@/lib/utils/option-letters';

// GET /api/admin/questions - Get all questions for admin management
export async function GET(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
  }

  // Check if user is admin
  if (session.user.role !== 'admin') {
    return NextResponse.json({ error: 'Acesso de administrador obrigatório' }, { status: 403 });
  }

  try {
    const searchParams = request.nextUrl.searchParams;

    // Pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Filters
    const search = searchParams.get('search') || '';
    const examBoardFilter = searchParams.get('examBoard');
    const yearFilter = searchParams.get('year');
    const subjectId = searchParams.get('subjectId');

    // Build where conditions
    const conditions = [];

    if (search) {
      conditions.push(
        or(
          like(questions.stem, `%${search}%`),
          like(questionAssociatedTexts.content, `%${search}%`),
          like(institutions.name, `%${search}%`),
          sql`${questions.number}::text LIKE ${'%' + search + '%'}`
        )
      );
    }

    if (examBoardFilter) {
      conditions.push(
        or(eq(examBoards.code, examBoardFilter), eq(examBoards.name, examBoardFilter))
      );
    }

    if (yearFilter) {
      conditions.push(eq(civilServiceExams.year, parseInt(yearFilter)));
    }

    if (subjectId && subjectId !== 'all') {
      conditions.push(eq(questions.subjectId, subjectId));
    }

    // Get total count
    const [{ count }] = await db
      .select({ count: sql<number>`count(*)` })
      .from(questions)
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .leftJoin(specializations, eq(civilServiceExams.specializationId, specializations.id))
      .leftJoin(subjects, eq(questions.subjectId, subjects.id))
      .leftJoin(topics, eq(questions.topicId, topics.id))
      .leftJoin(questionAssociatedTexts, eq(questions.associatedTextId, questionAssociatedTexts.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    // Get questions with exam details
    const questionList = await db
      .select({
        question: questions,
        test: civilServiceTests,
        exam: civilServiceExams,
        examBoard: examBoards,
        institution: institutions,
        position: positions,
        specialization: specializations,
        subject: subjects,
        topic: topics,
        associatedText: questionAssociatedTexts,
      })
      .from(questions)
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .leftJoin(specializations, eq(civilServiceExams.specializationId, specializations.id))
      .leftJoin(subjects, eq(questions.subjectId, subjects.id))
      .leftJoin(topics, eq(questions.topicId, topics.id))
      .leftJoin(questionAssociatedTexts, eq(questions.associatedTextId, questionAssociatedTexts.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(asc(questions.number))
      .limit(limit)
      .offset(offset);

    // Get options and keys for all questions
    const questionIds = questionList.map((q) => q.question.id);
    const [options, keys] =
      questionIds.length > 0
        ? await Promise.all([
            db
              .select()
              .from(questionOptions)
              .where(inArray(questionOptions.questionId, questionIds)),
            db.select().from(questionKeys).where(inArray(questionKeys.questionId, questionIds)),
          ])
        : [[], []];

    // Group options by question
    const optionsByQuestion = options.reduce(
      (acc, opt) => {
        if (!acc[opt.questionId]) acc[opt.questionId] = [];
        acc[opt.questionId].push(opt);
        return acc;
      },
      {} as Record<string, typeof options>
    );

    // Create a map of correct answers and key info by question
    const keysByQuestion = keys.reduce(
      (acc, key) => {
        acc[key.questionId] = key;
        return acc;
      },
      {} as Record<string, (typeof keys)[0]>
    );

    // Format response for admin use
    const questionsWithOptions = questionList.map(
      ({
        question,
        exam,
        examBoard,
        institution,
        position,
        specialization,
        subject,
        topic,
        associatedText,
      }) => {
        const questionOptions = optionsByQuestion[question.id] || [];
        const keyInfo = keysByQuestion[question.id];

        // Find correct answer order
        const correctAnswerOrder = keyInfo?.correctAnswerId
          ? questionOptions.find((opt) => opt.id === keyInfo.correctAnswerId)?.order || null
          : null;

        return {
          id: question.id,
          number: question.number,
          subject: subject?.name || '',
          topic: topic?.name || null,
          associated_text: associatedText?.content || null,
          associated_text_references: question.associatedTextReferences || null,
          stem: question.stem,
          options: questionOptions
            .sort((a, b) => a.order - b.order)
            .map((opt) => ({
              order: opt.order,
              text: opt.text,
            })),
          images: [], // TODO: Add images support if needed
          correct_answer_order: correctAnswerOrder,
          is_null: keyInfo?.isNull || false,
          change_reason: keyInfo?.changeReason || '',
          // Additional metadata for admin
          exam_info: {
            exam_board: examBoard.code || examBoard.name,
            institution: institution.name,
            year: exam.year,
            position: position?.name,
            specialization: specialization?.name,
          },
          created_at: question.createdAt,
          updated_at: question.updatedAt,
        };
      }
    );

    return NextResponse.json({
      questions: questionsWithOptions,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching admin questions:', error);
    return NextResponse.json({ error: 'Falha ao buscar questões' }, { status: 500 });
  }
}
