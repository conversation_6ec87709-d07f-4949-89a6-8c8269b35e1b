import { auth } from '@/auth';
import { db } from '@/db';
import { questions } from '@/db/schema/questions';
import { questionOptions, questionKeys } from '@/db/schema/questions';
import { questionAssociatedTexts } from '@/db/schema/question-texts';
import { subjects, topics } from '@/db/schema/exams';
import { eq } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (session.user.role !== 'admin') {
    return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
  }

  try {
    const { id: questionId } = await params;

    // Get question with related data
    const [question] = await db
      .select({
        question: questions,
        subject: subjects,
        topic: topics,
        associatedText: questionAssociatedTexts,
      })
      .from(questions)
      .leftJoin(subjects, eq(questions.subjectId, subjects.id))
      .leftJoin(topics, eq(questions.topicId, topics.id))
      .leftJoin(questionAssociatedTexts, eq(questions.associatedTextId, questionAssociatedTexts.id))
      .where(eq(questions.id, questionId))
      .limit(1);

    if (!question) {
      return NextResponse.json({ error: 'Question not found' }, { status: 404 });
    }

    // Get options and answer key
    const [options, keys] = await Promise.all([
      db
        .select()
        .from(questionOptions)
        .where(eq(questionOptions.questionId, questionId))
        .orderBy(questionOptions.order),
      db.select().from(questionKeys).where(eq(questionKeys.questionId, questionId)).limit(1),
    ]);

    const keyInfo = keys[0];
    const correctAnswerOrder = keyInfo?.correctAnswerId
      ? options.find((opt) => opt.id === keyInfo.correctAnswerId)?.order || null
      : null;

    const response = {
      id: question.question.id,
      number: question.question.number,
      subject: question.subject?.name || '',
      topic: question.topic?.name || null,
      associated_text: question.associatedText?.content || null,
      associated_text_references: question.question.associatedTextReferences || null,
      stem: question.question.stem,
      options: options.map((opt) => ({
        order: opt.order,
        text: opt.text,
      })),
      images: [], // TODO: Add images support if needed
      correct_answer_order: correctAnswerOrder,
      is_null: keyInfo?.isNull || false,
      change_reason: keyInfo?.changeReason || '',
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching question:', error);
    return NextResponse.json({ error: 'Failed to fetch question' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (session.user.role !== 'admin') {
    return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
  }

  try {
    const { id: questionId } = await params;
    const body = await request.json();

    const {
      number,
      subject,
      topic,
      // associated_text_id, // TODO: Handle associated text updates
      stem,
      options,
      correct_answer_order,
      is_null,
      change_reason,
    } = body;

    // Validate required fields
    if (!stem || !options || !Array.isArray(options) || options.length === 0) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Validate correct answer order
    if (
      correct_answer_order &&
      (correct_answer_order < 1 || correct_answer_order > options.length)
    ) {
      return NextResponse.json({ error: 'Invalid correct answer order' }, { status: 400 });
    }

    // Start transaction
    await db.transaction(async (tx) => {
      // Get or create subject
      let subjectId = null;
      if (subject) {
        const [existingSubject] = await tx
          .select({ id: subjects.id })
          .from(subjects)
          .where(eq(subjects.name, subject))
          .limit(1);

        if (existingSubject) {
          subjectId = existingSubject.id;
        } else {
          const [newSubject] = await tx
            .insert(subjects)
            .values({ name: subject })
            .returning({ id: subjects.id });
          subjectId = newSubject.id;
        }
      }

      // Get or create topic
      let topicId = null;
      if (topic) {
        const [existingTopic] = await tx
          .select({ id: topics.id })
          .from(topics)
          .where(eq(topics.name, topic))
          .limit(1);

        if (existingTopic) {
          topicId = existingTopic.id;
        } else {
          const [newTopic] = await tx
            .insert(topics)
            .values({ name: topic })
            .returning({ id: topics.id });
          topicId = newTopic.id;
        }
      }

      // Update question
      await tx
        .update(questions)
        .set({
          number,
          stem,
          // TODO: Handle associated text updates separately
          subjectId,
          topicId,
          // Note: isNull and changeReason are in questionKeys table, not questions table
          updatedAt: new Date(),
        })
        .where(eq(questions.id, questionId));

      // Delete existing options
      await tx.delete(questionOptions).where(eq(questionOptions.questionId, questionId));

      // Insert new options
      const newOptions = await tx
        .insert(questionOptions)
        .values(
          options.map((option: { order: number; text: string }) => ({
            questionId,
            order: option.order,
            text: option.text,
          }))
        )
        .returning({ id: questionOptions.id, order: questionOptions.order });

      // Update answer key if provided
      if (correct_answer_order) {
        const correctOption = newOptions.find((opt) => opt.order === correct_answer_order);
        if (correctOption) {
          // Delete existing answer key
          await tx.delete(questionKeys).where(eq(questionKeys.questionId, questionId));

          // Insert new answer key
          await tx.insert(questionKeys).values({
            questionId,
            correctAnswerId: correctOption.id,
            type: 'MULTIPLE_CHOICE',
            isNull: is_null || false,
            changeReason: change_reason || '',
          });
        }
      } else {
        // Delete existing answer key
        await tx.delete(questionKeys).where(eq(questionKeys.questionId, questionId));

        // Insert new answer key with null answer (for is_null and change_reason)
        if (is_null || change_reason) {
          await tx.insert(questionKeys).values({
            questionId,
            correctAnswerId: null,
            type: 'MULTIPLE_CHOICE',
            isNull: is_null || false,
            changeReason: change_reason || '',
          });
        }
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating question:', error);
    return NextResponse.json({ error: 'Failed to update question' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  if (session.user.role !== 'admin') {
    return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
  }

  try {
    const { id: questionId } = await params;

    // Start transaction to delete question and related data
    await db.transaction(async (tx) => {
      // Delete options
      await tx.delete(questionOptions).where(eq(questionOptions.questionId, questionId));

      // Delete answer keys
      await tx.delete(questionKeys).where(eq(questionKeys.questionId, questionId));

      // Delete question
      await tx.delete(questions).where(eq(questions.id, questionId));
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting question:', error);
    return NextResponse.json({ error: 'Failed to delete question' }, { status: 500 });
  }
}
