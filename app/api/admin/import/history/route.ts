import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { db } from '@/db/client';
import { ocrProcessingHistory } from '@/db/schema';
import { eq, desc } from 'drizzle-orm';
import { parseOCRExtractedData } from '@/lib/utils/json-parser';
import type { ImportData } from '@/types/import';

export async function GET(request: NextRequest) {
  const roleCheck = await requireRole(request, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const session = await auth();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get the user's OCR processing history
    const history = await db
      .select()
      .from(ocrProcessingHistory)
      .where(eq(ocrProcessingHistory.userId, session.user.id))
      .orderBy(desc(ocrProcessingHistory.createdAt))
      .limit(20);

    // Parse the extracted data for each history entry
    const historyWithParsedData = history.map((entry) => {
      try {
        // Only try to parse if we have extracted data and status is 'completed'
        if (entry.extractedData && entry.status === 'completed') {
          const parsedData = parseOCRExtractedData<ImportData>(entry.extractedData);
          return {
            ...entry,
            parsedData,
          };
        }
        return entry;
      } catch (error) {
        // If parsing fails, just return the entry without parsed data
        console.error(`Failed to parse OCR data for entry ${entry.id}:`, error);
        return entry;
      }
    });

    return NextResponse.json({ history: historyWithParsedData });
  } catch (error) {
    console.error('Failed to fetch OCR history:', error);
    return NextResponse.json({ error: 'Failed to fetch OCR history' }, { status: 500 });
  }
}
