import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { db } from '@/db/client';
import { ocrProcessingHistory } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { parseOCRExtractedData } from '@/lib/utils/json-parser';
import type { ImportData } from '@/types/import';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const roleCheck = await requireRole(request, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const session = await auth();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = await params;

  try {
    // Get the specific OCR processing history entry
    const [entry] = await db
      .select()
      .from(ocrProcessingHistory)
      .where(and(eq(ocrProcessingHistory.id, id), eq(ocrProcessingHistory.userId, session.user.id)))
      .limit(1);

    if (!entry) {
      return NextResponse.json({ error: 'OCR history entry not found' }, { status: 404 });
    }

    // Parse the extracted data if available
    let parsedData = null;
    if (entry.extractedData && entry.status === 'completed') {
      try {
        parsedData = parseOCRExtractedData<ImportData>(entry.extractedData);
      } catch (error) {
        console.error(`Failed to parse OCR data for entry ${entry.id}:`, error);
      }
    }

    return NextResponse.json({
      ...entry,
      parsedData,
    });
  } catch (error) {
    console.error('Failed to fetch OCR history entry:', error);
    return NextResponse.json({ error: 'Failed to fetch OCR history entry' }, { status: 500 });
  }
}
