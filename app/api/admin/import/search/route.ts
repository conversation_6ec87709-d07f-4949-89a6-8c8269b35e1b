import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { db } from '@/db/client';
import {
  institutions,
  examBoards,
  positions,
  specializations,
  subjects,
  topics,
} from '@/db/schema';
import { eq, ilike, and, or, isNotNull } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  const roleCheck = await requireRole(request, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const session = await auth();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const searchParams = request.nextUrl.searchParams;
  const type = searchParams.get('type');
  const query = searchParams.get('q') || '';
  const parentId = searchParams.get('parentId');

  console.log('Search API called:', { type, query, parentId });

  if (!type) {
    return NextResponse.json({ error: 'Type parameter is required' }, { status: 400 });
  }

  try {
    let results = [];

    switch (type) {
      case 'institution':
        console.log(`Searching institutions with query: "${query}"`);
        results = await db
          .select({
            id: institutions.id,
            name: institutions.name,
            code: institutions.code,
          })
          .from(institutions)
          .where(
            or(
              ilike(institutions.name, `%${query}%`),
              and(isNotNull(institutions.code), ilike(institutions.code, `%${query}%`))
            )
          )
          .orderBy(institutions.name)
          .limit(20);
        console.log(`Found ${results.length} institutions:`, results);
        break;

      case 'examBoard':
        console.log(`Searching exam boards with query: "${query}"`);
        results = await db
          .select({
            id: examBoards.id,
            name: examBoards.name,
            code: examBoards.code,
          })
          .from(examBoards)
          .where(
            or(
              ilike(examBoards.name, `%${query}%`),
              and(isNotNull(examBoards.code), ilike(examBoards.code, `%${query}%`))
            )
          )
          .orderBy(examBoards.name)
          .limit(20);
        console.log(`Found ${results.length} exam boards:`, results);
        break;

      case 'position':
        console.log(`Searching positions with query: "${query}"`);
        results = await db
          .select({
            id: positions.id,
            name: positions.name,
            code: positions.code,
          })
          .from(positions)
          .where(
            or(
              ilike(positions.name, `%${query}%`),
              and(isNotNull(positions.code), ilike(positions.code, `%${query}%`))
            )
          )
          .orderBy(positions.name)
          .limit(20);
        console.log(`Found ${results.length} positions:`, results);
        break;

      case 'specialization':
        if (!parentId) {
          // Return empty results if no parent ID is provided
          console.log('No parentId provided for specialization search, returning empty results');
          return NextResponse.json({ results: [] });
        }
        results = await db
          .select({
            id: specializations.id,
            name: specializations.name,
            code: specializations.code,
          })
          .from(specializations)
          .where(
            and(
              eq(specializations.positionId, parentId),
              or(
                ilike(specializations.name, `%${query}%`),
                and(isNotNull(specializations.code), ilike(specializations.code, `%${query}%`))
              )
            )
          )
          .orderBy(specializations.name)
          .limit(20);
        break;

      case 'subject':
        results = await db
          .select({
            id: subjects.id,
            name: subjects.name,
          })
          .from(subjects)
          .where(ilike(subjects.name, `%${query}%`))
          .orderBy(subjects.name)
          .limit(20);
        break;

      case 'topic':
        if (parentId) {
          results = await db
            .select({
              id: topics.id,
              name: topics.name,
            })
            .from(topics)
            .where(and(eq(topics.subjectId, parentId), ilike(topics.name, `%${query}%`)))
            .orderBy(topics.name)
            .limit(20);
        } else {
          results = await db
            .select({
              id: topics.id,
              name: topics.name,
            })
            .from(topics)
            .where(ilike(topics.name, `%${query}%`))
            .orderBy(topics.name)
            .limit(20);
        }
        break;

      default:
        return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 });
    }

    return NextResponse.json({ results });
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json({ error: 'Failed to search entities' }, { status: 500 });
  }
}
