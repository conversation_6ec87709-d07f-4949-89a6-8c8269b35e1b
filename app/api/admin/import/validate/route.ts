import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import type { ImportQuestion, QuestionOption, AssociatedTextBlock } from '@/types/import';
import { z } from 'zod';

interface ValidationError {
  path: string;
  message: string;
}

// Validation schema
const validateImportSchema = z.object({
  metadata: z.object({
    institution: z.string().min(1),
    exam_board: z.string().min(1),
    position: z.string().min(1),
    year: z.union([z.string().regex(/^\d{4}$/), z.number()]),
    test_type: z.string().min(1),
    total_questions: z.number().positive(),
    specialization: z.string().optional().nullable(),
    booklet: z.string().optional().nullable(),
    application_date: z.string().optional().nullable(),
  }),
  associated_texts_catalog: z.array(
    z.object({
      id: z.string(),
      content: z.string(),
    })
  ),
  questions: z.array(
    z.object({
      number: z.number().positive(),
      subject: z.string().optional().nullable(),
      topic: z.string().optional().nullable(),
      associated_text_id: z.string().nullable(),
      stem: z.string().min(1),
      associated_text_references: z
        .array(
          z.object({
            snippet: z.string(),
            location_pointer: z.string(),
          })
        )
        .nullable()
        .optional(),
      options: z.array(
        z.object({
          order: z.number(),
          text: z.string().min(1),
        })
      ),
      correct_answer_order: z.number().nullable(),
      is_null: z.boolean(),
      change_reason: z.string(),
      // Manual image assignment fields
      image_url: z.string().optional(),
      image_public_id: z.string().optional(),
      // Support for multiple manual images
      manual_images: z
        .array(
          z.object({
            url: z.string(),
            public_id: z.string(),
          })
        )
        .optional(),
    })
  ),
});

export async function POST(request: NextRequest) {
  const roleCheck = await requireRole(request, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const session = await auth();
  if (!session) {
    return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
  }

  try {
    const body = await request.json();

    // Basic schema validation first
    const validation = validateImportSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      );
    }

    const data = validation.data;
    const errors: ValidationError[] = [];

    // Validate metadata
    if (!data.metadata) {
      errors.push({ path: 'metadata', message: 'Metadados são obrigatórios' });
    } else {
      const requiredMetadataFields = [
        'institution',
        'exam_board',
        'position',
        'year',
        'test_type',
        'total_questions',
      ];

      requiredMetadataFields.forEach((field) => {
        if (!data.metadata[field as keyof typeof data.metadata]) {
          errors.push({ path: `metadata.${field}`, message: `${field} é obrigatório` });
        }
      });

      // Validate year
      if (data.metadata.year) {
        const year =
          typeof data.metadata.year === 'string'
            ? parseInt(data.metadata.year)
            : data.metadata.year;
        if (isNaN(year) || year < 1900 || year > new Date().getFullYear() + 1) {
          errors.push({ path: 'metadata.year', message: 'Ano inválido' });
        }
      }

      // Validate test type
      if (
        data.metadata.test_type &&
        !['MULTIPLA_ESCOLHA', 'CERTO_ERRADO', 'DISCURSIVA'].includes(data.metadata.test_type)
      ) {
        errors.push({
          path: 'metadata.test_type',
          message: 'Tipo de prova deve ser MULTIPLA_ESCOLHA, CERTO_ERRADO ou DISCURSIVA',
        });
      }

      // Validate application date format
      if (data.metadata.application_date) {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(data.metadata.application_date)) {
          errors.push({
            path: 'metadata.application_date',
            message: 'Data de aplicação deve estar no formato AAAA-MM-DD',
          });
        }
      }
    }

    // Validate associated_texts_catalog
    if (!data.associated_texts_catalog) {
      errors.push({
        path: 'associated_texts_catalog',
        message: 'Catálogo de textos associados é obrigatório',
      });
    } else if (!Array.isArray(data.associated_texts_catalog)) {
      errors.push({
        path: 'associated_texts_catalog',
        message: 'Catálogo de textos associados deve ser um array',
      });
    } else {
      const textIds = new Set<string>();
      data.associated_texts_catalog.forEach((block: AssociatedTextBlock, index: number) => {
        const blockPath = `associated_texts_catalog[${index}]`;

        if (!block.id) {
          errors.push({ path: `${blockPath}.id`, message: 'ID do bloco de texto é obrigatório' });
        } else if (textIds.has(block.id)) {
          errors.push({
            path: `${blockPath}.id`,
            message: `ID de bloco de texto duplicado: ${block.id}`,
          });
        } else {
          textIds.add(block.id);
        }

        if (!block.content) {
          errors.push({
            path: `${blockPath}.content`,
            message: 'Conteúdo do bloco de texto é obrigatório',
          });
        }
      });
    }

    // Validate questions
    if (!data.questions) {
      errors.push({ path: 'questions', message: 'Array de questões é obrigatório' });
    } else if (!Array.isArray(data.questions)) {
      errors.push({ path: 'questions', message: 'Questões devem ser um array' });
    } else {
      data.questions.forEach((question: ImportQuestion, index: number) => {
        const questionPath = `questions[${index}]`;

        // Required fields
        if (!question.number) {
          errors.push({
            path: `${questionPath}.number`,
            message: 'Número da questão é obrigatório',
          });
        }

        if (!question.stem) {
          errors.push({
            path: `${questionPath}.stem`,
            message: 'Enunciado da questão é obrigatório',
          });
        }

        // Validate options
        if (!question.options) {
          errors.push({ path: `${questionPath}.options`, message: 'Opções são obrigatórias' });
        } else if (!Array.isArray(question.options)) {
          errors.push({ path: `${questionPath}.options`, message: 'Opções devem ser um array' });
        } else if (question.options.length < 2) {
          errors.push({
            path: `${questionPath}.options`,
            message: 'Pelo menos 2 opções são obrigatórias',
          });
        } else {
          question.options.forEach((option: QuestionOption, optIndex: number) => {
            const optionPath = `${questionPath}.options[${optIndex}]`;

            if (!option.order) {
              errors.push({ path: `${optionPath}.order`, message: 'Ordem da opção é obrigatória' });
            }

            if (!option.text) {
              errors.push({ path: `${optionPath}.text`, message: 'Texto da opção é obrigatório' });
            }
          });
        }

        // Validate associated_text_id reference
        if (question.associated_text_id) {
          const textIds = new Set(
            data.associated_texts_catalog?.map((t: AssociatedTextBlock) => t.id) || []
          );
          if (!textIds.has(question.associated_text_id)) {
            errors.push({
              path: `${questionPath}.associated_text_id`,
              message: `Bloco de texto referenciado "${question.associated_text_id}" não encontrado no catálogo`,
            });
          }
        }

        // Validate answer data
        if (typeof question.is_null !== 'boolean') {
          errors.push({ path: `${questionPath}.is_null`, message: 'is_null deve ser um booleano' });
        }

        if (!question.is_null && question.correct_answer_order === null) {
          errors.push({
            path: `${questionPath}.correct_answer_order`,
            message: 'correct_answer_order é obrigatório quando a questão não é nula',
          });
        }

        if (question.correct_answer_order !== null) {
          const optionOrders = question.options?.map((o: QuestionOption) => o.order) || [];
          if (!optionOrders.includes(question.correct_answer_order)) {
            errors.push({
              path: `${questionPath}.correct_answer_order`,
              message: `correct_answer_order ${question.correct_answer_order} não corresponde a nenhuma ordem de opção`,
            });
          }
        }
      });

      // Note: total_questions validation removed to support multi-prompt parsing
      // due to context window limitations
    }

    if (errors.length > 0) {
      return NextResponse.json(
        {
          valid: false,
          errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      valid: true,
      summary: {
        totalQuestions: data.questions?.length || 0,
        metadata: data.metadata,
        subjects: data.questions
          ? Array.from(new Set(data.questions.map((q) => q.subject).filter(Boolean)))
          : [],
        topics: data.questions
          ? Array.from(new Set(data.questions.map((q) => q.topic).filter(Boolean)))
          : [],
      },
    });
  } catch (error) {
    console.error('Validation error:', error);
    return NextResponse.json(
      {
        valid: false,
        errors: [{ path: 'root', message: 'Formato JSON inválido' }],
      },
      { status: 400 }
    );
  }
}
