import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { db } from '@/db/client';
import {
  institutions,
  examBoards,
  positions,
  specializations,
  civilServiceExams,
  civilServiceTests,
  subjects,
  topics,
  questions,
  questionOptions,
  questionKeys,
  images,
  questionImages,
  questionAssociatedTexts,
} from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import type { AssociatedTextBlock } from '@/types/import';
import { z } from 'zod';
import { extractAltTextFromQuestion } from '@/lib/utils/extract-alt-text';
import { generateHash } from '@/lib/utils/hash';

// Validation schemas
const questionOptionSchema = z.object({
  order: z.number(),
  text: z.string().min(1),
});

const questionImageSchema = z.object({
  url: z.string(),
  alt_text: z.string(),
  page: z.number(),
  bbox: z.array(z.number()).length(4),
  type: z.enum([
    'chart',
    'cartoon',
    'diagram',
    'table_image',
    'graph',
    'map',
    'photo',
    'illustration',
  ]),
  text_content: z.string().nullable().optional(),
  position_in_question: z.enum([
    'before_stem',
    'in_stem',
    'after_stem',
    'in_option_A',
    'in_option_B',
    'in_option_C',
    'in_option_D',
    'in_option_E',
  ]),
});

const associatedTextBlockSchema = z.object({
  id: z.string(),
  content: z.string(),
});

const importQuestionSchema = z.object({
  number: z.number().positive(),
  subject: z.string().nullable().optional(),
  topic: z.string().nullable().optional(),
  associated_text_id: z.string().nullable(),
  stem: z.string().min(1),
  associated_text_references: z
    .array(
      z.object({
        snippet: z.string(),
        location_pointer: z.string(),
      })
    )
    .nullable()
    .optional(),
  options: z.array(questionOptionSchema),
  images: z.array(questionImageSchema).optional(),
  correct_answer_order: z.number().nullable(),
  is_null: z.boolean(),
  change_reason: z.string().nullable().optional().default(''),
  // Manual image assignment fields
  image_url: z.string().optional(),
  image_public_id: z.string().optional(),
  // Support for multiple manual images
  manual_images: z
    .array(
      z.object({
        url: z.string(),
        public_id: z.string(),
      })
    )
    .optional(),
});

const mappedDataSchema = z.object({
  institutionId: z.string(),
  institutionIsNew: z.boolean(),
  examBoardId: z.string(),
  examBoardIsNew: z.boolean(),
  positionId: z.string(),
  positionIsNew: z.boolean(),
  specializationId: z.string().nullable(),
  specializationIsNew: z.boolean(),
  year: z
    .number()
    .min(1900)
    .max(new Date().getFullYear() + 5),
  testType: z.string(),
  booklet: z.string().transform((val) => val || null),
  applicationDate: z.string().transform((val) => val || null),
  manualMode: z.boolean().optional(),
  manualInstitution: z.string().optional(),
  manualExamBoard: z.string().optional(),
  manualPosition: z.string().optional(),
  manualSpecialization: z.string().optional(),
  matchedExamId: z.uuid().nullable().optional(),
  matchedTestId: z.uuid().nullable().optional(),
  subjectMappings: z.record(z.string(), z.object({ id: z.string(), isNew: z.boolean() })),
  topicMappings: z.record(
    z.string(),
    z.object({ id: z.string(), isNew: z.boolean(), subjectId: z.string() })
  ),
});

const processImportSchema = z.object({
  importData: z.object({
    metadata: z.any(),
    associated_texts_catalog: z.array(associatedTextBlockSchema),
    questions: z.array(importQuestionSchema),
  }),
  mappedData: mappedDataSchema,
  selectedQuestions: z.array(z.number()),
  matchedExamId: z.uuid().nullable().optional(),
  matchedTestId: z.uuid().nullable().optional(),
});

export async function POST(request: NextRequest) {
  const roleCheck = await requireRole(request, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const session = await auth();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json();

    // Validate input
    const validation = processImportSchema.safeParse(body);
    if (!validation.success) {
      console.error('Validation error:', JSON.stringify(validation.error.format(), null, 2));
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      );
    }

    const { importData, mappedData, selectedQuestions, matchedExamId, matchedTestId } =
      validation.data;

    // Use Drizzle transaction
    return await db.transaction(async (tx) => {
      // If manual mode, create entities first
      let finalInstitutionId = mappedData.institutionId;
      let finalExamBoardId = mappedData.examBoardId;
      let finalPositionId = mappedData.positionId;
      let finalSpecializationId = mappedData.specializationId || null;

      if (mappedData.manualMode) {
        // Create institution
        if (mappedData.manualInstitution) {
          finalInstitutionId = uuidv4();
          await tx.insert(institutions).values({
            id: finalInstitutionId,
            name: mappedData.manualInstitution,
          });
        }

        // Create exam board
        if (mappedData.manualExamBoard) {
          finalExamBoardId = uuidv4();
          await tx.insert(examBoards).values({
            id: finalExamBoardId,
            name: mappedData.manualExamBoard,
          });
        }

        // Create position
        if (mappedData.manualPosition) {
          finalPositionId = uuidv4();
          await tx.insert(positions).values({
            id: finalPositionId,
            name: mappedData.manualPosition,
          });
        }

        // Create specialization if provided
        if (mappedData.manualSpecialization && finalPositionId) {
          finalSpecializationId = uuidv4();
          await tx.insert(specializations).values({
            id: finalSpecializationId,
            name: mappedData.manualSpecialization,
            positionId: finalPositionId,
          });
        }
      }

      // 1. Check if we have matched exam/test IDs from exam matcher
      let examId: string;
      let testId: string;

      if (matchedExamId && matchedTestId) {
        // Use existing exam and test from exam matcher
        examId = matchedExamId;
        testId = matchedTestId;
      } else {
        // Create new exam and test
        examId = uuidv4();
        await tx.insert(civilServiceExams).values({
          id: examId,
          institutionId: finalInstitutionId,
          examBoardId: finalExamBoardId,
          positionId: finalPositionId,
          specializationId: finalSpecializationId || null,
          year: mappedData.year,
          status: 'ATIVO',
        });

        // 2. Create civil service test
        testId = uuidv4();
        await tx.insert(civilServiceTests).values({
          id: testId,
          civilServiceExamId: examId,
          type: mappedData.testType,
          booklet: mappedData.booklet || null,
          applicationDate: mappedData.applicationDate || null,
        });
      }

      // 3. Create new subjects and topics if needed
      const createdSubjects: Record<string, string> = {};
      const createdTopics: Record<string, string> = {};

      // Create subjects marked as new
      if (mappedData.subjectMappings) {
        for (const [subjectName, mapping] of Object.entries(mappedData.subjectMappings)) {
          if (mapping.isNew) {
            // Check if subject already exists (handle race conditions)
            const existingSubject = await tx
              .select()
              .from(subjects)
              .where(eq(subjects.name, subjectName))
              .limit(1);

            if (existingSubject.length > 0) {
              // Subject already exists, use existing ID
              createdSubjects[subjectName] = existingSubject[0].id;
            } else {
              // Create new subject
              const subjectId = uuidv4();
              try {
                await tx.insert(subjects).values({
                  id: subjectId,
                  name: subjectName,
                });
                createdSubjects[subjectName] = subjectId;
              } catch (error) {
                // Handle duplicate key error (race condition)
                if (error instanceof Error && 'code' in error && error.code === '23505') {
                  const existingSubject = await tx
                    .select()
                    .from(subjects)
                    .where(eq(subjects.name, subjectName))
                    .limit(1);

                  if (existingSubject.length > 0) {
                    createdSubjects[subjectName] = existingSubject[0].id;
                  } else {
                    throw error; // Re-throw if still can't find the subject
                  }
                } else {
                  throw error;
                }
              }
            }
          } else {
            createdSubjects[subjectName] = mapping.id;
          }
        }
      }

      // Create topics marked as new
      if (mappedData.topicMappings) {
        for (const [topicName, mapping] of Object.entries(mappedData.topicMappings)) {
          if (mapping.isNew && mapping.subjectId) {
            // Check if topic already exists for this subject (handle race conditions)
            const existingTopic = await tx
              .select()
              .from(topics)
              .where(and(eq(topics.name, topicName), eq(topics.subjectId, mapping.subjectId)))
              .limit(1);

            if (existingTopic.length > 0) {
              // Topic already exists, use existing ID
              createdTopics[topicName] = existingTopic[0].id;
            } else {
              // Create new topic
              const topicId = uuidv4();
              try {
                await tx.insert(topics).values({
                  id: topicId,
                  subjectId: mapping.subjectId,
                  name: topicName,
                  level: 0,
                  path: topicName,
                });
                createdTopics[topicName] = topicId;
              } catch (error) {
                // Handle duplicate key error (race condition)
                if (error instanceof Error && 'code' in error && error.code === '23505') {
                  const existingTopic = await tx
                    .select()
                    .from(topics)
                    .where(and(eq(topics.name, topicName), eq(topics.subjectId, mapping.subjectId)))
                    .limit(1);

                  if (existingTopic.length > 0) {
                    createdTopics[topicName] = existingTopic[0].id;
                  } else {
                    throw error; // Re-throw if still can't find the topic
                  }
                } else {
                  throw error;
                }
              }
            }
          } else {
            createdTopics[topicName] = mapping.id;
          }
        }
      }

      // Create lookup map for associated texts
      const textCatalog = new Map<string, string>();
      if (importData.associated_texts_catalog) {
        importData.associated_texts_catalog.forEach((block: AssociatedTextBlock) => {
          textCatalog.set(block.id, block.content);
        });
      }

      // 4. Import selected questions
      const importedQuestions = [];
      const questionsToImport: number[] = selectedQuestions;

      for (const questionNumber of questionsToImport) {
        const questionId = uuidv4();

        const question = importData.questions.find((q) => q.number === questionNumber);
        if (!question) continue;

        // Check for associated_text_references
        if (question.associated_text_references) {
          // Will be stored in the database
        }

        // Get subject and topic IDs from created mappings
        let subjectId = null;
        let topicId = null;

        if (question.subject && createdSubjects[question.subject]) {
          subjectId = createdSubjects[question.subject];
        }

        if (question.topic && createdTopics[question.topic]) {
          topicId = createdTopics[question.topic];
        }

        // Resolve and store associated text
        let associatedTextId = null;
        if (question.associated_text_id) {
          // Get text from catalog
          let associatedTextContent = textCatalog.get(question.associated_text_id);

          // Fallback for backward compatibility
          if (
            !associatedTextContent &&
            question.associated_text_id.includes('<') &&
            question.associated_text_id.includes('>')
          ) {
            console.warn(
              `Question ${question.number}: associated_text_id contains HTML instead of ID reference. Using as fallback.`
            );
            associatedTextContent = question.associated_text_id;
          }

          if (associatedTextContent) {
            // Generate hash for deduplication
            const contentHash = generateHash(associatedTextContent);

            // Check if this text already exists
            const existingText = await tx
              .select()
              .from(questionAssociatedTexts)
              .where(eq(questionAssociatedTexts.hash, contentHash))
              .limit(1);

            if (existingText.length > 0) {
              // Use existing text
              associatedTextId = existingText[0].id;
            } else {
              // Create new associated text
              const newTextId = uuidv4();
              await tx.insert(questionAssociatedTexts).values({
                id: newTextId,
                content: associatedTextContent,
                hash: contentHash,
              });
              associatedTextId = newTextId;
            }
          } else if (question.associated_text_id) {
            console.error(
              `Question ${question.number}: associated_text_id '${question.associated_text_id}' not found in catalog`
            );
          }
        }

        // Insert question
        await tx.insert(questions).values({
          id: questionId,
          civilServiceTestId: testId,
          number: question.number,
          associatedTextId: associatedTextId,
          associatedTextReferences: question.associated_text_references || null,
          stem: question.stem,
          subjectId: subjectId,
          topicId: topicId,
        });

        // Extract alt text from question HTML content
        const altTextMap = extractAltTextFromQuestion(
          question.stem,
          question.options,
          undefined // Associated text is now stored separately
        );

        // Handle images
        let imageOrder = 0;
        if (question.images && Array.isArray(question.images) && question.images.length > 0) {
          for (let i = 0; i < question.images.length; i++) {
            const img = question.images[i];
            // Create a placeholder URL for now - in production you would upload to a CDN
            const imageUrl = `https://placeholder.com/exam-images/${testId}/${img.url}`;

            // Check if image already exists
            const existingImage = await tx
              .select({ id: images.id })
              .from(images)
              .where(eq(images.url, imageUrl))
              .limit(1);

            let imageId: string;
            if (existingImage.length > 0) {
              imageId = existingImage[0].id;
            } else {
              // Insert new image
              imageId = uuidv4();
              // Truncate alt_text to 500 characters if needed
              const altText = img.alt_text ? img.alt_text.substring(0, 500) : null;
              await tx.insert(images).values({
                id: imageId,
                url: imageUrl,
                altText: altText,
              });
            }

            // Link image to question
            await tx.insert(questionImages).values({
              id: uuidv4(),
              questionId: questionId,
              imageId: imageId,
              order: imageOrder++,
            });
          }
        }

        // Handle manually assigned images (from Manual Image Selector)
        if (question.image_url && question.image_public_id) {
          // Check if image already exists
          const existingImage = await tx
            .select({ id: images.id })
            .from(images)
            .where(eq(images.url, question.image_url))
            .limit(1);

          let imageId: string;
          if (existingImage.length > 0) {
            imageId = existingImage[0].id;
          } else {
            // Insert new image
            imageId = uuidv4();
            // Try to get alt text from the extracted map, fallback to generic text
            const extractedAltText = altTextMap.get(question.image_url);
            const altText =
              extractedAltText || `Manually assigned image for question ${question.number}`;

            await tx.insert(images).values({
              id: imageId,
              url: question.image_url,
              altText: altText.substring(0, 500), // Ensure it fits in the database column
            });
          }

          // Link image to question
          await tx.insert(questionImages).values({
            id: uuidv4(),
            questionId: questionId,
            imageId: imageId,
            order: imageOrder++,
          });
        }

        // Handle multiple manually assigned images
        if (question.manual_images && question.manual_images.length > 0) {
          for (const manualImage of question.manual_images) {
            // Check if image already exists
            const existingImage = await tx
              .select({ id: images.id })
              .from(images)
              .where(eq(images.url, manualImage.url))
              .limit(1);

            let imageId: string;
            if (existingImage.length > 0) {
              imageId = existingImage[0].id;
            } else {
              // Insert new image
              imageId = uuidv4();
              // Try to get alt text from the extracted map, fallback to generic text
              const extractedAltText = altTextMap.get(manualImage.url);
              const altText =
                extractedAltText || `Manually assigned image for question ${question.number}`;

              await tx.insert(images).values({
                id: imageId,
                url: manualImage.url,
                altText: altText.substring(0, 500), // Ensure it fits in the database column
              });
            }

            // Link image to question
            await tx.insert(questionImages).values({
              id: uuidv4(),
              questionId: questionId,
              imageId: imageId,
              order: imageOrder++,
            });
          }
        }

        // Insert options and track them for correct answer mapping
        const optionIds: Map<number, string> = new Map();
        for (const option of question.options) {
          const optionId = uuidv4();
          optionIds.set(option.order, optionId);

          await tx.insert(questionOptions).values({
            id: optionId,
            questionId: questionId,
            order: option.order,
            text: option.text,
          });
        }

        // Handle answer key data
        let correctAnswerId = null;
        if (!question.is_null && question.correct_answer_order) {
          correctAnswerId = optionIds.get(question.correct_answer_order) || null;
        }

        // Insert question key
        await tx.insert(questionKeys).values({
          id: uuidv4(),
          questionId: questionId,
          correctAnswerId: correctAnswerId,
          type: 'MULTIPLE_CHOICE',
          isNull: question.is_null,
          changeReason: question.change_reason || null,
        });

        importedQuestions.push({
          questionId,
          number: questionNumber,
        });
      }

      return NextResponse.json({
        success: true,
        examId,
        testId,
        importedCount: importedQuestions.length,
        importedQuestions,
      });
    });
  } catch (error) {
    console.error('Import error:', error);
    return NextResponse.json(
      {
        error: 'Failed to import data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
