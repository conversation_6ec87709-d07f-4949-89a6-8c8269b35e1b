import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db/client';
import { promptTemplates } from '@/db/schema';
import { inArray } from 'drizzle-orm';

// GET /api/admin/import/templates - List OCR templates
export async function GET() {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get only OCR-related prompt types
    const ocrPromptTypes = ['prova', 'gabarito', 'edital', 'prova_gabarito'] as const;

    const templates = await db
      .select()
      .from(promptTemplates)
      .where(
        inArray(
          promptTemplates.promptType,
          ocrPromptTypes as readonly ['prova', 'gabarito', 'edital', 'prova_gabarito']
        )
      )
      .orderBy(promptTemplates.isSystem, promptTemplates.promptType, promptTemplates.name);

    return NextResponse.json({
      success: true,
      templates,
    });
  } catch (error) {
    console.error('Failed to fetch templates:', error);
    return NextResponse.json({ error: 'Failed to fetch templates' }, { status: 500 });
  }
}
