import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { getGeminiService } from '@/lib/gemini/service';
import { db } from '@/db/client';
import { ocrProcessingHistory, promptTemplates } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { pythonImageExtractor } from '@/lib/python-image-extraction';

export async function POST(req: NextRequest) {
  try {
    // Check admin role
    const roleCheck = await requireRole(req, ROLES.ADMIN);
    if (roleCheck instanceof NextResponse) return roleCheck;

    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await req.formData();
    const provaFile = formData.get('provaFile') as File;
    const gabaritoFile = formData.get('gabaritoFile') as File;
    const modelName = (formData.get('modelName') as string) || 'gemini-2.5-flash';
    const customSystemPrompt = formData.get('systemPrompt') as string | null;

    // Image extraction parameters
    const extractImages = formData.get('extractImages') === 'true';
    const imageFolder = (formData.get('imageFolder') as string) || 'memo-extracted-images';
    const includeVisualDetection = formData.get('includeVisualDetection') === 'true';

    // Get model parameters
    const temperature = parseFloat(formData.get('temperature') as string) || 0.7;
    const topP = parseFloat(formData.get('topP') as string) || 0.95;
    const topK = parseInt(formData.get('topK') as string) || 40;
    const thinkingBudget = parseInt(formData.get('thinkingBudget') as string) || 0;
    const customPrompt =
      (formData.get('customPrompt') as string) ||
      'Process both PDFs together, analyzing the Prova (exam questions) and Gabarito (answer key) to ensure proper correspondence between questions and their correct answers.';

    if (!provaFile || !gabaritoFile) {
      return NextResponse.json(
        { error: 'Both Prova and Gabarito files are required' },
        { status: 400 }
      );
    }

    // Check file types
    if (provaFile.type !== 'application/pdf' || gabaritoFile.type !== 'application/pdf') {
      return NextResponse.json({ error: 'Only PDF files are supported' }, { status: 400 });
    }

    // Check file sizes (max 20MB each)
    if (provaFile.size > 20 * 1024 * 1024 || gabaritoFile.size > 20 * 1024 * 1024) {
      return NextResponse.json({ error: 'Each file must be less than 20MB' }, { status: 400 });
    }

    const startTime = Date.now();

    try {
      // Convert files to buffers
      const provaArrayBuffer = await provaFile.arrayBuffer();
      const provaBuffer = Buffer.from(provaArrayBuffer);

      const gabaritoArrayBuffer = await gabaritoFile.arrayBuffer();
      const gabaritoBuffer = Buffer.from(gabaritoArrayBuffer);

      // Get Gemini service
      const geminiService = getGeminiService();

      // Upload both files to Gemini
      const [uploadedProva, uploadedGabarito] = await Promise.all([
        geminiService.uploadFile({
          file: provaBuffer,
          mimeType: 'application/pdf',
          displayName: provaFile.name,
        }),
        geminiService.uploadFile({
          file: gabaritoBuffer,
          mimeType: 'application/pdf',
          displayName: gabaritoFile.name,
        }),
      ]);

      // Get the appropriate prompt - use custom if provided, otherwise fetch from database
      let systemPrompt = customSystemPrompt;

      if (!systemPrompt) {
        // Fetch the prova_gabarito template from database
        const [template] = await db
          .select()
          .from(promptTemplates)
          .where(
            and(
              eq(promptTemplates.promptType, 'prova_gabarito'),
              eq(promptTemplates.isDefault, true)
            )
          )
          .limit(1);

        if (template) {
          systemPrompt = template.promptContent;
        } else {
          return NextResponse.json(
            { error: 'No default template found for prova_gabarito' },
            { status: 500 }
          );
        }
      }

      // Create combined prompt that references both files
      const combinedPrompt = `${customPrompt}

Prova PDF: ${uploadedProva.uri}
Gabarito PDF: ${uploadedGabarito.uri}

Please analyze both documents and create the combined JSON output according to the system instructions.`;

      // Generate content with both files
      const response = await geminiService.generateContentWithFiles(
        [uploadedProva.uri, uploadedGabarito.uri],
        combinedPrompt,
        systemPrompt,
        modelName,
        {
          temperature,
          topP,
          topK,
          thinkingBudget,
        }
      );

      // Return raw text without validation
      const rawText = response.text;

      // Extract images if requested (simplified approach)
      let extractedImages: Array<{
        success: boolean;
        page_number?: number;
        cloudinary_url?: string;
        method?: string;
        error?: string;
      }> = [];
      let imgTagCount = 0;

      if (extractImages) {
        try {
          console.log('Starting simplified image extraction from prova PDF...');

          // Count <img> tags in OCR response (including self-closing tags with attributes)
          imgTagCount = (rawText.match(/<img[^>]*>/g) || []).length;
          console.log(`Found ${imgTagCount} <img> tags in OCR response`);

          // Debug: show where the images are found and extract page information
          const targetPages: number[] = [];
          if (imgTagCount > 0) {
            const imgMatches = rawText.match(/<img[^>]*>/g) || [];
            imgMatches.forEach((match, index) => {
              const srcMatch = match.match(/src="([^"]*)"/);
              const pageMatch = match.match(/data-page="([^"]*)"/);
              const pageNum = pageMatch ? parseInt(pageMatch[1]) : null;
              if (pageNum && !targetPages.includes(pageNum)) {
                targetPages.push(pageNum);
              }
              console.log(
                `  Image ${index + 1}: ${srcMatch ? srcMatch[1] : 'no src'} (page ${pageMatch ? pageMatch[1] : 'unknown'})`
              );
            });
            console.log(`Target pages for image extraction: [${targetPages.join(', ')}]`);
          }

          if (imgTagCount > 0) {
            console.log(`Attempting to extract ${imgTagCount} images from PDF...`);

            // Extract all images from prova PDF (without uploading to Cloudinary)
            const imageResults = await pythonImageExtractor.extractAllImages(provaBuffer, {
              uploadToCloudinary: false,
              folder: imageFolder,
              includeVisualDetection,
              skipCoverPage: true,
            });

            console.log(`Python extractor returned ${imageResults.length} results`);
            imageResults.forEach((result, index) => {
              console.log(
                `  Result ${index + 1}: success=${result.success}, page=${result.page_number}, method=${result.method}, error=${result.error}`
              );
            });

            // Take ALL successfully extracted images for manual selection
            extractedImages = imageResults.filter((result) => result.success);

            console.log(
              `Successfully extracted ${extractedImages.length} images (${imgTagCount} <img> tags found)`
            );

            // Additional debugging: show what was actually extracted
            if (extractedImages.length > 0) {
              console.log('✅ Extracted images details:');
              extractedImages.forEach((img, index) => {
                console.log(
                  `    Image ${index + 1}: page=${img.page_number}, method=${img.method}, url=${img.cloudinary_url ? 'uploaded' : 'not uploaded'}`
                );
              });
            } else {
              console.log('❌ No images were successfully extracted despite finding <img> tags');
            }
          } else {
            console.log('No <img> tags found in OCR response - skipping image extraction');
          }
        } catch (imageError) {
          console.error('Image extraction failed:', imageError);
          // Continue with OCR processing even if image extraction fails
        }
      }

      // Record processing history with raw text and extracted images
      await db.insert(ocrProcessingHistory).values({
        userId: session.user.id,
        templateId: null, // Will be updated to link to the template
        fileName: `${provaFile.name} + ${gabaritoFile.name}`,
        fileSize: provaFile.size + gabaritoFile.size,
        mimeType: 'application/pdf',
        modelUsed: modelName,
        status: 'completed',
        extractedData: {
          rawText,
          extractedImages: extractedImages.length > 0 ? extractedImages : undefined,
          imgTagCount: imgTagCount > 0 ? imgTagCount : undefined,
        },
        processingTimeMs: Date.now() - startTime,
        tokensUsed: response.tokenCount || 0,
      });

      // Clean up - delete both files from Gemini
      try {
        await Promise.all([
          geminiService.deleteFile(uploadedProva.uri),
          geminiService.deleteFile(uploadedGabarito.uri),
        ]);
      } catch (deleteError) {
        console.error('Failed to delete files from Gemini:', deleteError);
        // Continue anyway - files expire after 48 hours
      }

      return NextResponse.json({
        success: true,
        rawText,
        systemPrompt, // Return the system prompt used
        processingTime: Date.now() - startTime,
        tokensUsed: response.tokenCount,
        extractedImages: extractedImages.length > 0 ? extractedImages : undefined,
        imgTagCount: imgTagCount > 0 ? imgTagCount : undefined,
        imageExtractionEnabled: extractImages,
      });
    } catch (error) {
      console.error('Dual PDF OCR processing error:', error);

      // Record failed processing
      await db.insert(ocrProcessingHistory).values({
        userId: session.user.id,
        templateId: null,
        fileName: `${provaFile.name} + ${gabaritoFile.name}`,
        fileSize: provaFile.size + gabaritoFile.size,
        mimeType: 'application/pdf',
        modelUsed: modelName,
        status: 'failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        processingTimeMs: Date.now() - startTime,
      });

      return NextResponse.json(
        {
          error: 'Failed to process PDFs',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Route error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
