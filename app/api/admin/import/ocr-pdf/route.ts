import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { getGeminiService } from '@/lib/gemini/service';
import { db } from '@/db/client';
import { ocrProcessingHistory, promptTemplates } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { pythonImageExtractor } from '@/lib/python-image-extraction';

export async function POST(req: NextRequest) {
  try {
    // Check admin role
    const roleCheck = await requireRole(req, ROLES.ADMIN);
    if (roleCheck instanceof NextResponse) return roleCheck;

    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const promptType = formData.get('promptType') as string;
    const mode = formData.get('mode') as string; // Special mode for image extraction only
    const modelName = (formData.get('modelName') as string) || 'gemini-2.5-flash';
    const customSystemPrompt = formData.get('systemPrompt') as string | null;

    // Image extraction parameters
    const extractImages = formData.get('extractImages') === 'true';
    const imageFolder = (formData.get('imageFolder') as string) || 'memo-extracted-images';
    const includeVisualDetection = formData.get('includeVisualDetection') === 'true';

    // Get model parameters
    const temperature = parseFloat(formData.get('temperature') as string) || 0.7;
    const topP = parseFloat(formData.get('topP') as string) || 0.95;
    const topK = parseInt(formData.get('topK') as string) || 40;
    const thinkingBudget = parseInt(formData.get('thinkingBudget') as string) || 0;
    const customPrompt =
      (formData.get('customPrompt') as string) ||
      'Extract the data from this PDF according to the instructions provided.';

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Special handling for image extraction only mode
    if (mode === 'image_extraction_only') {
      if (!extractImages) {
        return NextResponse.json(
          { error: 'Image extraction must be enabled for this mode' },
          { status: 400 }
        );
      }

      // Convert file to buffer
      const fileBuffer = Buffer.from(await file.arrayBuffer());

      // Extract images using Python service
      const extractionResult = await pythonImageExtractor.extractImages(
        fileBuffer,
        undefined, // No specific coordinates - extract all images
        {
          uploadToCloudinary: true,
          folder: imageFolder,
          includeVisualDetection,
        }
      );

      if (!extractionResult.success) {
        return NextResponse.json(
          { error: extractionResult.error || 'Image extraction failed' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        extractedImages: extractionResult.extracted_images,
        imgTagCount: extractionResult.successful_extractions,
        totalProcessed: extractionResult.total_processed,
        rawText: 'Image extraction completed successfully',
      });
    }

    if (!['prova', 'gabarito', 'edital'].includes(promptType)) {
      return NextResponse.json({ error: 'Invalid prompt type' }, { status: 400 });
    }

    // Check file type
    if (file.type !== 'application/pdf') {
      return NextResponse.json({ error: 'Only PDF files are supported' }, { status: 400 });
    }

    // Check file size (max 20MB)
    if (file.size > 20 * 1024 * 1024) {
      return NextResponse.json({ error: 'File size must be less than 20MB' }, { status: 400 });
    }

    const startTime = Date.now();
    let templateId: string | null = null;

    try {
      // Convert file to buffer
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // Get Gemini service
      const geminiService = getGeminiService();

      // Upload file to Gemini
      const uploadedFile = await geminiService.uploadFile({
        file: buffer,
        mimeType: 'application/pdf',
        displayName: file.name,
      });

      // Get the appropriate prompt - use custom if provided, otherwise fetch from database
      let systemPrompt = customSystemPrompt;

      if (!systemPrompt) {
        // Fetch the template from database
        const [template] = await db
          .select()
          .from(promptTemplates)
          .where(
            and(eq(promptTemplates.promptType, promptType), eq(promptTemplates.isDefault, true))
          )
          .limit(1);

        if (template) {
          systemPrompt = template.promptContent;
          templateId = template.id;
        } else {
          return NextResponse.json(
            { error: `No default template found for ${promptType}` },
            { status: 500 }
          );
        }
      }

      // Generate content with the uploaded file
      const response = await geminiService.generateContentWithFile(
        uploadedFile.uri,
        customPrompt,
        systemPrompt,
        modelName,
        {
          temperature,
          topP,
          topK,
          thinkingBudget,
        }
      );

      // Return raw text without validation
      const rawText = response.text;

      // Extract images if requested (simplified approach)
      let extractedImages: Array<{
        success: boolean;
        page_number?: number;
        cloudinary_url?: string;
        method?: string;
        error?: string;
      }> = [];
      let imgTagCount = 0;

      if (extractImages) {
        try {
          console.log('Starting simplified image extraction from PDF...');

          // Count <img> tags in OCR response (including self-closing tags with attributes)
          imgTagCount = (rawText.match(/<img[^>]*>/g) || []).length;
          console.log(`Found ${imgTagCount} <img> tags in OCR response`);

          // Debug: show where the images are found and extract page information
          const targetPages: number[] = [];
          if (imgTagCount > 0) {
            const imgMatches = rawText.match(/<img[^>]*>/g) || [];
            imgMatches.forEach((match, index) => {
              const srcMatch = match.match(/src="([^"]*)"/);
              const pageMatch = match.match(/data-page="([^"]*)"/);
              const pageNum = pageMatch ? parseInt(pageMatch[1]) : null;
              if (pageNum && !targetPages.includes(pageNum)) {
                targetPages.push(pageNum);
              }
              console.log(
                `  Image ${index + 1}: ${srcMatch ? srcMatch[1] : 'no src'} (page ${pageMatch ? pageMatch[1] : 'unknown'})`
              );
            });
            console.log(`Target pages for image extraction: [${targetPages.join(', ')}]`);
          }

          if (imgTagCount > 0) {
            console.log(`Attempting to extract ${imgTagCount} images from PDF...`);
            // Convert file to buffer for image extraction
            const arrayBuffer = await file.arrayBuffer();
            const buffer = Buffer.from(arrayBuffer);

            // Extract all images from PDF (without uploading to Cloudinary)
            const imageResults = await pythonImageExtractor.extractAllImages(buffer, {
              uploadToCloudinary: false,
              folder: imageFolder,
              includeVisualDetection,
              skipCoverPage: true,
            });

            console.log(`Python extractor returned ${imageResults.length} results`);
            imageResults.forEach((result, index) => {
              console.log(
                `  Result ${index + 1}: success=${result.success}, page=${result.page_number}, method=${result.method}, error=${result.error}`
              );
            });

            // Take ALL successfully extracted images for manual selection
            extractedImages = imageResults.filter((result) => result.success);

            console.log(
              `Successfully extracted ${extractedImages.length} images (${imgTagCount} <img> tags found)`
            );

            // Update imgTagCount to reflect actual extracted images if we found more than expected
            if (extractedImages.length > imgTagCount) {
              imgTagCount = extractedImages.length;
              console.log(`Updated imgTagCount to ${imgTagCount} based on actual extractions`);
            }

            // Additional debugging: show what was actually extracted
            if (extractedImages.length > 0) {
              console.log('✅ Extracted images details:');
              extractedImages.forEach((img, index) => {
                console.log(
                  `    Image ${index + 1}: page=${img.page_number}, method=${img.method}, url=${img.cloudinary_url ? 'uploaded' : 'not uploaded'}`
                );
              });
            } else {
              console.log('❌ No images were successfully extracted despite finding <img> tags');
            }
          } else {
            console.log('No <img> tags found in OCR response - skipping image extraction');
          }
        } catch (imageError) {
          console.error('Image extraction failed:', imageError);
          // Continue with OCR processing even if image extraction fails
        }
      }

      // Record processing history with raw text and extracted images
      await db.insert(ocrProcessingHistory).values({
        userId: session.user.id,
        templateId: templateId,
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        modelUsed: modelName,
        status: 'completed',
        extractedData: {
          rawText,
          extractedImages: extractedImages.length > 0 ? extractedImages : undefined,
          imgTagCount: imgTagCount > 0 ? imgTagCount : undefined,
        },
        processingTimeMs: Date.now() - startTime,
        tokensUsed: response.tokenCount || 0,
      });

      // Clean up - delete the file from Gemini
      try {
        await geminiService.deleteFile(uploadedFile.uri);
      } catch (deleteError) {
        console.error('Failed to delete file from Gemini:', deleteError);
        // Continue anyway - files expire after 48 hours
      }

      return NextResponse.json({
        success: true,
        rawText,
        systemPrompt, // Return the system prompt used
        processingTime: Date.now() - startTime,
        tokensUsed: response.tokenCount,
        extractedImages: extractedImages.length > 0 ? extractedImages : undefined,
        imgTagCount: imgTagCount > 0 ? imgTagCount : undefined,
        imageExtractionEnabled: extractImages,
      });
    } catch (error) {
      console.error('OCR processing error:', error);

      // Record failed processing
      await db.insert(ocrProcessingHistory).values({
        userId: session.user.id,
        templateId: templateId,
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        modelUsed: modelName,
        status: 'failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        processingTimeMs: Date.now() - startTime,
      });

      return NextResponse.json(
        {
          error: 'Failed to process PDF',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Route error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
