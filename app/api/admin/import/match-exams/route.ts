import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { db } from '@/db/client';
import {
  civilServiceExams,
  institutions,
  examBoards,
  positions,
  specializations,
  civilServiceTests,
  questions,
} from '@/db/schema';
import { eq, ilike, and, or, sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  const roleCheck = await requireRole(request, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const session = await auth();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const searchParams = request.nextUrl.searchParams;
  const institution = searchParams.get('institution');
  const examBoard = searchParams.get('examBoard');
  const position = searchParams.get('position');
  const year = searchParams.get('year');
  const searchTerm = searchParams.get('search');

  try {
    // Build conditions based on search parameters
    const conditions = [];

    if (searchTerm) {
      // General search across all fields
      conditions.push(
        or(
          ilike(institutions.name, `%${searchTerm}%`),
          or(ilike(examBoards.name, `%${searchTerm}%`), ilike(examBoards.code, `%${searchTerm}%`)),
          ilike(positions.name, `%${searchTerm}%`),
          ilike(specializations.name, `%${searchTerm}%`),
          sql`CAST(${civilServiceExams.year} AS TEXT) LIKE ${`%${searchTerm}%`}`
        )
      );
    } else {
      // Specific field searches
      if (institution) {
        conditions.push(ilike(institutions.name, `%${institution}%`));
      }
      if (examBoard) {
        conditions.push(
          or(ilike(examBoards.name, `%${examBoard}%`), ilike(examBoards.code, `%${examBoard}%`))
        );
      }
      if (position) {
        conditions.push(ilike(positions.name, `%${position}%`));
      }
      if (year) {
        conditions.push(eq(civilServiceExams.year, parseInt(year)));
      }
    }

    // Build the query with conditions
    const baseQuery = db
      .select({
        examId: civilServiceExams.id,
        institutionId: civilServiceExams.institutionId,
        examBoardId: civilServiceExams.examBoardId,
        positionId: civilServiceExams.positionId,
        specializationId: civilServiceExams.specializationId,
        testId: civilServiceTests.id,
        institution:
          sql<string>`CASE WHEN ${institutions.code} IS NOT NULL THEN ${institutions.name} || ' (' || ${institutions.code} || ')' ELSE ${institutions.name} END`.as(
            'institution'
          ),
        examBoard: sql<string>`COALESCE(${examBoards.code}, ${examBoards.name})`.as('exam_board'),
        position: positions.name,
        specialization: specializations.name,
        year: civilServiceExams.year,
        testType: civilServiceTests.type,
        booklet: civilServiceTests.booklet,
        applicationDate: civilServiceTests.applicationDate,
        questionCount: sql<string>`COUNT(DISTINCT ${questions.id})`.as('question_count'),
      })
      .from(civilServiceExams)
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .leftJoin(specializations, eq(civilServiceExams.specializationId, specializations.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .leftJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id));

    // Apply conditions if any exist
    const query = conditions.length > 0 ? baseQuery.where(and(...conditions)) : baseQuery;

    const results = await query
      .groupBy(
        civilServiceExams.id,
        civilServiceExams.institutionId,
        civilServiceExams.examBoardId,
        civilServiceExams.positionId,
        civilServiceExams.specializationId,
        civilServiceTests.id,
        institutions.name,
        institutions.code,
        examBoards.code,
        examBoards.name,
        positions.name,
        specializations.name,
        civilServiceExams.year,
        civilServiceTests.type,
        civilServiceTests.booklet,
        civilServiceTests.applicationDate,
        civilServiceTests.createdAt
      )
      .orderBy(sql`${civilServiceExams.year} DESC`, sql`${civilServiceTests.createdAt} DESC`)
      .limit(50);

    const exams = results.map((row) => ({
      id: row.examId,
      testId: row.testId,
      institutionId: row.institutionId,
      examBoardId: row.examBoardId,
      positionId: row.positionId,
      specializationId: row.specializationId,
      institution: row.institution,
      examBoard: row.examBoard,
      position: row.position,
      specialization: row.specialization,
      year: row.year,
      testType: row.testType,
      booklet: row.booklet,
      applicationDate: row.applicationDate,
      questionCount: parseInt(row.questionCount),
    }));

    return NextResponse.json({ exams });
  } catch (error) {
    console.error('Error searching for exams:', error);
    return NextResponse.json({ error: 'Failed to search for exams' }, { status: 500 });
  }
}
