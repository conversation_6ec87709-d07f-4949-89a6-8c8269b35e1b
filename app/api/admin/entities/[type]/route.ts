import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { db } from '@/db/client';
import {
  institutions,
  examBoards,
  positions,
  specializations,
  subjects,
  topics,
} from '@/db/schema';
import { eq, ilike, and } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import { z } from 'zod';

// Validation schemas
const createEntitySchema = z.object({
  name: z.string().min(1, { error: 'Name is required' }).max(255),
  code: z.string().max(50).optional().nullable(),
  parentId: z.uuid().optional(),
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ type: string }> }
) {
  const roleCheck = await requireRole(request, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const session = await auth();
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { type } = await params;
  const body = await request.json();

  // Validate input
  const validation = createEntitySchema.safeParse(body);
  if (!validation.success) {
    return NextResponse.json(
      { error: 'Invalid input', details: validation.error.format() },
      { status: 400 }
    );
  }

  const { name, code, parentId } = validation.data;

  const id = uuidv4();

  try {
    let result;

    switch (type) {
      case 'institution':
        // First check if it already exists
        const existingInst = await db
          .select({ id: institutions.id, name: institutions.name, code: institutions.code })
          .from(institutions)
          .where(ilike(institutions.name, name))
          .limit(1);

        if (existingInst.length > 0) {
          return NextResponse.json(existingInst[0]);
        }

        const [newInst] = await db
          .insert(institutions)
          .values({ id, name, code: code || null })
          .returning({ id: institutions.id, name: institutions.name, code: institutions.code })
          .onConflictDoUpdate({
            target: institutions.name,
            set: { updatedAt: new Date() },
          });
        result = [newInst];
        break;

      case 'examBoard':
        // First check if it already exists
        const existingBoard = await db
          .select({ id: examBoards.id, name: examBoards.name, code: examBoards.code })
          .from(examBoards)
          .where(ilike(examBoards.name, name))
          .limit(1);

        if (existingBoard.length > 0) {
          return NextResponse.json(existingBoard[0]);
        }

        const [newBoard] = await db
          .insert(examBoards)
          .values({ id, name, code: code || null })
          .returning({ id: examBoards.id, name: examBoards.name, code: examBoards.code })
          .onConflictDoUpdate({
            target: examBoards.name,
            set: { updatedAt: new Date() },
          });
        result = [newBoard];
        break;

      case 'position':
        // First check if it already exists
        const existingPos = await db
          .select({ id: positions.id, name: positions.name, code: positions.code })
          .from(positions)
          .where(ilike(positions.name, name))
          .limit(1);

        if (existingPos.length > 0) {
          return NextResponse.json(existingPos[0]);
        }

        const [newPos] = await db
          .insert(positions)
          .values({ id, name, code: code || null })
          .returning({ id: positions.id, name: positions.name, code: positions.code })
          .onConflictDoUpdate({
            target: positions.name,
            set: { updatedAt: new Date() },
          });
        result = [newPos];
        break;

      case 'specialization':
        if (!parentId) {
          return NextResponse.json(
            { error: 'Position ID required for specialization' },
            { status: 400 }
          );
        }

        // First check if it already exists
        const existingSpec = await db
          .select({
            id: specializations.id,
            name: specializations.name,
            code: specializations.code,
          })
          .from(specializations)
          .where(and(eq(specializations.positionId, parentId), ilike(specializations.name, name)))
          .limit(1);

        if (existingSpec.length > 0) {
          return NextResponse.json(existingSpec[0]);
        }

        const [newSpec] = await db
          .insert(specializations)
          .values({ id, positionId: parentId, name, code: code || null })
          .returning({
            id: specializations.id,
            name: specializations.name,
            code: specializations.code,
          });
        result = [newSpec];
        break;

      case 'subject':
        // First check if it already exists
        const existingSubj = await db
          .select({ id: subjects.id, name: subjects.name })
          .from(subjects)
          .where(ilike(subjects.name, name))
          .limit(1);

        if (existingSubj.length > 0) {
          return NextResponse.json(existingSubj[0]);
        }

        const [newSubj] = await db
          .insert(subjects)
          .values({ id, name })
          .returning({ id: subjects.id, name: subjects.name })
          .onConflictDoUpdate({
            target: subjects.name,
            set: { updatedAt: new Date() },
          });
        result = [newSubj];
        break;

      case 'topic':
        if (!parentId) {
          return NextResponse.json({ error: 'Subject ID required for topic' }, { status: 400 });
        }

        // First check if it already exists
        const existingTopic = await db
          .select({ id: topics.id, name: topics.name })
          .from(topics)
          .where(and(eq(topics.subjectId, parentId), ilike(topics.name, name)))
          .limit(1);

        if (existingTopic.length > 0) {
          return NextResponse.json(existingTopic[0]);
        }

        const [newTopic] = await db
          .insert(topics)
          .values({ id, subjectId: parentId, name, level: 1, path: name })
          .returning({ id: topics.id, name: topics.name });
        result = [newTopic];
        break;

      default:
        return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 });
    }

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error('Create entity error:', error);
    return NextResponse.json({ error: 'Failed to create entity' }, { status: 500 });
  }
}
