import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { promises as fs } from 'fs';
import { v2 as cloudinary } from 'cloudinary';
import { z } from 'zod';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

const uploadImageSchema = z.object({
  local_path: z.string(),
  page_number: z.number(),
  hash: z.string(),
  folder: z.string().optional(),
  method: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const roleCheck = await requireRole(request, ROLES.ADMIN);
    if (roleCheck instanceof NextResponse) return roleCheck;

    const body = await request.json();
    const validatedData = uploadImageSchema.parse(body);

    const {
      local_path,
      page_number,
      hash,
      folder = 'memo-extracted-images',
      method = 'extracted',
    } = validatedData;

    // Check if file exists
    try {
      await fs.access(local_path);
    } catch {
      return NextResponse.json(
        {
          error: 'Local image file not found',
          details: 'The extracted image file may have been cleaned up or moved',
        },
        { status: 404 }
      );
    }

    // Upload to Cloudinary
    try {
      const uploadResult = await cloudinary.uploader.upload(local_path, {
        folder,
        public_id: `page_${page_number}_${hash.substring(0, 8)}`,
        context: {
          page: page_number.toString(),
          method,
          extraction_tool: 'advanced_python_extractor',
        },
        tags: ['pdf_extraction', 'memo_app', method],
      });

      // Clean up local file after successful upload
      try {
        await fs.unlink(local_path);
      } catch (cleanupError) {
        console.warn('Failed to clean up local file:', cleanupError);
      }

      return NextResponse.json({
        success: true,
        cloudinary_url: uploadResult.secure_url,
        public_id: uploadResult.public_id,
        uploaded: true,
      });
    } catch (uploadError) {
      console.error('Cloudinary upload failed:', uploadError);
      return NextResponse.json(
        {
          error: 'Failed to upload image to Cloudinary',
          details: uploadError instanceof Error ? uploadError.message : 'Unknown error',
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Upload image API error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: error.issues,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
