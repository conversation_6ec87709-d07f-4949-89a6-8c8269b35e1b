import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db';
import { modelSettings, type NewModelSettings } from '@/db/schema';
import { eq, desc } from 'drizzle-orm';

// GET /api/admin/model-settings - Get all model settings (admin only)
export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const settings = await db.select().from(modelSettings).orderBy(desc(modelSettings.createdAt));

    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error fetching model settings:', error);
    return NextResponse.json({ error: 'Failed to fetch model settings' }, { status: 500 });
  }
}

// POST /api/admin/model-settings - Create new model settings (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      name,
      systemPrompt,
      defaultModel,
      temperature,
      maxTokens,
      topP,
      frequencyPenalty,
      presencePenalty,
      reasoning,
      availableModels,
      isActive,
    } = body;

    // If setting as active, deactivate all others
    if (isActive) {
      await db
        .update(modelSettings)
        .set({ isActive: false })
        .where(eq(modelSettings.isActive, true));
    }

    const newSettings: NewModelSettings = {
      name,
      systemPrompt,
      defaultModel: defaultModel || 'openai/gpt-4o-mini',
      temperature: Math.round(temperature * 100), // Convert to int
      maxTokens,
      topP: Math.round(topP * 100), // Convert to int
      frequencyPenalty: Math.round(frequencyPenalty * 100), // Convert to int
      presencePenalty: Math.round(presencePenalty * 100), // Convert to int
      reasoning,
      availableModels: availableModels || [],
      isActive: isActive || false,
      createdBy: session.user.id,
    };

    const [settings] = await db.insert(modelSettings).values(newSettings).returning();

    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error creating model settings:', error);
    return NextResponse.json({ error: 'Failed to create model settings' }, { status: 500 });
  }
}

// Note: Active settings endpoint is at /api/model-settings/active
