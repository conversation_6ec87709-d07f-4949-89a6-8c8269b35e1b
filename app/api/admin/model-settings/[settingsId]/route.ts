import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db';
import { modelSettings } from '@/db/schema';
import { eq } from 'drizzle-orm';

interface RouteParams {
  params: {
    settingsId: string;
  };
}

// PATCH /api/admin/model-settings/[settingsId] - Update model settings (admin only)
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { settingsId } = await params;
    const body = await request.json();
    const {
      name,
      systemPrompt,
      defaultModel,
      temperature,
      maxTokens,
      topP,
      frequencyPenalty,
      presencePenalty,
      reasoning,
      availableModels,
      isActive,
    } = body;

    // If setting as active, deactivate all others
    if (isActive) {
      await db
        .update(modelSettings)
        .set({ isActive: false })
        .where(eq(modelSettings.isActive, true));
    }

    // Update settings
    const [updated] = await db
      .update(modelSettings)
      .set({
        ...(name !== undefined && { name }),
        ...(systemPrompt !== undefined && { systemPrompt }),
        ...(defaultModel !== undefined && { defaultModel }),
        ...(temperature !== undefined && { temperature: Math.round(temperature * 100) }),
        ...(maxTokens !== undefined && { maxTokens }),
        ...(topP !== undefined && { topP: Math.round(topP * 100) }),
        ...(frequencyPenalty !== undefined && {
          frequencyPenalty: Math.round(frequencyPenalty * 100),
        }),
        ...(presencePenalty !== undefined && {
          presencePenalty: Math.round(presencePenalty * 100),
        }),
        ...(reasoning !== undefined && { reasoning }),
        ...(availableModels !== undefined && { availableModels }),
        ...(isActive !== undefined && { isActive }),
        updatedAt: new Date(),
      })
      .where(eq(modelSettings.id, settingsId))
      .returning();

    if (!updated) {
      return NextResponse.json({ error: 'Settings not found' }, { status: 404 });
    }

    // Convert int values back to decimals
    return NextResponse.json({
      ...updated,
      temperature: updated.temperature / 100,
      topP: updated.topP / 100,
      frequencyPenalty: updated.frequencyPenalty / 100,
      presencePenalty: updated.presencePenalty / 100,
    });
  } catch (error) {
    console.error('Error updating model settings:', error);
    return NextResponse.json({ error: 'Failed to update model settings' }, { status: 500 });
  }
}

// DELETE /api/admin/model-settings/[settingsId] - Delete model settings (admin only)
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { settingsId } = await params;

    await db.delete(modelSettings).where(eq(modelSettings.id, settingsId));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting model settings:', error);
    return NextResponse.json({ error: 'Failed to delete model settings' }, { status: 500 });
  }
}
