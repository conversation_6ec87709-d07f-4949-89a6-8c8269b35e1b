import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { db } from '@/db/client';
import { promptTemplates } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';

const updateTemplateSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  feature: z.string().min(1).max(50),
  promptType: z.string().min(1).max(50),
  promptContent: z.string().min(1),
  modelName: z.string().default('gemini-2.5-flash'),
  isDefault: z.boolean().default(false),
  isEnabled: z.boolean().default(true),
  icon: z.string().optional(),
  displayOrder: z.number().optional(),
  metadata: z.any().optional(),
});

interface RouteParams {
  params: Promise<{ id: string }>;
}

// PUT /api/admin/prompts/[id] - Update a template
export async function PUT(req: NextRequest, { params }: RouteParams) {
  try {
    const roleCheck = await requireRole(req, ROLES.ADMIN);
    if (roleCheck instanceof NextResponse) return roleCheck;

    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json({ error: 'Template ID required' }, { status: 400 });
    }

    const body = await req.json();
    const validation = updateTemplateSchema.partial().safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request', details: validation.error.format() },
        { status: 400 }
      );
    }

    const data = validation.data;

    // Check if template exists and is not a system template
    const [existing] = await db
      .select()
      .from(promptTemplates)
      .where(eq(promptTemplates.id, id))
      .limit(1);

    if (!existing) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    if (existing.isSystem) {
      return NextResponse.json({ error: 'Cannot modify system templates' }, { status: 403 });
    }

    // If setting as default, unset other defaults for this feature and prompt type
    if (data.isDefault && data.feature && data.promptType) {
      await db
        .update(promptTemplates)
        .set({ isDefault: false })
        .where(
          and(
            eq(promptTemplates.feature, data.feature),
            eq(promptTemplates.promptType, data.promptType),
            eq(promptTemplates.isSystem, false)
          )
        );
    }

    const [updated] = await db
      .update(promptTemplates)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(promptTemplates.id, id))
      .returning();

    return NextResponse.json({
      success: true,
      template: updated,
    });
  } catch (error) {
    console.error('Failed to update template:', error);
    return NextResponse.json({ error: 'Failed to update template' }, { status: 500 });
  }
}

// DELETE /api/admin/prompts/[id] - Delete a template
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  try {
    const roleCheck = await requireRole(req, ROLES.ADMIN);
    if (roleCheck instanceof NextResponse) return roleCheck;

    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json({ error: 'Template ID required' }, { status: 400 });
    }

    // Check if template exists and is not a system template
    const [existing] = await db
      .select()
      .from(promptTemplates)
      .where(eq(promptTemplates.id, id))
      .limit(1);

    if (!existing) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    if (existing.isSystem) {
      return NextResponse.json({ error: 'Cannot delete system templates' }, { status: 403 });
    }

    await db.delete(promptTemplates).where(eq(promptTemplates.id, id));

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully',
    });
  } catch (error) {
    console.error('Failed to delete template:', error);
    return NextResponse.json({ error: 'Failed to delete template' }, { status: 500 });
  }
}
