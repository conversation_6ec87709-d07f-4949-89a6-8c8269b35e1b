import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES } from '@/lib/auth/features';
import { db } from '@/db/client';
import { promptTemplates } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';

const createTemplateSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  feature: z.string().min(1).max(50),
  promptType: z.string().min(1).max(50),
  promptContent: z.string().min(1),
  modelName: z.string().default('gemini-2.5-flash'),
  isDefault: z.boolean().default(false),
  isEnabled: z.boolean().default(true),
  icon: z.string().optional(),
  displayOrder: z.number().optional(),
  metadata: z.any().optional(),
});

// GET /api/admin/prompts - List all templates
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get filters from query params
    const { searchParams } = new URL(req.url);
    const feature = searchParams.get('feature');
    const promptType = searchParams.get('promptType');

    const baseQuery = db.select().from(promptTemplates);

    // Build where conditions
    const conditions = [];
    if (feature) {
      conditions.push(eq(promptTemplates.feature, feature));
    }
    if (promptType) {
      conditions.push(eq(promptTemplates.promptType, promptType));
    }

    const templates =
      conditions.length > 0
        ? await baseQuery
            .where(conditions.length === 1 ? conditions[0] : and(...conditions))
            .orderBy(
              promptTemplates.isSystem,
              promptTemplates.feature,
              promptTemplates.promptType,
              promptTemplates.displayOrder,
              promptTemplates.name
            )
        : await baseQuery.orderBy(
            promptTemplates.isSystem,
            promptTemplates.feature,
            promptTemplates.promptType,
            promptTemplates.displayOrder,
            promptTemplates.name
          );

    return NextResponse.json({
      success: true,
      templates,
    });
  } catch (error) {
    console.error('Failed to fetch templates:', error);
    return NextResponse.json({ error: 'Failed to fetch templates' }, { status: 500 });
  }
}

// POST /api/admin/prompts - Create a new template
export async function POST(req: NextRequest) {
  try {
    const roleCheck = await requireRole(req, ROLES.ADMIN);
    if (roleCheck instanceof NextResponse) return roleCheck;

    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const validation = createTemplateSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid request', details: validation.error.format() },
        { status: 400 }
      );
    }

    const data = validation.data;

    // If setting as default, unset other defaults for this feature and prompt type
    if (data.isDefault) {
      await db
        .update(promptTemplates)
        .set({ isDefault: false })
        .where(
          and(
            eq(promptTemplates.feature, data.feature),
            eq(promptTemplates.promptType, data.promptType),
            eq(promptTemplates.isSystem, false)
          )
        );
    }

    const [template] = await db
      .insert(promptTemplates)
      .values({
        ...data,
        createdBy: session.user.id,
        isSystem: false,
      })
      .returning();

    return NextResponse.json({
      success: true,
      template,
    });
  } catch (error) {
    console.error('Failed to create template:', error);
    return NextResponse.json({ error: 'Failed to create template' }, { status: 500 });
  }
}
