import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { users, userRoles } from '@/db/schema/auth';
import { eq } from 'drizzle-orm';
import { hashPassword } from '@/lib/auth/password';
import { z } from 'zod';

// Registration validation schema
const registerSchema = z.object({
  name: z.string().min(2, { error: 'Name must be at least 2 characters' }),
  email: z.email({ error: 'Invalid email address' }),
  password: z.string().min(8, { error: 'Password must be at least 8 characters' }),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate input
    const validatedData = registerSchema.parse(body);

    // Check if user already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.email, validatedData.email))
      .limit(1);

    if (existingUser.length > 0) {
      return NextResponse.json({ error: 'Usu<PERSON>rio já existe' }, { status: 409 });
    }

    // Hash password
    const passwordHash = await hashPassword(validatedData.password);

    // Generate a unique ID for the user (Auth.js uses varchar)
    const userId = crypto.randomUUID();

    // Create user with transaction
    await db.transaction(async (tx) => {
      // Create user
      await tx.insert(users).values({
        id: userId,
        email: validatedData.email,
        name: validatedData.name,
        passwordHash,
      });

      // Assign default role
      await tx.insert(userRoles).values({
        userId: userId,
        role: 'student_free',
      });

      return { userId };
    });

    return NextResponse.json({
      success: true,
      message: 'Registro realizado com sucesso. Faça login.',
    });
  } catch (error) {
    console.error('Registration error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.issues[0].message }, { status: 400 });
    }

    return NextResponse.json({ error: 'Falha ao registrar usuário' }, { status: 500 });
  }
}
