import { NextResponse } from 'next/server';
import { db } from '@/db';
import { modelSettings } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET /api/model-settings/active - Get active model settings (public)
export async function GET() {
  try {
    const [activeSettings] = await db
      .select()
      .from(modelSettings)
      .where(eq(modelSettings.isActive, true))
      .limit(1);

    if (!activeSettings) {
      return NextResponse.json(null);
    }

    // Convert int values back to decimals
    return NextResponse.json({
      ...activeSettings,
      temperature: activeSettings.temperature / 100,
      topP: activeSettings.topP / 100,
      frequencyPenalty: activeSettings.frequencyPenalty / 100,
      presencePenalty: activeSettings.presencePenalty / 100,
    });
  } catch (error) {
    console.error('Error fetching active model settings:', error);
    return NextResponse.json({ error: 'Failed to fetch active model settings' }, { status: 500 });
  }
}
