import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db';
import { conversations, type NewConversation } from '@/db/schema';
import { eq, desc, and } from 'drizzle-orm';

// GET /api/conversations - Get all conversations for the current user
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const includeArchived = searchParams.get('includeArchived') === 'true';

    const conditions = [eq(conversations.userId, session.user.id)];
    if (!includeArchived) {
      conditions.push(eq(conversations.archived, false));
    }

    const userConversations = await db
      .select()
      .from(conversations)
      .where(and(...conditions))
      .orderBy(desc(conversations.updatedAt));

    return NextResponse.json(userConversations);
  } catch (error) {
    console.error('Error fetching conversations:', error);
    return NextResponse.json({ error: 'Failed to fetch conversations' }, { status: 500 });
  }
}

// POST /api/conversations - Create a new conversation
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { title, model, systemPrompt, metadata } = body;

    const newConversation: NewConversation = {
      userId: session.user.id,
      title: title || 'New Chat',
      model: model || 'openai/gpt-4o-mini',
      systemPrompt,
      metadata,
    };

    const [conversation] = await db.insert(conversations).values(newConversation).returning();

    return NextResponse.json(conversation);
  } catch (error) {
    console.error('Error creating conversation:', error);
    return NextResponse.json({ error: 'Failed to create conversation' }, { status: 500 });
  }
}
