import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db';
import { conversations, messages, type NewMessage } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

interface RouteParams {
  params: {
    conversationId: string;
  };
}

// POST /api/conversations/[conversationId]/messages - Add message to conversation
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { conversationId } = await params;
    const body = await request.json();
    const { role, content, reasoning, model, metadata } = body;

    // Verify conversation ownership
    const [conversation] = await db
      .select()
      .from(conversations)
      .where(and(eq(conversations.id, conversationId), eq(conversations.userId, session.user.id)));

    if (!conversation) {
      return NextResponse.json({ error: 'Conversation not found' }, { status: 404 });
    }

    // Create message
    const newMessage: NewMessage = {
      conversationId,
      role,
      content,
      reasoning,
      model,
      metadata,
    };

    const [message] = await db.insert(messages).values(newMessage).returning();

    // Update conversation's updatedAt
    await db
      .update(conversations)
      .set({ updatedAt: new Date() })
      .where(eq(conversations.id, conversationId));

    return NextResponse.json(message);
  } catch (error) {
    console.error('Error creating message:', error);
    return NextResponse.json({ error: 'Failed to create message' }, { status: 500 });
  }
}
