import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db';
import { fsrsParameters } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(_request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // minReviews parameter kept for API compatibility but not used
    await _request.json();

    // Parameter optimization is not available in ts-fsrs
    // This endpoint is kept for API compatibility but returns a clear message
    return NextResponse.json(
      {
        success: false,
        error: 'Parameter optimization not available',
        message:
          'The ts-fsrs library does not include parameter optimization. Users will continue using default FSRS6 parameters which work well for most cases.',
        alternatives: [
          'Use the default FSRS6 parameters (recommended)',
          'Manually adjust parameters based on your experience',
          'Wait for future updates that may include optimization',
        ],
      },
      { status: 501 } // Not Implemented
    );
  } catch (error: unknown) {
    console.error('FSRS optimization error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check current parameters
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function GET(_request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's current parameters
    const params = await db
      .select()
      .from(fsrsParameters)
      .where(eq(fsrsParameters.userId, session.user.id))
      .limit(1);

    if (params.length === 0) {
      return NextResponse.json({
        has_custom_parameters: false,
        message: 'No custom parameters found. Using defaults.',
        optimization_available: false,
        note: 'FSRS6 default parameters work well for most users.',
      });
    }

    const p = params[0];
    const parameters = [
      p.w0,
      p.w1,
      p.w2,
      p.w3,
      p.w4,
      p.w5,
      p.w6,
      p.w7,
      p.w8,
      p.w9,
      p.w10,
      p.w11,
      p.w12,
      p.w13,
      p.w14,
      p.w15,
      p.w16,
      p.w17,
      p.w18,
      p.w19,
      p.w20,
    ];

    return NextResponse.json({
      has_custom_parameters: true,
      parameters,
      request_retention: p.requestRetention,
      last_updated: p.updatedAt,
      optimization_available: false,
      note: 'Parameters are using FSRS6 defaults. Optimization is not available with ts-fsrs.',
    });
  } catch (error: unknown) {
    console.error('Failed to get FSRS parameters:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
