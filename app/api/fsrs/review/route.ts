import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { FSRSService } from '@/lib/fsrs/service';
import { Rating } from '@/lib/fsrs/types';
import { updateUserReputation } from '@/lib/reputation';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { cardId, questionId, rating, seed } = await request.json();

    if ((!cardId && !questionId) || rating === undefined) {
      return NextResponse.json({ error: 'Campos obrigatórios ausentes' }, { status: 400 });
    }

    // Validate rating
    if (![Rating.Again, Rating.Hard, Rating.Good, Rating.Easy].includes(rating)) {
      return NextResponse.json({ error: 'Avaliação inválida' }, { status: 400 });
    }

    // Initialize service for the user
    const service = new FSRSService(session.user.id);
    await service.initialize();

    let actualCardId = cardId;

    // If no cardId provided (new card), create one first
    if (!cardId && questionId) {
      actualCardId = await service.createCard(questionId);
    }

    // Review the card
    const result = await service.reviewCard(actualCardId, rating, seed);

    // Award reputation for good/easy answers
    if (rating === Rating.Good || rating === Rating.Easy) {
      await updateUserReputation({
        userId: session.user.id,
        change: 5,
        reason: rating === Rating.Good ? 'question_answered_good' : 'question_answered_easy',
        relatedId: actualCardId,
        relatedType: 'fsrs_card',
      });
    }

    // Get the user's updated reputation if they earned points
    let userReputation = null;
    if (rating === Rating.Good || rating === Rating.Easy) {
      const { getUserReputation } = await import('@/lib/reputation');
      userReputation = await getUserReputation(session.user.id);
    }

    return NextResponse.json({
      ...result,
      cardId: actualCardId,
      userReputation,
    });
  } catch (error: unknown) {
    console.error('FSRS review error:', error);
    return NextResponse.json(
      {
        error: 'Falha ao revisar carta',
        details: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}
