import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { FSRSService } from '@/lib/fsrs/service';

export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { cardId, questionId, seed } = await req.json();

    if (!cardId && !questionId) {
      return NextResponse.json(
        { error: 'ID da carta ou ID da questão é obrigatório' },
        { status: 400 }
      );
    }

    const fsrsService = new FSRSService(session.user.id);
    await fsrsService.initialize();

    // For new cards (no cardId), use actual FSRS calculations
    if (!cardId) {
      if (!questionId) {
        return NextResponse.json(
          { error: 'ID da questão é obrigatório para novas cartas' },
          { status: 400 }
        );
      }

      console.log('[Schedule API] Getting options for new card:', { questionId, seed });

      // Use real FSRS calculations for new cards
      const schedulingOptions = await fsrsService.getSchedulingOptionsForNewCard(questionId, seed);

      console.log('[Schedule API] Returning options for new card:', schedulingOptions);

      return NextResponse.json({ schedulingOptions });
    }

    // Pass a seed for consistent fuzz calculation
    const schedulingOptions = await fsrsService.getSchedulingOptions(cardId, seed);

    return NextResponse.json({ schedulingOptions });
  } catch (error) {
    console.error('Error getting scheduling options:', error);
    return NextResponse.json({ error: 'Falha ao obter opções de agendamento' }, { status: 500 });
  }
}
