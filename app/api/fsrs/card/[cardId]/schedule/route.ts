import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { FSRSService } from '@/lib/fsrs/service';

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ cardId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { cardId } = await params;

    // Initialize service for the user
    const service = new FSRSService(session.user.id);
    await service.initialize();

    // Get scheduling options for the card
    const schedulingOptions = await service.getSchedulingOptions(cardId);

    return NextResponse.json({ schedulingInfo: schedulingOptions });
  } catch (error: unknown) {
    console.error('FSRS schedule info error:', error);
    return NextResponse.json(
      {
        error: 'Failed to get scheduling info',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
