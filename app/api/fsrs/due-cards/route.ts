import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { FSRSService } from '@/lib/fsrs/service';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '20', 10);

    // Initialize service for the user
    const service = new FSRSService(session.user.id);
    await service.initialize();

    // Get due cards
    const cards = await service.getDueCards(limit);

    return NextResponse.json({
      cards,
      count: cards.length,
    });
  } catch (error: unknown) {
    console.error('FSRS due cards error:', error);
    return NextResponse.json(
      {
        error: 'Failed to get due cards',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
