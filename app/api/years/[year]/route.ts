import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db/client';
import {
  civilServiceExams,
  civilServiceTests,
  institutions,
  examBoards,
  positions,
  questions,
  subjects,
} from '@/db/schema';
import { eq, sql, desc, and, asc } from 'drizzle-orm';

export async function GET(request: NextRequest, { params }: { params: Promise<{ year: string }> }) {
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { year } = await params;
  const yearNumber = parseInt(year);

  if (isNaN(yearNumber) || yearNumber < 1900 || yearNumber > new Date().getFullYear() + 5) {
    return NextResponse.json({ error: 'Invalid year' }, { status: 400 });
  }

  try {
    // Get overall statistics for the year
    const [stats] = await db
      .select({
        totalQuestions: sql<number>`count(distinct ${questions.id})`,
        totalExams: sql<number>`count(distinct ${civilServiceExams.id})`,
        totalInstitutions: sql<number>`count(distinct ${institutions.id})`,
        totalPositions: sql<number>`count(distinct ${positions.id})`,
      })
      .from(civilServiceExams)
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .where(eq(civilServiceExams.year, yearNumber));

    // Get monthly distribution (based on test application dates)
    const monthlyDistribution = await db
      .select({
        month: sql<number>`extract(month from ${civilServiceTests.applicationDate})`,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
      })
      .from(civilServiceExams)
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(
        and(
          eq(civilServiceExams.year, yearNumber),
          sql`${civilServiceTests.applicationDate} is not null`
        )
      )
      .groupBy(sql`extract(month from ${civilServiceTests.applicationDate})`)
      .orderBy(asc(sql`extract(month from ${civilServiceTests.applicationDate})`));

    // Fill in missing months with zeros
    const monthNames = [
      'Jan',
      'Fev',
      'Mar',
      'Abr',
      'Mai',
      'Jun',
      'Jul',
      'Ago',
      'Set',
      'Out',
      'Nov',
      'Dez',
    ];
    const monthlyData = monthNames.map((month, index) => {
      const data = monthlyDistribution.find((m) => m.month === index + 1);
      return {
        month,
        questionCount: data?.questionCount || 0,
        examCount: data?.examCount || 0,
      };
    });

    // Get top exam boards
    const topExamBoards = await db
      .select({
        id: examBoards.id,
        name: examBoards.name,
        code: examBoards.code,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
      })
      .from(civilServiceExams)
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(civilServiceExams.year, yearNumber))
      .groupBy(examBoards.id, examBoards.name, examBoards.code)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    const totalQuestionsForBoards = topExamBoards.reduce(
      (sum, board) => sum + board.questionCount,
      0
    );
    const topExamBoardsWithPercentage = topExamBoards.map((board) => ({
      id: board.id,
      name: board.code || board.name,
      questionCount: board.questionCount,
      examCount: board.examCount,
      percentage:
        totalQuestionsForBoards > 0
          ? Math.round((board.questionCount / totalQuestionsForBoards) * 1000) / 10
          : 0,
    }));

    // Get top institutions
    const topInstitutions = await db
      .select({
        id: institutions.id,
        name: institutions.name,
        code: institutions.code,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
      })
      .from(civilServiceExams)
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(civilServiceExams.year, yearNumber))
      .groupBy(institutions.id, institutions.name, institutions.code)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Get top positions
    const topPositions = await db
      .select({
        id: positions.id,
        name: positions.name,
        code: positions.code,
        institutionName: institutions.name,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(civilServiceExams)
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(civilServiceExams.year, yearNumber))
      .groupBy(positions.id, positions.name, positions.code, institutions.name)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Get subject distribution
    const subjectDistribution = await db
      .select({
        id: subjects.id,
        name: subjects.name,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(civilServiceExams)
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(subjects, eq(questions.subjectId, subjects.id))
      .where(eq(civilServiceExams.year, yearNumber))
      .groupBy(subjects.id, subjects.name)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    const totalQuestionsForSubjects = subjectDistribution.reduce(
      (sum, subject) => sum + subject.questionCount,
      0
    );
    const subjectDistributionWithPercentage = subjectDistribution.map((subject) => ({
      ...subject,
      percentage:
        totalQuestionsForSubjects > 0
          ? Math.round((subject.questionCount / totalQuestionsForSubjects) * 1000) / 10
          : 0,
    }));

    // Get major exams (those with the most questions)
    const majorExams = await db
      .select({
        id: civilServiceExams.id,
        institutionId: institutions.id,
        institutionName: institutions.name,
        institutionCode: institutions.code,
        examBoardId: examBoards.id,
        examBoardName: examBoards.name,
        examBoardCode: examBoards.code,
        positionId: positions.id,
        positionName: positions.name,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        applicationDate: sql<string>`min(${civilServiceTests.applicationDate})`,
      })
      .from(civilServiceExams)
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(civilServiceExams.year, yearNumber))
      .groupBy(
        civilServiceExams.id,
        institutions.id,
        institutions.name,
        institutions.code,
        examBoards.id,
        examBoards.name,
        examBoards.code,
        positions.id,
        positions.name
      )
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Format response
    const response = {
      year: yearNumber,
      totalQuestions: stats?.totalQuestions || 0,
      totalExams: stats?.totalExams || 0,
      totalInstitutions: stats?.totalInstitutions || 0,
      monthlyDistribution: monthlyData,
      topExamBoards: topExamBoardsWithPercentage,
      topInstitutions: topInstitutions.map((inst) => ({
        id: inst.id,
        name: inst.name,
        code: inst.code,
        acronym: inst.code,
        questionCount: inst.questionCount,
        examCount: inst.examCount,
      })),
      topPositions: topPositions.map((pos) => ({
        id: pos.id,
        name: pos.name,
        institution: pos.institutionName,
        questionCount: pos.questionCount,
      })),
      subjectDistribution: subjectDistributionWithPercentage,
      recentExams: majorExams.map((exam) => ({
        id: exam.id,
        institution: exam.institutionName,
        institutionId: exam.institutionId,
        examBoard: exam.examBoardCode || exam.examBoardName,
        examBoardId: exam.examBoardId,
        position: exam.positionName,
        positionId: exam.positionId,
        questionCount: exam.questionCount,
      })),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching year data:', error);
    return NextResponse.json({ error: 'Failed to fetch year data' }, { status: 500 });
  }
}
