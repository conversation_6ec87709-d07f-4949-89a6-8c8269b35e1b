import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db/client';
import {
  subjects,
  topics,
  questions,
  civilServiceTests,
  civilServiceExams,
  institutions,
  examBoards,
  positions,
} from '@/db/schema';
import { eq, sql, desc, and } from 'drizzle-orm';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = await params;

  try {
    // Get subject details
    const subjectResult = await db
      .select({
        id: subjects.id,
        name: subjects.name,
      })
      .from(subjects)
      .where(eq(subjects.id, id))
      .limit(1);

    if (subjectResult.length === 0) {
      return NextResponse.json({ error: 'Subject not found' }, { status: 404 });
    }

    const subject = subjectResult[0];

    // Get statistics
    const [stats] = await db
      .select({
        totalQuestions: sql<number>`count(distinct ${questions.id})`,
        totalTopics: sql<number>`count(distinct ${topics.id})`,
        totalExams: sql<number>`count(distinct ${civilServiceExams.id})`,
      })
      .from(subjects)
      .leftJoin(topics, eq(topics.subjectId, subjects.id))
      .innerJoin(questions, eq(questions.subjectId, subjects.id))
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .where(eq(subjects.id, id));

    // Get yearly trend
    const currentYear = new Date().getFullYear();
    const yearlyTrend = await db
      .select({
        year: civilServiceExams.year,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(subjects)
      .innerJoin(questions, eq(questions.subjectId, subjects.id))
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .where(and(eq(subjects.id, id), sql`${civilServiceExams.year} >= ${currentYear - 4}`))
      .groupBy(civilServiceExams.year)
      .orderBy(civilServiceExams.year);

    // Get top exam boards
    const topExamBoards = await db
      .select({
        id: examBoards.id,
        name: examBoards.name,
        code: examBoards.code,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(subjects)
      .innerJoin(questions, eq(questions.subjectId, subjects.id))
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .where(eq(subjects.id, id))
      .groupBy(examBoards.id, examBoards.name, examBoards.code)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    const totalQuestionsForBoards = topExamBoards.reduce(
      (sum, board) => sum + board.questionCount,
      0
    );
    const topExamBoardsWithPercentage = topExamBoards.map((board) => ({
      id: board.id,
      name: board.code || board.name,
      questionCount: board.questionCount,
      percentage:
        totalQuestionsForBoards > 0
          ? Math.round((board.questionCount / totalQuestionsForBoards) * 1000) / 10
          : 0,
    }));

    // Get top institutions
    const topInstitutions = await db
      .select({
        id: institutions.id,
        name: institutions.name,
        code: institutions.code,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        lastExam: sql<number>`max(${civilServiceExams.year})`,
      })
      .from(subjects)
      .innerJoin(questions, eq(questions.subjectId, subjects.id))
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .where(eq(subjects.id, id))
      .groupBy(institutions.id, institutions.name, institutions.code)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Get top positions
    const topPositions = await db
      .select({
        id: positions.id,
        name: positions.name,
        code: positions.code,
        institutionName: institutions.name,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(subjects)
      .innerJoin(questions, eq(questions.subjectId, subjects.id))
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .where(eq(subjects.id, id))
      .groupBy(positions.id, positions.name, positions.code, institutions.name)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Get topics
    const topicsList = await db
      .select({
        id: topics.id,
        name: topics.name,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(topics)
      .leftJoin(questions, eq(questions.topicId, topics.id))
      .where(eq(topics.subjectId, id))
      .groupBy(topics.id, topics.name)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(20);

    const totalQuestionsForTopics = topicsList.reduce((sum, topic) => sum + topic.questionCount, 0);
    const topicsWithPercentage = topicsList.map((topic) => ({
      id: topic.id,
      name: topic.name,
      questionCount: topic.questionCount,
      percentage:
        totalQuestionsForTopics > 0
          ? Math.round((topic.questionCount / totalQuestionsForTopics) * 1000) / 10
          : 0,
    }));

    // Get question type distribution
    const questionDistribution = await db
      .select({
        type: civilServiceTests.type,
        count: sql<number>`count(distinct ${questions.id})`,
      })
      .from(subjects)
      .innerJoin(questions, eq(questions.subjectId, subjects.id))
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(subjects.id, id))
      .groupBy(civilServiceTests.type);

    const totalQuestionsForTypes = questionDistribution.reduce((sum, type) => sum + type.count, 0);
    const questionDistributionFormatted = questionDistribution.map((dist) => ({
      type:
        dist.type === 'MULTIPLA_ESCOLHA'
          ? 'Múltipla Escolha'
          : dist.type === 'CERTO_ERRADO'
            ? 'Certo ou Errado'
            : 'Dissertativa',
      count: dist.count,
      percentage:
        totalQuestionsForTypes > 0
          ? Math.round((dist.count / totalQuestionsForTypes) * 1000) / 10
          : 0,
    }));

    // Format response
    const response = {
      id: subject.id,
      name: subject.name,
      totalQuestions: stats?.totalQuestions || 0,
      totalTopics: stats?.totalTopics || 0,
      totalExams: stats?.totalExams || 0,
      yearlyTrend: yearlyTrend.map((trend) => ({
        year: trend.year,
        questionCount: trend.questionCount,
      })),
      topExamBoards: topExamBoardsWithPercentage,
      topInstitutions: topInstitutions.map((inst) => ({
        id: inst.id,
        name: inst.name,
        code: inst.code,
        acronym: inst.code,
        questionCount: inst.questionCount,
        lastExam: inst.lastExam.toString(),
      })),
      topPositions: topPositions.map((pos) => ({
        id: pos.id,
        name: pos.name,
        institution: pos.institutionName,
        questionCount: pos.questionCount,
      })),
      topics: topicsWithPercentage,
      questionDistribution: questionDistributionFormatted,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching subject:', error);
    return NextResponse.json({ error: 'Failed to fetch subject' }, { status: 500 });
  }
}
