import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { createOpenRouterClient, ChatCompletionOptions } from '@/lib/openrouter/client';

// Extended usage type for OpenRouter responses
interface OpenRouterUsage {
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  cost?: number;
  reasoning_tokens?: number;
  cached_tokens?: number;
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const apiKey = process.env.OPENROUTER_API_KEY;
    if (!apiKey) {
      return NextResponse.json({ error: 'OpenRouter API key not configured' }, { status: 500 });
    }

    const body = (await request.json()) as ChatCompletionOptions;
    const {
      model,
      messages,
      temperature = 0.7,
      maxTokens = 1000,
      stream = false,
      reasoning,
      topP,
      frequencyPenalty,
      presencePenalty,
    } = body;

    const client = createOpenRouterClient(apiKey);

    if (stream) {
      const startTime = Date.now();
      let firstTokenTime: number | null = null;
      let tokenCount = 0;
      let streamUsage: OpenRouterUsage | null = null;
      let generationId: string | undefined;

      const streamResponse = await client.chat.completions.create({
        model: model || 'openai/gpt-4o-mini',
        messages,
        temperature,
        max_tokens: maxTokens,
        stream: true,
        stream_options: {
          include_usage: true,
        },
        ...(reasoning && { reasoning }),
        ...(topP !== undefined && { top_p: topP }),
        ...(frequencyPenalty !== undefined && { frequency_penalty: frequencyPenalty }),
        ...(presencePenalty !== undefined && { presence_penalty: presencePenalty }),
      } as Parameters<typeof client.chat.completions.create>[0]);

      const encoder = new TextEncoder();
      const readableStream = new ReadableStream({
        async start(controller) {
          try {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            for await (const chunk of streamResponse as any) {
              // Extract generation ID from headers if available
              if (!generationId && chunk.id) {
                generationId = chunk.id;
              }

              // Handle content chunks
              const content = chunk.choices[0]?.delta?.content || '';
              const reasoning = chunk.choices[0]?.delta?.reasoning || '';

              if (content || reasoning) {
                if (!firstTokenTime) {
                  firstTokenTime = Date.now();
                }

                const payload: Record<string, string> = {};
                if (content) {
                  tokenCount++;
                  payload.content = content;
                }
                if (reasoning) {
                  payload.reasoning = reasoning;
                }

                controller.enqueue(encoder.encode(`data: ${JSON.stringify(payload)}\n\n`));
              }

              // Handle usage data (comes at the end for streaming)
              if (chunk.usage) {
                streamUsage = chunk.usage;
              }
            }

            // Send metadata at the end
            const endTime = Date.now();
            const totalTime = (endTime - startTime) / 1000;
            const timeToFirst = firstTokenTime ? (firstTokenTime - startTime) / 1000 : 0;

            // Calculate tokens per second based on actual completion tokens
            const completionTokens = streamUsage?.completion_tokens || tokenCount;
            const tokensPerSecond = totalTime > 0 ? completionTokens / totalTime : 0;

            controller.enqueue(
              encoder.encode(
                `data: ${JSON.stringify({
                  metadata: {
                    model: model || 'openai/gpt-4o-mini',
                    usage: streamUsage
                      ? {
                          promptTokens: streamUsage.prompt_tokens,
                          completionTokens: streamUsage.completion_tokens,
                          totalTokens: streamUsage.total_tokens,
                          cost: streamUsage.cost,
                          reasoningTokens: streamUsage.reasoning_tokens,
                          cachedTokens: streamUsage.cached_tokens,
                        }
                      : {
                          completionTokens: tokenCount,
                        },
                    latency: {
                      timeToFirstToken: timeToFirst,
                      totalTime,
                    },
                    performance: {
                      tokensPerSecond,
                    },
                    generationId,
                  },
                })}\n\n`
              )
            );

            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
            controller.close();
          } catch (error) {
            controller.error(error);
          }
        },
      });

      return new Response(readableStream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          Connection: 'keep-alive',
        },
      });
    } else {
      const startTime = Date.now();

      const completion = (await client.chat.completions.create({
        model: model || 'openai/gpt-4o-mini',
        messages,
        temperature,
        max_tokens: maxTokens,
        stream: false,
        ...(reasoning && { reasoning }),
        ...(topP !== undefined && { top_p: topP }),
        ...(frequencyPenalty !== undefined && { frequency_penalty: frequencyPenalty }),
        ...(presencePenalty !== undefined && { presence_penalty: presencePenalty }),
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } as Parameters<typeof client.chat.completions.create>[0])) as any;

      const endTime = Date.now();
      const responseMessage = completion.choices[0]?.message;
      const usage = completion.usage as OpenRouterUsage | undefined;
      const totalTime = (endTime - startTime) / 1000;

      // Calculate tokens per second based on completion tokens
      const completionTokens = usage?.completion_tokens || 0;
      const tokensPerSecond =
        totalTime > 0 && completionTokens > 0 ? completionTokens / totalTime : 0;

      return NextResponse.json({
        content: responseMessage?.content || '',
        role: responseMessage?.role || 'assistant',
        metadata: {
          model: completion.model || model || 'openai/gpt-4o-mini',
          usage: usage
            ? {
                promptTokens: usage.prompt_tokens,
                completionTokens: usage.completion_tokens,
                totalTokens: usage.total_tokens,
                cost: usage.cost,
                reasoningTokens: usage.reasoning_tokens,
                cachedTokens: usage.cached_tokens,
              }
            : undefined,
          latency: {
            timeToFirstToken: totalTime, // For non-streaming, this is the same as total time
            totalTime,
          },
          performance: {
            tokensPerSecond,
          },
          generationId: completion.id,
        },
      });
    }
  } catch (error) {
    console.error('OpenRouter API error:', error);
    // Return more detailed error information
    if (error instanceof Error) {
      return NextResponse.json(
        {
          error: 'Failed to process chat request',
          details: error.message,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
        },
        { status: 500 }
      );
    }
    return NextResponse.json({ error: 'Failed to process chat request' }, { status: 500 });
  }
}
