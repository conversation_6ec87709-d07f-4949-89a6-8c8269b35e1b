import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db/client';
import {
  specializations,
  positions,
  civilServiceExams,
  civilServiceTests,
  institutions,
  questions,
  subjects,
} from '@/db/schema';
import { eq, sql, desc, and } from 'drizzle-orm';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = await params;

  try {
    // Get specialization details with position info
    const specializationResult = await db
      .select({
        id: specializations.id,
        name: specializations.name,
        code: specializations.code,
        positionId: specializations.positionId,
        positionName: positions.name,
        positionCode: positions.code,
      })
      .from(specializations)
      .innerJoin(positions, eq(specializations.positionId, positions.id))
      .where(eq(specializations.id, id))
      .limit(1);

    if (specializationResult.length === 0) {
      return NextResponse.json({ error: 'Specialization not found' }, { status: 404 });
    }

    const specialization = specializationResult[0];

    // Get statistics
    const [stats] = await db
      .select({
        totalQuestions: sql<number>`count(distinct ${questions.id})`,
        totalExams: sql<number>`count(distinct ${civilServiceExams.id})`,
        totalInstitutions: sql<number>`count(distinct ${institutions.id})`,
      })
      .from(specializations)
      .innerJoin(civilServiceExams, eq(civilServiceExams.specializationId, specializations.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .where(eq(specializations.id, id));

    // Get related positions (positions that have similar exams)
    const relatedPositions = await db
      .select({
        id: positions.id,
        name: positions.name,
        code: positions.code,
        institutionName: institutions.name,
        lastExam: sql<number>`max(${civilServiceExams.year})`,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(civilServiceExams)
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(civilServiceExams.specializationId, id))
      .groupBy(positions.id, positions.name, positions.code, institutions.name)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Get top subjects
    const topSubjects = await db
      .select({
        id: subjects.id,
        name: subjects.name,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(specializations)
      .innerJoin(civilServiceExams, eq(civilServiceExams.specializationId, specializations.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(subjects, eq(questions.subjectId, subjects.id))
      .where(eq(specializations.id, id))
      .groupBy(subjects.id, subjects.name)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    const totalQuestionsForSubjects = topSubjects.reduce(
      (sum, subject) => sum + subject.questionCount,
      0
    );
    const topSubjectsWithPercentage = topSubjects.map((subject) => ({
      ...subject,
      percentage:
        totalQuestionsForSubjects > 0
          ? Math.round((subject.questionCount / totalQuestionsForSubjects) * 1000) / 10
          : 0,
    }));

    // Get top institutions
    const topInstitutions = await db
      .select({
        id: institutions.id,
        name: institutions.name,
        code: institutions.code,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
        lastExam: sql<number>`max(${civilServiceExams.year})`,
      })
      .from(specializations)
      .innerJoin(civilServiceExams, eq(civilServiceExams.specializationId, specializations.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .where(eq(specializations.id, id))
      .groupBy(institutions.id, institutions.name, institutions.code)
      .orderBy(desc(sql`count(distinct ${civilServiceExams.id})`))
      .limit(10);

    // Get yearly growth
    const currentYear = new Date().getFullYear();
    const yearlyGrowth = await db
      .select({
        year: civilServiceExams.year,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
        positionCount: sql<number>`count(distinct ${positions.id})`,
      })
      .from(specializations)
      .innerJoin(civilServiceExams, eq(civilServiceExams.specializationId, specializations.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .where(and(eq(specializations.id, id), sql`${civilServiceExams.year} >= ${currentYear - 4}`))
      .groupBy(civilServiceExams.year)
      .orderBy(desc(civilServiceExams.year));

    // Calculate growth rate
    let growthRate = 0;
    if (yearlyGrowth.length >= 2) {
      const currentYearData = yearlyGrowth[0];
      const previousYearData = yearlyGrowth[1];
      if (previousYearData.questionCount > 0) {
        growthRate =
          Math.round(
            ((currentYearData.questionCount - previousYearData.questionCount) /
              previousYearData.questionCount) *
              1000
          ) / 10;
      }
    }

    // Format response
    const response = {
      id: specialization.id,
      name: specialization.name,
      code: specialization.code,
      position: {
        id: specialization.positionId,
        name: specialization.positionName,
      },
      totalQuestions: stats?.totalQuestions || 0,
      totalPositions: relatedPositions.length,
      totalExams: stats?.totalExams || 0,
      growthRate,
      relatedPositions: relatedPositions.map((pos) => ({
        id: pos.id,
        name: pos.name,
        institution: pos.institutionName,
        lastExam: pos.lastExam.toString(),
        questionCount: pos.questionCount,
      })),
      topSubjects: topSubjectsWithPercentage,
      topInstitutions: topInstitutions.map((inst) => ({
        id: inst.id,
        name: inst.name,
        code: inst.code,
        acronym: inst.code,
        examCount: inst.examCount,
        lastExam: inst.lastExam.toString(),
      })),
      yearlyGrowth: yearlyGrowth.map((year) => ({
        year: year.year,
        questionCount: year.questionCount,
        positionCount: year.positionCount,
      })),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching specialization:', error);
    return NextResponse.json({ error: 'Failed to fetch specialization' }, { status: 500 });
  }
}
