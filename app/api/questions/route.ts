import { auth } from '@/auth';
import { db } from '@/db';
import { questions } from '@/db/schema/questions';
import {
  civilServiceTests,
  civilServiceExams,
  examBoards,
  institutions,
  subjects,
  topics,
  positions,
  specializations,
} from '@/db/schema/exams';
import { questionOptions, questionKeys } from '@/db/schema/questions';
import { questionAssociatedTexts } from '@/db/schema/question-texts';
import { questionComments } from '@/db/schema';
import { and, eq, like, or, sql, desc, asc, inArray } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';
import { addOptionLettersWithCorrect } from '@/lib/utils/option-letters';

// GET /api/questions - Search and filter questions
export async function GET(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
  }

  try {
    const searchParams = request.nextUrl.searchParams;

    // Pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Filters
    const search = searchParams.get('search') || '';
    const examBoardFilter = searchParams.get('examBoard');
    const yearFilter = searchParams.get('year');
    const subjectId = searchParams.get('subjectId');
    const examId = searchParams.get('examId');

    // Sort
    const sortBy = searchParams.get('sortBy') || 'recent';

    // Build where conditions
    const conditions = [];

    if (search) {
      conditions.push(
        or(
          like(questions.stem, `%${search}%`),
          like(questionAssociatedTexts.content, `%${search}%`),
          like(institutions.name, `%${search}%`)
        )
      );
    }

    if (examBoardFilter) {
      conditions.push(
        or(eq(examBoards.code, examBoardFilter), eq(examBoards.name, examBoardFilter))
      );
    }

    if (yearFilter) {
      conditions.push(eq(civilServiceExams.year, parseInt(yearFilter)));
    }

    if (subjectId && subjectId !== 'all') {
      conditions.push(eq(questions.subjectId, subjectId));
    }

    if (examId) {
      conditions.push(eq(civilServiceTests.id, examId));
    }

    // Get total count
    const [{ count }] = await db
      .select({ count: sql<number>`count(*)` })
      .from(questions)
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .leftJoin(specializations, eq(civilServiceExams.specializationId, specializations.id))
      .leftJoin(subjects, eq(questions.subjectId, subjects.id))
      .leftJoin(topics, eq(questions.topicId, topics.id))
      .leftJoin(questionAssociatedTexts, eq(questions.associatedTextId, questionAssociatedTexts.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    // Get questions with exam details
    const orderBy =
      sortBy === 'recent'
        ? desc(questions.id)
        : sortBy === 'exam'
          ? [
              asc(sql`COALESCE(${examBoards.code}, ${examBoards.name})`),
              asc(civilServiceExams.year),
              asc(questions.number),
            ]
          : asc(questions.number);

    const questionList = await db
      .select({
        question: questions,
        test: civilServiceTests,
        exam: civilServiceExams,
        examBoard: examBoards,
        institution: institutions,
        position: positions,
        specialization: specializations,
        subject: subjects,
        topic: topics,
        associatedText: questionAssociatedTexts,
      })
      .from(questions)
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .leftJoin(specializations, eq(civilServiceExams.specializationId, specializations.id))
      .leftJoin(subjects, eq(questions.subjectId, subjects.id))
      .leftJoin(topics, eq(questions.topicId, topics.id))
      .leftJoin(questionAssociatedTexts, eq(questions.associatedTextId, questionAssociatedTexts.id))
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(...(Array.isArray(orderBy) ? orderBy : [orderBy]))
      .limit(limit)
      .offset(offset);

    // Get options and keys for all questions
    const questionIds = questionList.map((q) => q.question.id);
    const [options, keys, commentCounts] =
      questionIds.length > 0
        ? await Promise.all([
            db
              .select()
              .from(questionOptions)
              .where(inArray(questionOptions.questionId, questionIds)),
            db.select().from(questionKeys).where(inArray(questionKeys.questionId, questionIds)),
            // Get comment counts for all questions
            db
              .select({
                questionId: questionComments.questionId,
                count: sql<number>`cast(count(*) as integer)`,
              })
              .from(questionComments)
              .where(inArray(questionComments.questionId, questionIds))
              .groupBy(questionComments.questionId),
          ])
        : [[], [], []];

    // Group options by question
    const optionsByQuestion = options.reduce(
      (acc, opt) => {
        if (!acc[opt.questionId]) acc[opt.questionId] = [];
        acc[opt.questionId].push(opt);
        return acc;
      },
      {} as Record<string, typeof options>
    );

    // Create a map of correct answers by question
    const correctAnswersByQuestion = keys.reduce(
      (acc, key) => {
        acc[key.questionId] = key.correctAnswerId;
        return acc;
      },
      {} as Record<string, string | null>
    );

    // Create a map of comment counts by question
    const commentCountsByQuestion = commentCounts.reduce(
      (acc, row) => {
        acc[row.questionId] = row.count;
        return acc;
      },
      {} as Record<string, number>
    );

    // Format response
    const questionsWithOptions = questionList.map(
      ({
        question,
        test,
        exam,
        examBoard,
        institution,
        position,
        specialization,
        subject,
        topic,
        associatedText,
      }) => ({
        ...question,
        associatedText: associatedText?.content || null,
        associatedTextReferences: question.associatedTextReferences || null,
        options: addOptionLettersWithCorrect(
          optionsByQuestion[question.id] || [],
          correctAnswersByQuestion[question.id]
        ),
        commentCount: commentCountsByQuestion[question.id] ?? 0,
        exam: {
          id: test.id,
          name: institution.code ? `${institution.name} (${institution.code})` : institution.name,
          examBoard: examBoard.code || examBoard.name,
          examBoardId: examBoard.id,
          year: exam.year,
        },
        institution: {
          id: institution.id,
          name: institution.name,
          code: institution.code,
        },
        position: position ? { id: position.id, name: position.name } : null,
        specialization: specialization
          ? { id: specialization.id, name: specialization.name }
          : null,
        subject: subject ? { id: subject.id, name: subject.name } : null,
        topic: topic ? { id: topic.id, name: topic.name } : null,
      })
    );

    // Get unique filters for UI
    const [examBoardsList, years, subjectsList, exams] = await Promise.all([
      db
        .selectDistinct({
          examBoard: sql<string>`COALESCE(${examBoards.code}, ${examBoards.name})`,
        })
        .from(examBoards)
        .innerJoin(civilServiceExams, eq(civilServiceExams.examBoardId, examBoards.id))
        .innerJoin(
          civilServiceTests,
          eq(civilServiceTests.civilServiceExamId, civilServiceExams.id)
        )
        .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
        .orderBy(sql`COALESCE(${examBoards.code}, ${examBoards.name})`),

      db
        .selectDistinct({ year: civilServiceExams.year })
        .from(civilServiceExams)
        .innerJoin(
          civilServiceTests,
          eq(civilServiceTests.civilServiceExamId, civilServiceExams.id)
        )
        .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
        .orderBy(desc(civilServiceExams.year)),

      db
        .selectDistinct({ id: subjects.id, name: subjects.name })
        .from(subjects)
        .innerJoin(questions, eq(questions.subjectId, subjects.id))
        .orderBy(subjects.name),

      db
        .select({
          id: civilServiceTests.id,
          name: sql<string>`CASE WHEN ${institutions.code} IS NOT NULL THEN ${institutions.name} || ' (' || ${institutions.code} || ')' ELSE ${institutions.name} END`,
          examBoard: sql<string>`COALESCE(${examBoards.code}, ${examBoards.name})`,
          institutionCode: institutions.code,
          year: civilServiceExams.year,
        })
        .from(civilServiceTests)
        .innerJoin(
          civilServiceExams,
          eq(civilServiceTests.civilServiceExamId, civilServiceExams.id)
        )
        .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
        .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
        .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
        .groupBy(
          civilServiceTests.id,
          institutions.name,
          institutions.code,
          examBoards.code,
          examBoards.name,
          civilServiceExams.year
        )
        .orderBy(institutions.name, desc(civilServiceExams.year)),
    ]);

    return NextResponse.json({
      questions: questionsWithOptions,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
      },
      filters: {
        examBoards: examBoardsList
          .map((e) => e.examBoard)
          .filter(Boolean)
          .filter((value, index, self) => self.indexOf(value) === index),
        years: years.map((y) => y.year).filter(Boolean),
        subjects: subjectsList.filter((s) => s.id && s.name),
        exams,
      },
    });
  } catch (error) {
    console.error('Error searching questions:', error);
    return NextResponse.json({ error: 'Falha ao buscar questões' }, { status: 500 });
  }
}
