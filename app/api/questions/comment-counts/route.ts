import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { questionComments } from '@/db/schema';
import { inArray, sql } from 'drizzle-orm';

// GET /api/questions/comment-counts - Get comment counts for multiple questions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const questionIds = searchParams.get('ids')?.split(',').filter(Boolean);

    if (!questionIds || questionIds.length === 0) {
      return NextResponse.json({ error: 'No question IDs provided' }, { status: 400 });
    }

    // Get comment counts for all requested questions
    const commentCounts = await db
      .select({
        questionId: questionComments.questionId,
        count: sql<number>`count(*)`.as('count'),
      })
      .from(questionComments)
      .where(inArray(questionComments.questionId, questionIds))
      .groupBy(questionComments.questionId);

    // Create a map of questionId to count
    const countsMap: Record<string, number> = {};
    questionIds.forEach((id) => {
      countsMap[id] = 0; // Default to 0
    });

    commentCounts.forEach((row) => {
      countsMap[row.questionId] = row.count;
    });

    return NextResponse.json({ counts: countsMap });
  } catch (error) {
    console.error('Error fetching comment counts:', error);
    return NextResponse.json({ error: 'Failed to fetch comment counts' }, { status: 500 });
  }
}
