import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db';
import {
  questionComments,
  questionCommentVotes,
  users,
  commentEditNotifications,
} from '@/db/schema';
import { and, eq, desc, sql } from 'drizzle-orm';
import { alias } from 'drizzle-orm/pg-core';

// GET /api/questions/[questionId]/comments - Fetch comments for a question
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ questionId: string }> }
) {
  try {
    const session = await auth();
    const { questionId } = await params;

    // Create an alias for the votes subquery
    const userVotes = alias(questionCommentVotes, 'user_votes');

    // Fetch all comments for the question with user info and vote status
    const commentsData = await db
      .select({
        id: questionComments.id,
        content: questionComments.content,
        parentId: questionComments.parentId,
        createdAt: questionComments.createdAt,
        votes: questionComments.votes,
        userId: questionComments.userId,
        username: users.name,
        userAvatar: users.avatarUrl,
        userImage: users.image,
        userReputation: users.reputation,
        userVote: userVotes.voteType,
        deletedAt: questionComments.deletedAt,
        editHistory: questionComments.editHistory,
      })
      .from(questionComments)
      .leftJoin(users, eq(questionComments.userId, users.id))
      .leftJoin(
        userVotes,
        session?.user?.id
          ? and(eq(userVotes.commentId, questionComments.id), eq(userVotes.userId, session.user.id))
          : undefined
      )
      .where(eq(questionComments.questionId, questionId))
      .orderBy(desc(questionComments.createdAt));

    // Fetch edit notifications for all comments
    const editNotifications = await db
      .select()
      .from(commentEditNotifications)
      .where(
        sql`${commentEditNotifications.replyId} IN (
          SELECT id FROM ${questionComments} WHERE question_id = ${questionId}
        )`
      );

    // Create a map of edit notifications by reply ID
    const notificationsByReplyId = new Map<string, (typeof editNotifications)[0][]>();
    editNotifications.forEach((notification) => {
      if (!notificationsByReplyId.has(notification.replyId)) {
        notificationsByReplyId.set(notification.replyId, []);
      }
      notificationsByReplyId.get(notification.replyId)!.push(notification);
    });

    // Organize comments into a tree structure
    interface CommentWithReplies {
      id: string;
      userId: string;
      username: string;
      userAvatar?: string;
      userReputation: number;
      content: string;
      timestamp: Date;
      votes: number;
      userVote: 'up' | 'down' | null;
      parentId: string | null;
      replies: CommentWithReplies[];
      isDeleted: boolean;
      editHistory: string[];
      editNotifications?: Array<{
        editTimestamp: Date;
        commentId: string;
      }>;
    }

    const commentMap = new Map<string, CommentWithReplies>();
    const rootComments: CommentWithReplies[] = [];

    // First pass: create all comment objects
    commentsData.forEach((comment) => {
      const isDeleted = comment.deletedAt !== null;
      const editHistory = JSON.parse(comment.editHistory || '[]');
      const notifications = notificationsByReplyId.get(comment.id);

      const commentObj = {
        id: comment.id,
        userId: isDeleted ? '' : comment.userId,
        username: isDeleted ? 'Usuário anônimo' : comment.username || 'Anonymous',
        userAvatar: isDeleted ? undefined : comment.userAvatar || comment.userImage || undefined,
        userReputation: isDeleted ? 0 : comment.userReputation || 0,
        content: comment.content,
        timestamp: comment.createdAt,
        votes: comment.votes,
        userVote: comment.userVote as 'up' | 'down' | null,
        parentId: comment.parentId,
        replies: [],
        isDeleted,
        editHistory,
        editNotifications: notifications?.map((n) => ({
          editTimestamp: n.editTimestamp,
          commentId: n.commentId,
        })),
      };
      commentMap.set(comment.id, commentObj);
    });

    // Second pass: organize into tree structure
    commentMap.forEach((comment) => {
      if (comment.parentId) {
        const parent = commentMap.get(comment.parentId);
        if (parent) {
          parent.replies.push(comment);
        }
      } else {
        rootComments.push(comment);
      }
    });

    return NextResponse.json({ comments: rootComments });
  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json({ error: 'Failed to fetch comments' }, { status: 500 });
  }
}

// POST /api/questions/[questionId]/comments - Create a new comment
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ questionId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { content, parentId } = await request.json();
    const { questionId } = await params;

    if (!content || content.trim().length === 0) {
      return NextResponse.json({ error: 'Comment content is required' }, { status: 400 });
    }

    // Check if parent comment exists, belongs to the same question, and is not deleted
    if (parentId) {
      const [parentComment] = await db
        .select({
          questionId: questionComments.questionId,
          deletedAt: questionComments.deletedAt,
        })
        .from(questionComments)
        .where(eq(questionComments.id, parentId))
        .limit(1);

      if (!parentComment || parentComment.questionId !== questionId) {
        return NextResponse.json({ error: 'Invalid parent comment' }, { status: 400 });
      }

      if (parentComment.deletedAt) {
        return NextResponse.json({ error: 'Cannot reply to a deleted comment' }, { status: 403 });
      }

      // Check nesting level (max 3 levels)
      let currentParentId = parentId;
      let nestingLevel = 1;
      while (currentParentId && nestingLevel < 3) {
        const parent = await db
          .select({ parentId: questionComments.parentId })
          .from(questionComments)
          .where(eq(questionComments.id, currentParentId))
          .limit(1);

        if (parent.length && parent[0].parentId) {
          currentParentId = parent[0].parentId;
          nestingLevel++;
        } else {
          break;
        }
      }

      if (nestingLevel >= 3) {
        return NextResponse.json({ error: 'Maximum nesting level reached' }, { status: 400 });
      }
    }

    const [newComment] = await db
      .insert(questionComments)
      .values({
        questionId,
        userId: session.user.id,
        content: content.trim(),
        parentId: parentId || null,
      })
      .returning();

    // Fetch the complete comment with user info
    const [completeComment] = await db
      .select({
        id: questionComments.id,
        content: questionComments.content,
        parentId: questionComments.parentId,
        createdAt: questionComments.createdAt,
        votes: questionComments.votes,
        userId: questionComments.userId,
        username: users.name,
        userAvatar: users.avatarUrl,
        userImage: users.image,
        userReputation: users.reputation,
      })
      .from(questionComments)
      .leftJoin(users, eq(questionComments.userId, users.id))
      .where(eq(questionComments.id, newComment.id));

    const formattedComment = {
      id: completeComment.id,
      userId: completeComment.userId,
      username: completeComment.username || 'Anonymous',
      userAvatar: completeComment.userAvatar || completeComment.userImage || undefined,
      userReputation: completeComment.userReputation || 0,
      content: completeComment.content,
      timestamp: completeComment.createdAt,
      votes: completeComment.votes,
      userVote: null,
      parentId: completeComment.parentId,
      replies: [],
    };

    return NextResponse.json({ comment: formattedComment });
  } catch (error) {
    console.error('Error creating comment:', error);
    return NextResponse.json({ error: 'Failed to create comment' }, { status: 500 });
  }
}
