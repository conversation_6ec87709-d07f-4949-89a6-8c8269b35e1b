import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db';
import { fsrsCards, fsrsDecks, fsrsDeckCards } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { FSRSService } from '@/lib/fsrs/service';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ questionId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { questionId } = await params;
    const { answer, rating } = await request.json();

    // First, ensure the user has a default deck for standalone questions
    let defaultDeck = await db.query.fsrsDecks.findFirst({
      where: and(eq(fsrsDecks.userId, session.user.id), eq(fsrsDecks.title, 'Questões Avulsas')),
    });

    if (!defaultDeck) {
      // Create a default deck for standalone questions
      const [newDeck] = await db
        .insert(fsrsDecks)
        .values({
          userId: session.user.id,
          title: 'Questões Avulsas',
          description: 'Questões respondidas fora de um baralho específico',
          isActive: true,
        })
        .returning();
      defaultDeck = newDeck;
    }

    // Check if this question is already in the default deck
    let deckCard = await db.query.fsrsDeckCards.findFirst({
      where: and(
        eq(fsrsDeckCards.deckId, defaultDeck.id),
        eq(fsrsDeckCards.questionId, questionId)
      ),
    });

    if (!deckCard) {
      // Add question to default deck
      const [newDeckCard] = await db
        .insert(fsrsDeckCards)
        .values({
          deckId: defaultDeck.id,
          questionId,
        })
        .returning();
      deckCard = newDeckCard;
    }

    // Check if there's already an FSRS card for this question
    let card = await db.query.fsrsCards.findFirst({
      where: eq(fsrsCards.questionId, questionId),
    });

    const service = new FSRSService(session.user.id);
    await service.initialize();

    if (!card) {
      // Create a new FSRS card for this question
      const [newCard] = await db
        .insert(fsrsCards)
        .values({
          questionId,
          state: 0, // New card
          due: new Date(),
          stability: 0,
          difficulty: 0,
          elapsedDays: 0,
          scheduledDays: 0,
          learningSteps: 0,
          reps: 0,
          lapses: 0,
          lastReview: null,
        })
        .returning();

      card = newCard;
    }

    // Generate seed based on current minute for consistent intervals
    const now = new Date();
    const minuteTimestamp = Math.floor(now.getTime() / 60000) * 60000;
    const seed = `${questionId}-${minuteTimestamp}`;

    // Get scheduling options with seed
    const schedulingOptions =
      card.reps === 0
        ? await service.getSchedulingOptionsForNewCard(questionId, seed)
        : await service.getSchedulingOptions(card.id, seed);

    // If rating is provided, review the card
    if (rating !== undefined) {
      await service.reviewCard(card.id, rating);
    }

    return NextResponse.json({
      success: true,
      cardId: card.id,
      schedulingOptions, // Return raw scheduling options, not schedulingInfo
      answer,
    });
  } catch (error) {
    console.error('Question answer error:', error);
    return NextResponse.json(
      {
        error: 'Failed to process answer',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
