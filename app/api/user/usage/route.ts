import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getUserUsageStats } from '@/lib/middleware/usage-tracking';
import { getUserSubscription } from '@/lib/auth/rbac';

/**
 * GET /api/user/usage
 * Get current user's usage statistics
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function GET(_req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    // Get usage stats
    const usage = await getUserUsageStats(session.user.id);

    // Get subscription info
    const subscription = await getUserSubscription(session.user.id);

    return NextResponse.json({
      user: {
        id: session.user.id,
        email: session.user.email,
        tier: session.user.tier || 'free',
      },
      subscription: subscription
        ? {
            tier: subscription.tier,
            status: subscription.status,
            expiresAt: subscription.expiresAt,
            trialEndsAt: subscription.trialEndsAt,
          }
        : null,
      usage,
      features: session.user.features || [],
    });
  } catch (error) {
    console.error('Error fetching usage stats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
