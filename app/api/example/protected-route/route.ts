import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { requireRole } from '@/lib/auth/rbac';
import { ROLES, FEATURES } from '@/lib/auth/features';
import { trackUsage } from '@/lib/middleware/usage-tracking';
import { addSecurityHeaders } from '@/lib/auth/security';

/**
 * Example: Admin-only endpoint
 */
export async function GET(req: NextRequest) {
  // Check if user has admin role
  const roleCheck = await requireRole(req, ROLES.ADMIN);
  if (roleCheck instanceof NextResponse) return roleCheck;

  // Admin logic here
  const response = NextResponse.json({
    message: 'Admin access granted',
    data: {
      /* admin data */
    },
  });

  // Add security headers
  return addSecurityHeaders(response);
}

/**
 * Example: Feature-gated endpoint with usage tracking
 */
export async function POST(req: NextRequest) {
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Track usage for questions (applies limits for free tier)
  return trackUsage('questionsAnswered', 'questionsPerDay')(req, async () => {
    // Check if user has access to advanced analytics
    if (session.user.features?.includes(FEATURES.ADVANCED_ANALYTICS)) {
      // Return detailed analytics
      return NextResponse.json({
        message: 'Question answered with advanced analytics',
        analytics: {
          difficulty: 'hard',
          timeSpent: 120,
          accuracy: 0.85,
          weakAreas: ['topic1', 'topic2'],
        },
      });
    }

    // Return basic response for free tier
    return NextResponse.json({
      message: 'Question answered',
      correct: true,
    });
  });
}

/**
 * Example: Teacher-only content creation
 */
export async function PUT(req: NextRequest) {
  // Check if user has teacher role
  const roleCheck = await requireRole(req, ROLES.TEACHER);
  if (roleCheck instanceof NextResponse) return roleCheck;

  const session = await auth();
  const body = await req.json();

  // Teachers can create content
  return NextResponse.json({
    message: 'Content created successfully',
    createdBy: session!.user.email,
    content: body,
  });
}

/**
 * Example: Get user's current permissions and usage
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function DELETE(_req: NextRequest) {
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Return user's current access information
  return NextResponse.json({
    user: {
      id: session.user.id,
      email: session.user.email,
      role: session.user.role,
      tier: session.user.tier,
      features: session.user.features,
      subscription: session.user.subscription,
    },
    access: {
      isAdmin: session.user.role === ROLES.ADMIN,
      isTeacher: session.user.role === ROLES.TEACHER,
      isPremium: session.user.tier !== 'free',
      hasVideoAccess: session.user.features?.includes(FEATURES.VIDEO_EXPLANATIONS),
      hasPdfDownloads: session.user.features?.includes(FEATURES.PDF_DOWNLOADS),
    },
  });
}
