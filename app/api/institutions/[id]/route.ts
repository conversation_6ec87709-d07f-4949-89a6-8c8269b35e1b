import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db/client';
import {
  institutions,
  civilServiceExams,
  civilServiceTests,
  examBoards,
  positions,
  specializations,
  questions,
  subjects,
} from '@/db/schema';
import { eq, sql, desc, and } from 'drizzle-orm';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = await params;

  try {
    // Get institution details
    const institutionResult = await db
      .select({
        id: institutions.id,
        name: institutions.name,
        code: institutions.code,
      })
      .from(institutions)
      .where(eq(institutions.id, id))
      .limit(1);

    if (institutionResult.length === 0) {
      return NextResponse.json({ error: 'Institution not found' }, { status: 404 });
    }

    const institution = institutionResult[0];

    // Get statistics
    const [stats] = await db
      .select({
        totalQuestions: sql<number>`count(distinct ${questions.id})`,
        totalExams: sql<number>`count(distinct ${civilServiceExams.id})`,
        totalTests: sql<number>`count(distinct ${civilServiceTests.id})`,
        totalPositions: sql<number>`count(distinct ${positions.id})`,
        totalExamBoards: sql<number>`count(distinct ${examBoards.id})`,
      })
      .from(institutions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .where(eq(institutions.id, id));

    // Get exam boards used by this institution
    const examBoardsList = await db
      .select({
        id: examBoards.id,
        name: examBoards.name,
        code: examBoards.code,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        lastExam: sql<number>`max(${civilServiceExams.year})`,
      })
      .from(institutions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(institutions.id, id))
      .groupBy(examBoards.id, examBoards.name, examBoards.code)
      .orderBy(desc(sql`count(distinct ${questions.id})`));

    // Get positions offered by this institution
    const positionsList = await db
      .select({
        id: positions.id,
        name: positions.name,
        code: positions.code,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        lastExam: sql<number>`max(${civilServiceExams.year})`,
      })
      .from(institutions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(institutions.id, id))
      .groupBy(positions.id, positions.name, positions.code)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Get specializations
    const specializationsList = await db
      .select({
        id: specializations.id,
        name: specializations.name,
        code: specializations.code,
        positionId: specializations.positionId,
        positionName: positions.name,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(institutions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(specializations, eq(civilServiceExams.specializationId, specializations.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(institutions.id, id))
      .groupBy(
        specializations.id,
        specializations.name,
        specializations.code,
        specializations.positionId,
        positions.name
      )
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Get top subjects
    const topSubjects = await db
      .select({
        id: subjects.id,
        name: subjects.name,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(institutions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(subjects, eq(questions.subjectId, subjects.id))
      .where(eq(institutions.id, id))
      .groupBy(subjects.id, subjects.name)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    const totalQuestionsForSubjects = topSubjects.reduce(
      (sum, subject) => sum + subject.questionCount,
      0
    );
    const topSubjectsWithPercentage = topSubjects.map((subject) => ({
      ...subject,
      percentage:
        totalQuestionsForSubjects > 0
          ? Math.round((subject.questionCount / totalQuestionsForSubjects) * 1000) / 10
          : 0,
    }));

    // Get yearly statistics
    const currentYear = new Date().getFullYear();
    const yearlyStats = await db
      .select({
        year: civilServiceExams.year,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        positionCount: sql<number>`count(distinct ${positions.id})`,
      })
      .from(institutions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .where(and(eq(institutions.id, id), sql`${civilServiceExams.year} >= ${currentYear - 4}`))
      .groupBy(civilServiceExams.year)
      .orderBy(desc(civilServiceExams.year));

    // Get recent exams
    const recentExams = await db
      .select({
        id: civilServiceExams.id,
        year: civilServiceExams.year,
        positionId: positions.id,
        positionName: positions.name,
        specializationId: specializations.id,
        specializationName: specializations.name,
        examBoardId: examBoards.id,
        examBoardName: examBoards.name,
        examBoardCode: examBoards.code,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        testCount: sql<number>`count(distinct ${civilServiceTests.id})`,
      })
      .from(institutions)
      .innerJoin(civilServiceExams, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .leftJoin(specializations, eq(civilServiceExams.specializationId, specializations.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(institutions.id, id))
      .groupBy(
        civilServiceExams.id,
        civilServiceExams.year,
        positions.id,
        positions.name,
        specializations.id,
        specializations.name,
        examBoards.id,
        examBoards.name,
        examBoards.code
      )
      .orderBy(desc(civilServiceExams.year), desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Format response
    const response = {
      id: institution.id,
      name: institution.name,
      code: institution.code,
      fullName: institution.name,
      totalQuestions: stats?.totalQuestions || 0,
      totalExams: stats?.totalExams || 0,
      totalPositions: stats?.totalPositions || 0,
      totalExamBoards: stats?.totalExamBoards || 0,
      examBoards: examBoardsList.map((board) => ({
        id: board.id,
        name: board.code || board.name,
        fullName: board.name,
        examCount: board.examCount,
        questionCount: board.questionCount,
        lastExam: board.lastExam.toString(),
      })),
      positions: positionsList.map((position) => ({
        id: position.id,
        name: position.name,
        code: position.code,
        examCount: position.examCount,
        questionCount: position.questionCount,
        lastExam: position.lastExam.toString(),
      })),
      specializations: specializationsList.map((spec) => ({
        id: spec.id,
        name: spec.name,
        code: spec.code,
        positionId: spec.positionId,
        positionName: spec.positionName,
        questionCount: spec.questionCount,
      })),
      topSubjects: topSubjectsWithPercentage,
      yearlyStatistics: yearlyStats,
      recentExams: recentExams.map((exam) => ({
        id: exam.id,
        year: exam.year,
        position: exam.positionName,
        positionId: exam.positionId,
        specialization: exam.specializationName,
        specializationId: exam.specializationId,
        examBoard: exam.examBoardCode || exam.examBoardName,
        examBoardId: exam.examBoardId,
        questionCount: exam.questionCount,
        testCount: exam.testCount,
      })),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching institution:', error);
    return NextResponse.json({ error: 'Failed to fetch institution' }, { status: 500 });
  }
}
