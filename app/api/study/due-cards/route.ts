import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db';
import {
  fsrsDecks,
  fsrsDeckCards,
  questions,
  questionOptions,
  questionKeys,
  fsrsCards,
  civilServiceTests,
  civilServiceExams,
  examBoards,
  institutions,
  positions,
  specializations,
  subjects,
  topics,
  images,
  questionImages,
  questionComments,
  questionAssociatedTexts,
} from '@/db/schema';
import { eq, and, inArray, sql } from 'drizzle-orm';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function GET(_req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Get user's active decks
    const userDecks = await db
      .select()
      .from(fsrsDecks)
      .where(and(eq(fsrsDecks.userId, session.user.id), eq(fsrsDecks.isActive, true)));

    if (userDecks.length === 0) {
      return NextResponse.json({ questions: [], message: 'Nenhum baralho ativo encontrado' });
    }

    // Get deck cards
    const deckCardsList = await db
      .select({ questionId: fsrsDeckCards.questionId })
      .from(fsrsDeckCards)
      .where(
        inArray(
          fsrsDeckCards.deckId,
          userDecks.map((d) => d.id)
        )
      );

    if (deckCardsList.length === 0) {
      return NextResponse.json({ questions: [], message: 'Nenhuma carta nos baralhos' });
    }

    const questionIds = deckCardsList.map((dc) => dc.questionId);

    // Get existing FSRS cards for these questions
    const existingCards = await db
      .select()
      .from(fsrsCards)
      .where(inArray(fsrsCards.questionId, questionIds));

    const existingQuestionIds = new Set(existingCards.map((card) => card.questionId));

    // Find questions that don't have FSRS cards yet (new cards)
    const newQuestionIds = questionIds.filter((id) => !existingQuestionIds.has(id));

    // Get due cards (existing cards that are due for review)
    const now = new Date();
    const dueCards = existingCards.filter((card) => card.due <= now);

    // Combine due cards and new cards (prioritize due cards, then add new cards)
    const totalCardsNeeded = 20; // Study session size
    const dueCardsList = dueCards.slice(0, totalCardsNeeded);
    const remainingSlots = totalCardsNeeded - dueCardsList.length;

    // Add new cards if we have remaining slots
    const newCardsToAdd = newQuestionIds.slice(0, remainingSlots);

    // Get questions for due cards
    const dueQuestionIds = dueCardsList.map((card) => card.questionId);
    const allQuestionIdsToFetch = [...dueQuestionIds, ...newCardsToAdd];

    if (allQuestionIdsToFetch.length === 0) {
      // Get next review time from existing cards
      const nextCard = existingCards
        .filter((card) => card.due > now)
        .sort((a, b) => a.due.getTime() - b.due.getTime())[0];

      return NextResponse.json({
        questions: [],
        message: 'Nenhuma carta vencida',
        nextReview: nextCard?.due,
      });
    }

    // Fetch questions with exam information
    const questionsToStudy = await db
      .select({
        question: questions,
        test: civilServiceTests,
        exam: civilServiceExams,
        examBoard: examBoards,
        institution: institutions,
        position: positions,
        specialization: specializations,
        subject: subjects,
        topic: topics,
        associatedText: questionAssociatedTexts,
      })
      .from(questions)
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(positions, eq(civilServiceExams.positionId, positions.id))
      .innerJoin(specializations, eq(civilServiceExams.specializationId, specializations.id))
      .leftJoin(subjects, eq(questions.subjectId, subjects.id))
      .leftJoin(topics, eq(questions.topicId, topics.id))
      .leftJoin(questionAssociatedTexts, eq(questions.associatedTextId, questionAssociatedTexts.id))
      .where(inArray(questions.id, allQuestionIdsToFetch));

    // Create a map of existing cards by questionId for easy lookup
    const cardsByQuestionId = new Map(existingCards.map((card) => [card.questionId, card]));

    // Get all questionIds for bulk fetching
    const studyQuestionIds = questionsToStudy.map((q) => q.question.id);

    // Bulk fetch options, keys, images, and comment counts for all questions (4 queries instead of 4N)
    const [allOptions, allKeys, allImages, commentCounts] = await Promise.all([
      db
        .select()
        .from(questionOptions)
        .where(inArray(questionOptions.questionId, studyQuestionIds))
        .orderBy(questionOptions.questionId, questionOptions.order),
      db.select().from(questionKeys).where(inArray(questionKeys.questionId, studyQuestionIds)),
      db
        .select({
          questionId: questionImages.questionId,
          imageId: questionImages.imageId,
          order: questionImages.order,
          url: images.url,
          altText: images.altText,
        })
        .from(questionImages)
        .innerJoin(images, eq(questionImages.imageId, images.id))
        .where(inArray(questionImages.questionId, studyQuestionIds))
        .orderBy(questionImages.questionId, questionImages.order),
      db
        .select({
          questionId: questionComments.questionId,
          count: sql<number>`cast(count(*) as integer)`,
        })
        .from(questionComments)
        .where(inArray(questionComments.questionId, studyQuestionIds))
        .groupBy(questionComments.questionId),
    ]);

    // Group options, keys, and images by questionId for easy lookup
    const optionsByQuestion = allOptions.reduce(
      (acc, opt) => {
        if (!acc[opt.questionId]) acc[opt.questionId] = [];
        acc[opt.questionId].push(opt);
        return acc;
      },
      {} as Record<string, typeof allOptions>
    );

    const imagesByQuestion = allImages.reduce(
      (acc, img) => {
        if (!acc[img.questionId]) acc[img.questionId] = [];
        acc[img.questionId].push(img);
        return acc;
      },
      {} as Record<string, typeof allImages>
    );

    const keysByQuestion = allKeys.reduce(
      (acc, key) => {
        acc[key.questionId] = key;
        return acc;
      },
      {} as Record<string, (typeof allKeys)[0]>
    );

    const commentCountsByQuestion = commentCounts.reduce(
      (acc, row) => {
        acc[row.questionId] = row.count;
        return acc;
      },
      {} as Record<string, number>
    );

    // Map the questions without async operations
    const fullQuestions = questionsToStudy.map((row) => {
      const {
        question,
        test,
        exam,
        examBoard,
        institution,
        position,
        specialization,
        subject,
        topic,
        associatedText,
      } = row;

      // Get options, key, and images from pre-fetched data
      const options = optionsByQuestion[question.id] || [];
      const key = keysByQuestion[question.id];
      const images = imagesByQuestion[question.id] || [];

      const correctOption = key?.correctAnswerId
        ? options.find((opt) => opt.id === key.correctAnswerId)
        : null;

      // Check if this is an existing card or a new card
      const existingCard = cardsByQuestionId.get(question.id);

      return {
        cardId: existingCard?.id || null, // null for new cards
        questionId: question.id,
        questionNumber: question.number,
        // Exam information
        examBoard: examBoard.code || examBoard.name,
        examBoardId: examBoard.id,
        institution: institution.code
          ? `${institution.name} (${institution.code})`
          : institution.name,
        institutionId: institution.id,
        year: exam.year,
        position: position.name,
        positionId: position.id,
        specialization: specialization.name,
        specializationId: specialization.id,
        testType: test.type,
        booklet: test.booklet,
        // Use actual subject and topic if available
        subject: subject?.name || 'Sem Classificação',
        subjectId: subject?.id,
        topic: topic?.name || 'Sem Classificação',
        topicId: topic?.id,
        questionText: question.stem,
        associatedText: associatedText?.content || null,
        associatedTextReferences: question.associatedTextReferences || null,
        correctAnswer: correctOption ? String.fromCharCode(64 + correctOption.order) : undefined,
        options: options.map((opt) => ({
          letter: String.fromCharCode(64 + opt.order),
          text: opt.text,
          commentary: opt.commentary || undefined,
        })),
        images: images.map((img) => ({
          url: img.url,
          altText: img.altText,
          order: img.order,
        })),
        isNew: !existingCard, // Flag to indicate if this is a new card
        commentCount: commentCountsByQuestion[question.id] ?? 0,
      };
    });

    return NextResponse.json({ questions: fullQuestions });
  } catch (error) {
    console.error('Error fetching due cards:', error);
    return NextResponse.json({ error: 'Falha ao buscar cartas vencidas' }, { status: 500 });
  }
}
