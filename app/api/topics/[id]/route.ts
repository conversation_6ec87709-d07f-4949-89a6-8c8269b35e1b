import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db/client';
import {
  topics,
  subjects,
  questions,
  civilServiceTests,
  civilServiceExams,
  institutions,
  examBoards,
} from '@/db/schema';
import { eq, sql, desc, and, ne } from 'drizzle-orm';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = await params;

  try {
    // Get topic details with subject info
    const topicResult = await db
      .select({
        id: topics.id,
        name: topics.name,
        subjectId: topics.subjectId,
        subjectName: subjects.name,
      })
      .from(topics)
      .innerJoin(subjects, eq(topics.subjectId, subjects.id))
      .where(eq(topics.id, id))
      .limit(1);

    if (topicResult.length === 0) {
      return NextResponse.json({ error: 'Topic not found' }, { status: 404 });
    }

    const topic = topicResult[0];

    // Get statistics
    const [stats] = await db
      .select({
        totalQuestions: sql<number>`count(distinct ${questions.id})`,
        totalExams: sql<number>`count(distinct ${civilServiceExams.id})`,
      })
      .from(topics)
      .innerJoin(questions, eq(questions.topicId, topics.id))
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .where(eq(topics.id, id));

    // Get yearly distribution
    const currentYear = new Date().getFullYear();
    const yearlyDistribution = await db
      .select({
        year: civilServiceExams.year,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(topics)
      .innerJoin(questions, eq(questions.topicId, topics.id))
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .where(and(eq(topics.id, id), sql`${civilServiceExams.year} >= ${currentYear - 4}`))
      .groupBy(civilServiceExams.year)
      .orderBy(civilServiceExams.year);

    // Calculate growth rate
    let growthRate = 0;
    if (yearlyDistribution.length >= 2) {
      const currentYearData = yearlyDistribution[yearlyDistribution.length - 1];
      const previousYearData = yearlyDistribution[yearlyDistribution.length - 2];
      if (previousYearData.questionCount > 0) {
        growthRate =
          Math.round(
            ((currentYearData.questionCount - previousYearData.questionCount) /
              previousYearData.questionCount) *
              1000
          ) / 10;
      }
    }

    // Get exam boards distribution
    const examBoardsList = await db
      .select({
        id: examBoards.id,
        name: examBoards.name,
        code: examBoards.code,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(topics)
      .innerJoin(questions, eq(questions.topicId, topics.id))
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .where(eq(topics.id, id))
      .groupBy(examBoards.id, examBoards.name, examBoards.code)
      .orderBy(desc(sql`count(distinct ${questions.id})`));

    const totalQuestionsForBoards = examBoardsList.reduce(
      (sum, board) => sum + board.questionCount,
      0
    );
    const examBoardsWithPercentage = examBoardsList.map((board) => ({
      id: board.id,
      name: board.code || board.name,
      questionCount: board.questionCount,
      percentage:
        totalQuestionsForBoards > 0
          ? Math.round((board.questionCount / totalQuestionsForBoards) * 1000) / 10
          : 0,
    }));

    // Get top institutions
    const topInstitutions = await db
      .select({
        id: institutions.id,
        name: institutions.name,
        code: institutions.code,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        lastExam: sql<number>`max(${civilServiceExams.year})`,
      })
      .from(topics)
      .innerJoin(questions, eq(questions.topicId, topics.id))
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .where(eq(topics.id, id))
      .groupBy(institutions.id, institutions.name, institutions.code)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Get related topics (other topics from the same subject)
    const relatedTopics = await db
      .select({
        id: topics.id,
        name: topics.name,
        subjectName: subjects.name,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(topics)
      .innerJoin(subjects, eq(topics.subjectId, subjects.id))
      .leftJoin(questions, eq(questions.topicId, topics.id))
      .where(and(eq(topics.subjectId, topic.subjectId), ne(topics.id, id)))
      .groupBy(topics.id, topics.name, subjects.name)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Get keyword frequency from question stems (simplified - just count common words)
    const keywordData = await db
      .select({
        stem: questions.stem,
      })
      .from(topics)
      .innerJoin(questions, eq(questions.topicId, topics.id))
      .where(eq(topics.id, id))
      .limit(100);

    // Simple keyword extraction (in production, you'd want more sophisticated NLP)
    const commonWords = [
      'de',
      'a',
      'o',
      'que',
      'e',
      'do',
      'da',
      'em',
      'um',
      'para',
      'é',
      'com',
      'não',
      'uma',
      'os',
      'no',
      'se',
      'na',
      'por',
      'mais',
      'as',
      'dos',
      'como',
      'mas',
      'foi',
      'ao',
      'ele',
      'das',
      'tem',
      'à',
      'seu',
      'sua',
      'ou',
      'ser',
      'quando',
      'muito',
      'há',
      'nos',
      'já',
      'está',
      'eu',
      'também',
      'só',
      'pelo',
      'pela',
      'até',
      'isso',
      'ela',
      'entre',
      'era',
      'depois',
      'sem',
      'mesmo',
      'aos',
      'ter',
      'seus',
      'quem',
      'nas',
      'me',
      'esse',
      'eles',
      'estão',
      'você',
      'tinha',
      'foram',
      'essa',
      'num',
      'nem',
      'suas',
      'meu',
      'às',
      'minha',
      'têm',
      'numa',
      'pelos',
      'elas',
      'havia',
      'seja',
      'qual',
      'será',
      'nós',
      'tenho',
      'lhe',
      'deles',
      'essas',
      'esses',
      'pelas',
      'este',
      'fosse',
      'dele',
    ];

    const wordCount: Record<string, number> = {};
    keywordData.forEach(({ stem }) => {
      const words = stem.toLowerCase().split(/\s+/);
      words.forEach((word) => {
        const cleanWord = word.replace(/[^a-záàâãéèêíïóôõöúçñ]/gi, '');
        if (cleanWord.length > 3 && !commonWords.includes(cleanWord)) {
          wordCount[cleanWord] = (wordCount[cleanWord] || 0) + 1;
        }
      });
    });

    const keywordFrequency = Object.entries(wordCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 20)
      .map(([keyword, count]) => ({ keyword, count }));

    // Format response
    const response = {
      id: topic.id,
      name: topic.name,
      subject: {
        id: topic.subjectId,
        name: topic.subjectName,
      },
      totalQuestions: stats?.totalQuestions || 0,
      totalExams: stats?.totalExams || 0,
      growthRate,
      yearlyDistribution: yearlyDistribution.map((year) => ({
        year: year.year,
        questionCount: year.questionCount,
      })),
      examBoards: examBoardsWithPercentage,
      topInstitutions: topInstitutions.map((inst) => ({
        id: inst.id,
        name: inst.name,
        code: inst.code,
        acronym: inst.code,
        questionCount: inst.questionCount,
        lastExam: inst.lastExam.toString(),
      })),
      relatedTopics: relatedTopics.map((topic) => ({
        id: topic.id,
        name: topic.name,
        subject: topic.subjectName,
        questionCount: topic.questionCount,
      })),
      keywordFrequency,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching topic:', error);
    return NextResponse.json({ error: 'Failed to fetch topic' }, { status: 500 });
  }
}
