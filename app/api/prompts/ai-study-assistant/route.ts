import { NextResponse } from 'next/server';
import { db } from '@/db/client';
import { promptTemplates } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/prompts/ai-study-assistant - Get AI study assistant prompts
export async function GET() {
  try {
    // Fetch only enabled AI study assistant prompts
    const prompts = await db
      .select({
        id: promptTemplates.id,
        name: promptTemplates.name,
        promptContent: promptTemplates.promptContent,
        icon: promptTemplates.icon,
        displayOrder: promptTemplates.displayOrder,
        metadata: promptTemplates.metadata,
      })
      .from(promptTemplates)
      .where(
        and(
          eq(promptTemplates.feature, 'ai_study_assistant'),
          eq(promptTemplates.promptType, 'sample_prompts'),
          eq(promptTemplates.isEnabled, true)
        )
      )
      .orderBy(promptTemplates.displayOrder, promptTemplates.name);

    // Transform to the expected format for the frontend
    const transformedPrompts = prompts.map((p, index) => ({
      id: p.id,
      icon: p.icon || 'Sparkles',
      label: p.name,
      prompt: p.promptContent,
      order: p.displayOrder ?? index,
      enabled: true, // Only returning enabled prompts
    }));

    return NextResponse.json({ prompts: transformedPrompts });
  } catch (error) {
    console.error('Failed to fetch AI study assistant prompts:', error);
    return NextResponse.json({ error: 'Failed to fetch prompts' }, { status: 500 });
  }
}
