import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db';
import { questionComments, questionCommentVotes, users } from '@/db/schema';
import { and, eq, sql } from 'drizzle-orm';
import { updateUserReputation } from '@/lib/reputation';

// POST /api/comments/[commentId]/vote - Vote on a comment
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ commentId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { voteType } = await request.json();

    if (voteType !== 'up' && voteType !== 'down') {
      return NextResponse.json({ error: 'Invalid vote type' }, { status: 400 });
    }

    const { commentId } = await params;

    // Check if the comment exists and is not deleted
    const [comment] = await db
      .select()
      .from(questionComments)
      .where(eq(questionComments.id, commentId))
      .limit(1);

    if (!comment) {
      return NextResponse.json({ error: 'Comment not found' }, { status: 404 });
    }

    if (comment.deletedAt) {
      return NextResponse.json({ error: 'Cannot vote on deleted comments' }, { status: 403 });
    }

    // Check if any parent comment in the hierarchy is deleted
    if (comment.parentId) {
      let currentParentId: string | null = comment.parentId;
      while (currentParentId) {
        const [parentComment] = await db
          .select({
            parentId: questionComments.parentId,
            deletedAt: questionComments.deletedAt,
          })
          .from(questionComments)
          .where(eq(questionComments.id, currentParentId))
          .limit(1);

        if (parentComment?.deletedAt) {
          return NextResponse.json(
            { error: 'Cannot vote on replies of deleted comments' },
            { status: 403 }
          );
        }

        currentParentId = parentComment?.parentId || null;
      }
    }

    // Check if user has already voted
    const [existingVote] = await db
      .select()
      .from(questionCommentVotes)
      .where(
        and(
          eq(questionCommentVotes.commentId, commentId),
          eq(questionCommentVotes.userId, session.user.id)
        )
      )
      .limit(1);

    await db.transaction(async (tx) => {
      if (existingVote) {
        if (existingVote.voteType === voteType) {
          // Remove the vote
          await tx.delete(questionCommentVotes).where(eq(questionCommentVotes.id, existingVote.id));

          // Update vote count
          await tx
            .update(questionComments)
            .set({
              votes: sql`${questionComments.votes} ${voteType === 'up' ? sql`- 1` : sql`+ 1`}`,
            })
            .where(eq(questionComments.id, commentId));

          // Update reputation (undo previous vote)
          if (comment.userId !== session.user.id) {
            await updateUserReputation(
              {
                userId: comment.userId,
                change: voteType === 'up' ? -2 : 1, // Undo previous reputation change
                reason: voteType === 'up' ? 'comment_downvoted' : 'comment_upvoted',
                relatedId: commentId,
                relatedType: 'comment',
              },
              tx
            );
          }
        } else {
          // Change vote type
          await tx
            .update(questionCommentVotes)
            .set({ voteType })
            .where(eq(questionCommentVotes.id, existingVote.id));

          // Update vote count (remove old vote, add new vote)
          const adjustment = voteType === 'up' ? 2 : -2;
          await tx
            .update(questionComments)
            .set({
              votes: sql`${questionComments.votes} + ${adjustment}`,
            })
            .where(eq(questionComments.id, commentId));

          // Update reputation (change from one vote type to another)
          if (comment.userId !== session.user.id) {
            await updateUserReputation(
              {
                userId: comment.userId,
                change: voteType === 'up' ? 3 : -3, // +2-(-1) or -1-(+2)
                reason: voteType === 'up' ? 'comment_upvoted' : 'comment_downvoted',
                relatedId: commentId,
                relatedType: 'comment',
              },
              tx
            );
          }
        }
      } else {
        // Create new vote
        await tx.insert(questionCommentVotes).values({
          commentId,
          userId: session.user.id,
          voteType,
        });

        // Update vote count
        await tx
          .update(questionComments)
          .set({
            votes: sql`${questionComments.votes} ${voteType === 'up' ? sql`+ 1` : sql`- 1`}`,
          })
          .where(eq(questionComments.id, commentId));

        // Update reputation
        if (comment.userId !== session.user.id) {
          await updateUserReputation(
            {
              userId: comment.userId,
              change: voteType === 'up' ? 2 : -1,
              reason: voteType === 'up' ? 'comment_upvoted' : 'comment_downvoted',
              relatedId: commentId,
              relatedType: 'comment',
            },
            tx
          );
        }
      }
    });

    // Fetch updated comment with author's reputation
    const [updatedComment] = await db
      .select({
        votes: questionComments.votes,
        userId: questionComments.userId,
      })
      .from(questionComments)
      .where(eq(questionComments.id, commentId));

    // Fetch the comment author's updated reputation
    const [authorInfo] = await db
      .select({
        reputation: users.reputation,
      })
      .from(users)
      .where(eq(users.id, updatedComment.userId));

    // Fetch user's current vote
    const [userVote] = await db
      .select({
        voteType: questionCommentVotes.voteType,
      })
      .from(questionCommentVotes)
      .where(
        and(
          eq(questionCommentVotes.commentId, commentId),
          eq(questionCommentVotes.userId, session.user.id)
        )
      )
      .limit(1);

    return NextResponse.json({
      votes: updatedComment.votes,
      userVote: userVote?.voteType || null,
      authorUserId: updatedComment.userId,
      authorReputation: authorInfo?.reputation || 0,
    });
  } catch (error) {
    console.error('Error voting on comment:', error);
    return NextResponse.json({ error: 'Failed to vote on comment' }, { status: 500 });
  }
}
