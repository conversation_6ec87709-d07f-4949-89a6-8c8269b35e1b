import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db';
import { questionComments, commentEditNotifications } from '@/db/schema';
import { eq, and, sql } from 'drizzle-orm';

// DELETE /api/comments/[commentId] - Delete a comment
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ commentId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { commentId } = await params;

    // Check if the comment exists and belongs to the user
    const [comment] = await db
      .select()
      .from(questionComments)
      .where(and(eq(questionComments.id, commentId), eq(questionComments.userId, session.user.id)))
      .limit(1);

    if (!comment) {
      return NextResponse.json(
        { error: 'Comment not found or you do not have permission to delete it' },
        { status: 404 }
      );
    }

    // Check if comment has replies
    const [replyCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(questionComments)
      .where(eq(questionComments.parentId, commentId));

    if (replyCount.count > 0) {
      // Soft delete - preserve content but mark as deleted
      await db
        .update(questionComments)
        .set({
          deletedAt: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(questionComments.id, commentId));
    } else {
      // Hard delete - no replies, safe to remove completely
      await db.delete(questionComments).where(eq(questionComments.id, commentId));
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting comment:', error);
    return NextResponse.json({ error: 'Failed to delete comment' }, { status: 500 });
  }
}

// PATCH /api/comments/[commentId] - Edit a comment
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ commentId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { content } = await request.json();

    if (!content || content.trim().length === 0) {
      return NextResponse.json({ error: 'Comment content is required' }, { status: 400 });
    }

    const { commentId } = await params;

    // Check if the comment exists and belongs to the user
    const [comment] = await db
      .select()
      .from(questionComments)
      .where(and(eq(questionComments.id, commentId), eq(questionComments.userId, session.user.id)))
      .limit(1);

    if (!comment) {
      return NextResponse.json(
        { error: 'Comment not found or you do not have permission to edit it' },
        { status: 404 }
      );
    }

    const editTimestamp = new Date();

    // Get the current edit history
    const editHistory = JSON.parse(comment.editHistory || '[]');
    editHistory.push(editTimestamp.toISOString());

    // Update the comment
    const [updatedComment] = await db
      .update(questionComments)
      .set({
        content: content.trim(),
        updatedAt: editTimestamp,
        editHistory: JSON.stringify(editHistory),
      })
      .where(eq(questionComments.id, commentId))
      .returning();

    // Find replies that were made before this edit
    const repliesBeforeEdit = await db
      .select()
      .from(questionComments)
      .where(
        and(
          eq(questionComments.parentId, commentId),
          sql`${questionComments.createdAt} < ${editTimestamp.toISOString()}::timestamp`
        )
      );

    // Create edit notifications for each reply made before this edit
    if (repliesBeforeEdit.length > 0) {
      await db.insert(commentEditNotifications).values(
        repliesBeforeEdit.map((reply) => ({
          commentId: commentId,
          replyId: reply.id,
          editTimestamp: editTimestamp,
        }))
      );
    }

    return NextResponse.json({
      success: true,
      comment: updatedComment,
    });
  } catch (error) {
    console.error('Error editing comment:', error);
    return NextResponse.json({ error: 'Failed to edit comment' }, { status: 500 });
  }
}
