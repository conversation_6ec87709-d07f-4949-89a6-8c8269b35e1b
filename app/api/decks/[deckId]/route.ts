import { auth } from '@/auth';
import { db } from '@/db';
import { fsrsDecks, fsrsDeckCards, fsrsCards } from '@/db/schema/fsrs';
import { and, eq, inArray } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/decks/[deckId] - Get deck details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ deckId: string }> }
) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { deckId } = await params;

  try {
    const [deck] = await db
      .select()
      .from(fsrsDecks)
      .where(and(eq(fsrsDecks.id, deckId), eq(fsrsDecks.userId, session.user.id)));

    if (!deck) {
      return NextResponse.json({ error: 'Deck not found' }, { status: 404 });
    }

    return NextResponse.json(deck);
  } catch (error) {
    console.error('Error fetching deck:', error);
    return NextResponse.json({ error: 'Failed to fetch deck' }, { status: 500 });
  }
}

// PUT /api/decks/[deckId] - Update deck
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ deckId: string }> }
) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { deckId } = await params;

  try {
    const body = await request.json();
    const { title, description, isActive } = body;

    const [updatedDeck] = await db
      .update(fsrsDecks)
      .set({
        ...(title !== undefined && { title: title.trim() }),
        ...(description !== undefined && { description: description?.trim() || null }),
        ...(isActive !== undefined && { isActive }),
        updatedAt: new Date(),
      })
      .where(and(eq(fsrsDecks.id, deckId), eq(fsrsDecks.userId, session.user.id)))
      .returning();

    if (!updatedDeck) {
      return NextResponse.json({ error: 'Deck not found' }, { status: 404 });
    }

    return NextResponse.json(updatedDeck);
  } catch (error) {
    console.error('Error updating deck:', error);
    return NextResponse.json({ error: 'Failed to update deck' }, { status: 500 });
  }
}

// DELETE /api/decks/[deckId] - Delete deck
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ deckId: string }> }
) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { deckId } = await params;

  try {
    // Get all question IDs from the deck
    const deckQuestions = await db
      .select({ questionId: fsrsDeckCards.questionId })
      .from(fsrsDeckCards)
      .where(eq(fsrsDeckCards.deckId, deckId));

    // Delete FSRS cards for these questions
    if (deckQuestions.length > 0) {
      const questionIds = deckQuestions.map((q) => q.questionId);
      await db.delete(fsrsCards).where(inArray(fsrsCards.questionId, questionIds));
    }

    // Then delete deck cards
    await db.delete(fsrsDeckCards).where(eq(fsrsDeckCards.deckId, deckId));

    // Finally delete the deck
    const [deletedDeck] = await db
      .delete(fsrsDecks)
      .where(and(eq(fsrsDecks.id, deckId), eq(fsrsDecks.userId, session.user.id)))
      .returning();

    if (!deletedDeck) {
      return NextResponse.json({ error: 'Deck not found' }, { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting deck:', error);
    return NextResponse.json({ error: 'Failed to delete deck' }, { status: 500 });
  }
}
