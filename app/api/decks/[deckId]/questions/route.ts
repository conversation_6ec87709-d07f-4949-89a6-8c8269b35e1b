import { auth } from '@/auth';
import { db } from '@/db';
import { fsrsDecks, fsrsDeckCards } from '@/db/schema/fsrs';
import { questions } from '@/db/schema/questions';
import { civilServiceTests, civilServiceExams, examBoards, institutions } from '@/db/schema/exams';
import { questionOptions, questionKeys } from '@/db/schema/questions';
import { and, eq, inArray } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';
import { addOptionLettersWithCorrect } from '@/lib/utils/option-letters';

// GET /api/decks/[deckId]/questions - List questions in a deck
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ deckId: string }> }
) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { deckId } = await params;

  try {
    // Verify deck ownership
    const [deck] = await db
      .select()
      .from(fsrsDecks)
      .where(and(eq(fsrsDecks.id, deckId), eq(fsrsDecks.userId, session.user.id)));

    if (!deck) {
      return NextResponse.json({ error: 'Deck not found' }, { status: 404 });
    }

    // Get questions in the deck with exam details
    const deckQuestions = await db
      .select({
        questionId: questions.id,
        question: questions,
        test: civilServiceTests,
        exam: civilServiceExams,
        examBoard: examBoards,
        institution: institutions,
        deckCardId: fsrsDeckCards.id,
        addedAt: fsrsDeckCards.addedAt,
      })
      .from(fsrsDeckCards)
      .innerJoin(questions, eq(fsrsDeckCards.questionId, questions.id))
      .innerJoin(civilServiceTests, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(civilServiceExams, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(examBoards, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .where(eq(fsrsDeckCards.deckId, deckId))
      .orderBy(fsrsDeckCards.addedAt);

    // Get options and keys for all questions
    const questionIds = deckQuestions.map((q) => q.questionId);
    const [options, keys] =
      questionIds.length > 0
        ? await Promise.all([
            db
              .select()
              .from(questionOptions)
              .where(inArray(questionOptions.questionId, questionIds)),
            db.select().from(questionKeys).where(inArray(questionKeys.questionId, questionIds)),
          ])
        : [[], []];

    // Group options by question
    const optionsByQuestion = options.reduce(
      (acc, opt) => {
        if (!acc[opt.questionId]) acc[opt.questionId] = [];
        acc[opt.questionId].push(opt);
        return acc;
      },
      {} as Record<string, typeof options>
    );

    // Create a map of correct answers by question
    const correctAnswersByQuestion = keys.reduce(
      (acc, key) => {
        acc[key.questionId] = key.correctAnswerId;
        return acc;
      },
      {} as Record<string, string | null>
    );

    // Format response
    const questionsWithOptions = deckQuestions.map(
      ({ question, exam, examBoard, institution, deckCardId, addedAt }) => ({
        deckCardId,
        addedAt,
        question: {
          ...question,
          options: addOptionLettersWithCorrect(
            optionsByQuestion[question.id] || [],
            correctAnswersByQuestion[question.id]
          ),
          exam: {
            name: institution.code ? `${institution.name} (${institution.code})` : institution.name,
            examBoard: examBoard.code || examBoard.name,
            year: exam.year,
          },
        },
      })
    );

    return NextResponse.json({
      deck,
      questions: questionsWithOptions,
      totalQuestions: questionsWithOptions.length,
    });
  } catch (error) {
    console.error('Error fetching deck questions:', error);
    return NextResponse.json({ error: 'Failed to fetch deck questions' }, { status: 500 });
  }
}

// POST /api/decks/[deckId]/questions - Add questions to deck
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ deckId: string }> }
) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { deckId } = await params;

  try {
    const body = await request.json();
    const { questionIds } = body;

    if (!Array.isArray(questionIds) || questionIds.length === 0) {
      return NextResponse.json({ error: 'Question IDs are required' }, { status: 400 });
    }

    // Verify deck ownership
    const [deck] = await db
      .select()
      .from(fsrsDecks)
      .where(and(eq(fsrsDecks.id, deckId), eq(fsrsDecks.userId, session.user.id)));

    if (!deck) {
      return NextResponse.json({ error: 'Deck not found' }, { status: 404 });
    }

    // Check which questions are already in the deck
    const existingCards = await db
      .select({ questionId: fsrsDeckCards.questionId })
      .from(fsrsDeckCards)
      .where(and(eq(fsrsDeckCards.deckId, deckId), inArray(fsrsDeckCards.questionId, questionIds)));

    const existingQuestionIds = new Set(existingCards.map((c) => c.questionId));
    const newQuestionIds = questionIds.filter((id) => !existingQuestionIds.has(id));

    if (newQuestionIds.length === 0) {
      return NextResponse.json({
        message: 'All questions are already in the deck',
        added: 0,
      });
    }

    // Add new questions to deck
    const newCards = await db
      .insert(fsrsDeckCards)
      .values(
        newQuestionIds.map((questionId) => ({
          deckId: deckId,
          questionId,
        }))
      )
      .returning();

    return NextResponse.json(
      {
        message: 'Questions added successfully',
        added: newCards.length,
        skipped: existingQuestionIds.size,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error adding questions to deck:', error);
    return NextResponse.json({ error: 'Failed to add questions to deck' }, { status: 500 });
  }
}

// DELETE /api/decks/[deckId]/questions - Remove questions from deck
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ deckId: string }> }
) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { deckId } = await params;

  try {
    const body = await request.json();
    const { questionIds } = body;

    if (!Array.isArray(questionIds) || questionIds.length === 0) {
      return NextResponse.json({ error: 'Question IDs are required' }, { status: 400 });
    }

    // Verify deck ownership
    const [deck] = await db
      .select()
      .from(fsrsDecks)
      .where(and(eq(fsrsDecks.id, deckId), eq(fsrsDecks.userId, session.user.id)));

    if (!deck) {
      return NextResponse.json({ error: 'Deck not found' }, { status: 404 });
    }

    // Remove questions from deck
    await db
      .delete(fsrsDeckCards)
      .where(and(eq(fsrsDeckCards.deckId, deckId), inArray(fsrsDeckCards.questionId, questionIds)));

    return NextResponse.json({
      message: 'Questions removed successfully',
      removed: questionIds.length,
    });
  } catch (error) {
    console.error('Error removing questions from deck:', error);
    return NextResponse.json({ error: 'Failed to remove questions from deck' }, { status: 500 });
  }
}
