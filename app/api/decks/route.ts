import { auth } from '@/auth';
import { db } from '@/db';
import { fsrsDecks } from '@/db/schema/fsrs';
import { eq } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';

// GET /api/decks - List user's decks
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function GET(_request: NextRequest) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
  }

  try {
    const decks = await db
      .select()
      .from(fsrsDecks)
      .where(eq(fsrsDecks.userId, session.user.id))
      .orderBy(fsrsDecks.updatedAt);

    return NextResponse.json(decks);
  } catch (error) {
    console.error('Error fetching decks:', error);
    return NextResponse.json({ error: 'Falha ao buscar baralhos' }, { status: 500 });
  }
}

// POST /api/decks - Create new deck
export async function POST(request: NextRequest) {
  const session = await auth();

  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { title, description } = body;

    if (!title || typeof title !== 'string') {
      return NextResponse.json({ error: 'Título é obrigatório' }, { status: 400 });
    }

    const [newDeck] = await db
      .insert(fsrsDecks)
      .values({
        userId: session.user.id,
        title: title.trim(),
        description: description?.trim() || null,
        isActive: true,
      })
      .returning();

    return NextResponse.json(newDeck, { status: 201 });
  } catch (error) {
    console.error('Error creating deck:', error);
    return NextResponse.json({ error: 'Falha ao criar baralho' }, { status: 500 });
  }
}
