import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { db } from '@/db/client';
import {
  examBoards,
  civilServiceExams,
  civilServiceTests,
  institutions,
  questions,
  subjects,
} from '@/db/schema';
import { eq, sql, desc, and } from 'drizzle-orm';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const session = await auth();
  if (!session?.user?.id) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { id } = await params;

  try {
    // Get exam board details
    const examBoardResult = await db
      .select({
        id: examBoards.id,
        name: examBoards.name,
        code: examBoards.code,
      })
      .from(examBoards)
      .where(eq(examBoards.id, id))
      .limit(1);

    if (examBoardResult.length === 0) {
      return NextResponse.json({ error: 'Exam board not found' }, { status: 404 });
    }

    const examBoard = examBoardResult[0];

    // Get statistics
    const [stats] = await db
      .select({
        totalQuestions: sql<number>`count(distinct ${questions.id})`,
        totalExams: sql<number>`count(distinct ${civilServiceExams.id})`,
        totalTests: sql<number>`count(distinct ${civilServiceTests.id})`,
        totalInstitutions: sql<number>`count(distinct ${institutions.id})`,
      })
      .from(examBoards)
      .innerJoin(civilServiceExams, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .where(eq(examBoards.id, id));

    // Get question types distribution
    const questionTypes = await db
      .select({
        type: civilServiceTests.type,
        count: sql<number>`count(distinct ${questions.id})`,
      })
      .from(examBoards)
      .innerJoin(civilServiceExams, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(examBoards.id, id))
      .groupBy(civilServiceTests.type);

    const totalQuestionsForTypes = questionTypes.reduce((sum, type) => sum + type.count, 0);
    const questionTypesWithPercentage = questionTypes.map((type) => ({
      type:
        type.type === 'MULTIPLA_ESCOLHA'
          ? 'Múltipla Escolha'
          : type.type === 'CERTO_ERRADO'
            ? 'Certo ou Errado'
            : 'Discursiva',
      count: type.count,
      percentage:
        totalQuestionsForTypes > 0
          ? Math.round((type.count / totalQuestionsForTypes) * 1000) / 10
          : 0,
    }));

    // Get top institutions
    const topInstitutions = await db
      .select({
        id: institutions.id,
        name: institutions.name,
        code: institutions.code,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        lastExam: sql<number>`max(${civilServiceExams.year})`,
      })
      .from(examBoards)
      .innerJoin(civilServiceExams, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(examBoards.id, id))
      .groupBy(institutions.id, institutions.name, institutions.code)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Get top subjects
    const topSubjects = await db
      .select({
        id: subjects.id,
        name: subjects.name,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(examBoards)
      .innerJoin(civilServiceExams, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .innerJoin(subjects, eq(questions.subjectId, subjects.id))
      .where(eq(examBoards.id, id))
      .groupBy(subjects.id, subjects.name)
      .orderBy(desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    const totalQuestionsForSubjects = topSubjects.reduce(
      (sum, subject) => sum + subject.questionCount,
      0
    );
    const topSubjectsWithPercentage = topSubjects.map((subject) => ({
      ...subject,
      percentage:
        totalQuestionsForSubjects > 0
          ? Math.round((subject.questionCount / totalQuestionsForSubjects) * 1000) / 10
          : 0,
    }));

    // Get yearly statistics
    const currentYear = new Date().getFullYear();
    const yearlyStats = await db
      .select({
        year: civilServiceExams.year,
        questionCount: sql<number>`count(distinct ${questions.id})`,
        examCount: sql<number>`count(distinct ${civilServiceExams.id})`,
      })
      .from(examBoards)
      .innerJoin(civilServiceExams, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(and(eq(examBoards.id, id), sql`${civilServiceExams.year} >= ${currentYear - 4}`))
      .groupBy(civilServiceExams.year)
      .orderBy(desc(civilServiceExams.year));

    // Get recent exams
    const recentExams = await db
      .select({
        id: civilServiceExams.id,
        institutionId: institutions.id,
        institutionName: institutions.name,
        institutionCode: institutions.code,
        year: civilServiceExams.year,
        questionCount: sql<number>`count(distinct ${questions.id})`,
      })
      .from(examBoards)
      .innerJoin(civilServiceExams, eq(civilServiceExams.examBoardId, examBoards.id))
      .innerJoin(institutions, eq(civilServiceExams.institutionId, institutions.id))
      .innerJoin(civilServiceTests, eq(civilServiceTests.civilServiceExamId, civilServiceExams.id))
      .innerJoin(questions, eq(questions.civilServiceTestId, civilServiceTests.id))
      .where(eq(examBoards.id, id))
      .groupBy(
        civilServiceExams.id,
        institutions.id,
        institutions.name,
        institutions.code,
        civilServiceExams.year
      )
      .orderBy(desc(civilServiceExams.year), desc(sql`count(distinct ${questions.id})`))
      .limit(10);

    // Format response
    const response = {
      id: examBoard.id,
      name: examBoard.code || examBoard.name,
      fullName: examBoard.name,
      code: examBoard.code,
      totalQuestions: stats?.totalQuestions || 0,
      totalExams: stats?.totalExams || 0,
      totalInstitutions: stats?.totalInstitutions || 0,
      questionTypes: questionTypesWithPercentage,
      topInstitutions: topInstitutions.map((inst) => ({
        id: inst.id,
        name: inst.name,
        code: inst.code,
        questionCount: inst.questionCount,
        lastExam: inst.lastExam.toString(),
      })),
      topSubjects: topSubjectsWithPercentage,
      yearlyStatistics: yearlyStats,
      recentExams: recentExams.map((exam) => ({
        id: exam.id,
        institution: exam.institutionName,
        institutionId: exam.institutionId,
        institutionCode: exam.institutionCode,
        year: exam.year,
        questionCount: exam.questionCount,
      })),
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching exam board:', error);
    return NextResponse.json({ error: 'Failed to fetch exam board' }, { status: 500 });
  }
}
