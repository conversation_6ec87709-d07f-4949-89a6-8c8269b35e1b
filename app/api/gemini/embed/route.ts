import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { trackUsage } from '@/lib/middleware/usage-tracking';
import { getGeminiService } from '@/lib/gemini/service';
import type { GeminiEmbedRequest } from '@/lib/gemini/types';
import { z } from 'zod';

const embedRequestSchema = z.object({
  text: z.string().min(1).max(10000),
  model: z.string().optional(),
});

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await req.json();

    const validation = embedRequestSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Solicitação inválida', details: validation.error.format() },
        { status: 400 }
      );
    }

    const request: GeminiEmbedRequest = validation.data;

    return trackUsage('aiEmbedding', 'aiEmbeddingsPerDay')(req, async () => {
      try {
        const geminiService = getGeminiService();
        const response = await geminiService.generateEmbedding(request);

        return NextResponse.json({
          success: true,
          data: {
            embedding: response.embedding,
            tokenCount: response.tokenCount,
          },
        });
      } catch (error) {
        console.error('Gemini embedding error:', error);

        if (error instanceof Error && 'code' in error) {
          const errorWithCode = error as Error & { code: string };
          if (errorWithCode.code === 'INVALID_API_KEY') {
            return NextResponse.json(
              { error: 'Erro de configuração da chave API' },
              { status: 500 }
            );
          }

          if (errorWithCode.code === 'QUOTA_EXCEEDED') {
            return NextResponse.json({ error: 'Cota da API excedida' }, { status: 429 });
          }
        }

        return NextResponse.json(
          {
            error: 'Falha ao gerar embedding',
            details: error instanceof Error ? error.message : 'Erro desconhecido',
          },
          { status: 500 }
        );
      }
    });
  } catch (error) {
    console.error('Route error:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
