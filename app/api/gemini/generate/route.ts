import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { trackUsage } from '@/lib/middleware/usage-tracking';
import { getGeminiService } from '@/lib/gemini/service';
import type { GeminiGenerateRequest } from '@/lib/gemini/types';
import { z } from 'zod';

const generateRequestSchema = z.object({
  prompt: z.string().min(1).max(10000),
  systemInstruction: z.string().optional(),
  temperature: z.number().min(0).max(2).optional(),
  maxOutputTokens: z.number().min(1).max(8192).optional(),
  topK: z.number().min(1).max(100).optional(),
  topP: z.number().min(0).max(1).optional(),
});

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const body = await req.json();

    const validation = generateRequestSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Solicitação inválida', details: validation.error.format() },
        { status: 400 }
      );
    }

    const request: GeminiGenerateRequest = validation.data;

    return trackUsage('aiGeneration', 'aiGenerationsPerDay')(req, async () => {
      try {
        const geminiService = getGeminiService();
        const response = await geminiService.generateContent(request);

        return NextResponse.json({
          success: true,
          data: {
            text: response.text,
            finishReason: response.finishReason,
            tokenCount: response.tokenCount,
          },
        });
      } catch (error) {
        console.error('Gemini generation error:', error);

        if (error instanceof Error && 'code' in error) {
          const errorWithCode = error as Error & { code: string };
          if (errorWithCode.code === 'INVALID_API_KEY') {
            return NextResponse.json(
              { error: 'Erro de configuração da chave API' },
              { status: 500 }
            );
          }

          if (errorWithCode.code === 'QUOTA_EXCEEDED') {
            return NextResponse.json({ error: 'Cota da API excedida' }, { status: 429 });
          }

          if (errorWithCode.code === 'CONTENT_BLOCKED') {
            return NextResponse.json(
              { error: 'Conteúdo foi bloqueado pelos filtros de segurança' },
              { status: 400 }
            );
          }
        }

        return NextResponse.json(
          {
            error: 'Falha ao gerar conteúdo',
            details: error instanceof Error ? error.message : 'Erro desconhecido',
          },
          { status: 500 }
        );
      }
    });
  } catch (error) {
    console.error('Route error:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
