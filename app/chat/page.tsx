'use client';

import { useState, useEffect } from 'react';
import { ChatBot } from '@/components/chat-bot';
import { ChatSidebar } from '@/components/chat-sidebar';
import { useConversations } from '@/hooks/use-conversations';
import { ChatMessage } from '@/lib/openrouter/client';
import { ModelConfig } from '@/lib/openrouter/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useAuth } from '@/hooks/use-auth';
import { ConversationEvents, dispatchConversationEvent } from '@/lib/events';
import { SidebarInset } from '@/components/ui/sidebar';

export default function ChatPage() {
  const { hasRole } = useAuth();
  const {
    currentConversation,
    fetchConversation,
    createConversation,
    addMessage,
    updateConversation,
    setCurrentConversation,
  } = useConversations();

  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [activeSettings, setActiveSettings] = useState<{
    defaultModel?: string;
    systemPrompt?: string;
    availableModels?: ModelConfig[];
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    reasoning?: {
      enabled?: boolean;
      effort?: 'low' | 'medium' | 'high';
      maxTokens?: number;
    };
  } | null>(null);

  // Fetch active model settings on mount
  useEffect(() => {
    fetchActiveSettings();
  }, []);

  const fetchActiveSettings = async () => {
    try {
      const response = await fetch('/api/model-settings/active');
      if (response.ok) {
        const settings = await response.json();
        setActiveSettings(settings);
      }
    } catch (error) {
      console.error('Error fetching active settings:', error);
    }
  };

  const handleSelectConversation = async (conversationId: string | null) => {
    setSelectedConversationId(conversationId);
    if (conversationId) {
      await fetchConversation(conversationId);
    } else {
      // Clear the current conversation when null is selected
      setCurrentConversation(null);
    }
  };

  const handleNewConversation = async () => {
    const conversation = await createConversation({
      title: 'New Chat',
      model: activeSettings?.defaultModel || 'openai/gpt-4o-mini',
      systemPrompt: activeSettings?.systemPrompt,
      metadata: activeSettings
        ? {
            temperature: activeSettings.temperature,
            maxTokens: activeSettings.maxTokens,
            topP: activeSettings.topP,
            frequencyPenalty: activeSettings.frequencyPenalty,
            presencePenalty: activeSettings.presencePenalty,
            reasoning: activeSettings.reasoning,
          }
        : undefined,
    });
    if (conversation) {
      setSelectedConversationId(conversation.id);
      await fetchConversation(conversation.id);
      // Dispatch event to update conversation list
      dispatchConversationEvent(ConversationEvents.CONVERSATION_CREATED, {
        conversationId: conversation.id,
        conversation,
      });
    }
  };

  const handleMessageSent = async () => {
    if (!currentConversation) {
      // Create a new conversation if none exists
      await handleNewConversation();
    }

    // The message will be saved by the ChatBot component when it receives the response
  };

  const handleSaveMessage = async (message: {
    role: 'user' | 'assistant';
    content: string;
    reasoning?: string;
    model?: string;
    metadata?: ChatMessage['metadata'];
  }) => {
    if (currentConversation) {
      await addMessage(message);
      // Dispatch event to update conversation list
      dispatchConversationEvent(ConversationEvents.MESSAGE_ADDED, {
        conversationId: currentConversation.id,
      });
    }
  };

  const handleDeleteMessage = async (messageIndex: number) => {
    if (!currentConversation) return;

    // Get the message to delete
    const messageToDelete = currentConversation.messages[messageIndex];
    if (!messageToDelete) return;

    try {
      const response = await fetch(
        `/api/conversations/${currentConversation.id}/messages/${messageToDelete.id}`,
        {
          method: 'DELETE',
        }
      );

      if (response.ok) {
        // Refresh the conversation to get updated messages
        await fetchConversation(currentConversation.id);

        // Dispatch event to update conversation list
        dispatchConversationEvent(ConversationEvents.MESSAGE_ADDED, {
          conversationId: currentConversation.id,
        });
      }
    } catch (error) {
      console.error('Error deleting message:', error);
    }
  };

  // Show setup message if no active settings
  if (activeSettings === null) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle>Model Settings Not Initialized</CardTitle>
            <CardDescription>
              {hasRole('admin')
                ? 'No active model settings found. Please initialize the settings to start using the chat.'
                : 'The chat system is not yet configured. Please contact an administrator.'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {hasRole('admin') ? (
              <div className="space-y-4">
                <Link href="/admin/model-settings">
                  <Button className="w-full">Go to Model Settings</Button>
                </Link>
                <p className="text-muted-foreground text-center text-sm">
                  Or navigate directly to: <code>/admin/model-settings</code>
                </p>
              </div>
            ) : (
              <p className="text-muted-foreground text-center">
                Please ask your administrator to configure the chat models.
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <ChatSidebar
        selectedConversationId={selectedConversationId || undefined}
        onSelectConversation={handleSelectConversation}
        onNewConversation={handleNewConversation}
      />
      <SidebarInset>
        <main className="flex flex-1 flex-col overflow-hidden">
          <ChatBot
            key={currentConversation?.id} // Reset component when conversation changes
            conversationId={currentConversation?.id}
            initialMessages={
              currentConversation?.messages.map((m) => ({
                role: m.role,
                content: m.content,
                reasoning: m.reasoning || undefined,
                metadata: m.metadata || undefined,
              })) || []
            }
            title={currentConversation?.title || 'New Chat'}
            systemPrompt={
              currentConversation?.systemPrompt ||
              activeSettings?.systemPrompt ||
              'You are a helpful study assistant. You help students learn and understand complex topics by providing clear explanations, examples, and practice questions.'
            }
            placeholder="Ask me anything..."
            defaultModel={
              currentConversation?.model ||
              activeSettings?.defaultModel ||
              'google/gemini-2.5-flash-lite'
            }
            showModelSelector={true}
            streaming={true}
            availableModels={activeSettings?.availableModels || []}
            reasoning={
              activeSettings?.reasoning || {
                enabled: true,
                effort: 'medium',
              }
            }
            temperature={activeSettings?.temperature}
            maxTokens={activeSettings?.maxTokens}
            topP={activeSettings?.topP}
            frequencyPenalty={activeSettings?.frequencyPenalty}
            presencePenalty={activeSettings?.presencePenalty}
            onMessageSent={handleMessageSent}
            onMessageReceived={handleSaveMessage}
            onMessageDeleted={handleDeleteMessage}
            onConversationTitleChange={(title) => {
              if (currentConversation) {
                updateConversation(currentConversation.id, { title });
              }
            }}
          />
        </main>
      </SidebarInset>
    </>
  );
}
