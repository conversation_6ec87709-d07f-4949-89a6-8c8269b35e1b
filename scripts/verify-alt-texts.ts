import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from '@/db/schema';
import { images } from '@/db/schema';
import { or, like, isNull } from 'drizzle-orm';

// Load environment variables
config({ path: '.env.local' });

// Create database connection
const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  console.error('DATABASE_URL not found in environment variables');
  process.exit(1);
}

const queryClient = postgres(connectionString);
const db = drizzle(queryClient, { schema });

async function verifyAltTexts() {
  console.log('Verifying alt texts in the database...\n');

  try {
    // Get all images
    const allImages = await db
      .select({
        id: images.id,
        url: images.url,
        altText: images.altText,
      })
      .from(images)
      .orderBy(images.altText);

    console.log(`Total images in database: ${allImages.length}\n`);

    // Group by alt text patterns
    let genericCount = 0;
    let properAltTextCount = 0;
    let emptyCount = 0;

    console.log('Sample of images with their alt texts:');
    console.log('='.repeat(80));

    for (const image of allImages) {
      if (!image.altText || image.altText.trim() === '') {
        emptyCount++;
      } else if (image.altText.includes('Manually assigned image for question')) {
        genericCount++;
        console.log(`❌ Generic: ${image.altText}`);
        console.log(`   URL: ${image.url}`);
        console.log(`   ID: ${image.id}\n`);
      } else {
        properAltTextCount++;
        if (properAltTextCount <= 5) {
          // Show first 5 proper alt texts
          console.log(`✅ Proper: ${image.altText.substring(0, 60)}...`);
          console.log(`   URL: ${image.url}`);
          console.log(`   ID: ${image.id}\n`);
        }
      }
    }

    console.log('='.repeat(80));
    console.log('\nSummary:');
    console.log(`- Images with proper alt text: ${properAltTextCount}`);
    console.log(`- Images with generic alt text: ${genericCount}`);
    console.log(`- Images with empty alt text: ${emptyCount}`);
    console.log(`- Total images: ${allImages.length}`);
  } catch (error) {
    console.error('Error during verification:', error);
    await queryClient.end();
    process.exit(1);
  }

  await queryClient.end();
  process.exit(0);
}

// Run the verification
verifyAltTexts();
