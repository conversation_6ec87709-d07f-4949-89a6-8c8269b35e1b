#!/usr/bin/env python3
"""
Advanced PDF Image Extraction Service - V3
Integrates with memo app for robust image extraction and Cloudinary upload
Based on the advanced image extractor with PyMuPDF and computer vision
"""

import sys
import json
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import tempfile
import os
import hashlib
import numpy as np
from dataclasses import dataclass, asdict
from enum import Enum
import io

try:
    import fitz  # PyMuPDF
    from PIL import Image
    import cv2
    import cloudinary
    import cloudinary.uploader
    from cloudinary import CloudinaryImage
except ImportError as e:
    print(f"Error: Missing required packages. Install with: pip install PyMuPDF pillow opencv-python cloudinary")
    print(f"Missing: {e}")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExtractionMethod(Enum):
    EMBEDDED = "embedded"  # Direct extraction of embedded images
    REGION = "region"      # Extract regions based on coordinates
    VISUAL = "visual"      # Use computer vision to detect image areas

@dataclass
class ExtractedImage:
    """Represents an extracted image with metadata"""
    image_data: bytes
    page_num: int
    bbox: Tuple[float, float, float, float]  # x0, y0, x1, y1
    method: ExtractionMethod
    confidence: float  # 0-1 confidence score
    hash: str  # For deduplication
    dimensions: Tuple[int, int]  # width, height
    cloudinary_url: Optional[str] = None
    public_id: Optional[str] = None

class AdvancedPDFImageExtractor:
    """Advanced PDF image extractor with multiple strategies and Cloudinary integration"""
    
    def __init__(self, cloudinary_cloud_name: str = None, cloudinary_api_key: str = None, cloudinary_api_secret: str = None):
        """Initialize the extractor with optional Cloudinary credentials"""
        self.cloudinary_configured = False
        
        if all([cloudinary_cloud_name, cloudinary_api_key, cloudinary_api_secret]):
            cloudinary.config(
                cloud_name=cloudinary_cloud_name,
                api_key=cloudinary_api_key,
                api_secret=cloudinary_api_secret
            )
            self.cloudinary_configured = True
            logger.info("Cloudinary configured successfully")
        else:
            logger.warning("Cloudinary not configured - will not upload images")

    def extract_images_from_pdf(
        self, 
        pdf_bytes: bytes, 
        coordinates_list: List[Dict[str, Any]] = None,
        upload_to_cloudinary: bool = True,
        folder: str = "memo-extracted-images",
        include_visual_detection: bool = False,
        skip_cover_page: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Extract images from PDF using multiple methods
        
        Args:
            pdf_bytes: PDF file as bytes
            coordinates_list: Optional list of coordinate objects for targeted extraction
            upload_to_cloudinary: Whether to upload extracted images to Cloudinary
            folder: Cloudinary folder for uploads
            include_visual_detection: Whether to use visual detection
            skip_cover_page: Whether to skip the first page
            
        Returns:
            List of extracted image results
        """
        results = []
        
        try:
            # Open PDF from bytes
            doc = fitz.open(stream=pdf_bytes, filetype="pdf")
            logger.info(f"Opened PDF with {len(doc)} pages")
            
            extracted_images = []
            
            if coordinates_list:
                # Extract images based on provided coordinates
                logger.info(f"Extracting {len(coordinates_list)} images based on coordinates")
                for coord_data in coordinates_list:
                    extracted_img = self._extract_from_coordinates(doc, coord_data)
                    if extracted_img:
                        extracted_images.append(extracted_img)
            else:
                # Use automatic extraction methods
                logger.info("Using automatic extraction methods")
                
                # Method 1: Extract embedded images
                embedded_images = self._extract_embedded_images(doc, skip_cover_page)
                extracted_images.extend(embedded_images)
                logger.info(f"Found {len(embedded_images)} embedded images")
                
                # Method 2: Visual detection (optional)
                if include_visual_detection:
                    visual_images = self._extract_visual_regions(doc)
                    extracted_images.extend(visual_images)
                    logger.info(f"Found {len(visual_images)} visual regions")
            
            # Deduplicate images
            unique_images = self._deduplicate_images(extracted_images)
            logger.info(f"After deduplication: {len(unique_images)} unique images")
            
            # Upload to Cloudinary if configured and requested
            for img in unique_images:
                result = self._process_extracted_image(img, upload_to_cloudinary, folder)
                results.append(result)
            
            doc.close()
            
        except Exception as e:
            logger.error(f"Failed to extract images from PDF: {e}")
            return [{"success": False, "error": f"PDF processing failed: {e}"}]
        
        return results

    def _extract_from_coordinates(self, doc: fitz.Document, coord_data: Dict[str, Any]) -> Optional[ExtractedImage]:
        """Extract image from specific coordinates"""
        try:
            # Parse coordinate data - handle different formats
            page_number = coord_data.get('pageNumber', coord_data.get('page', 1))
            bbox = coord_data.get('boundingBox', coord_data.get('bbox'))
            text = coord_data.get('text', 'extracted_image')
            
            if not bbox:
                logger.warning(f"No bounding box found in coordinates: {coord_data}")
                return None
            
            # Handle different bbox formats
            if isinstance(bbox, list) and len(bbox) == 4:
                # Format: [x1, y1, x2, y2]
                x0, y0, x1, y1 = bbox
            elif isinstance(bbox, dict):
                # Format: {x1, y1, x2, y2} or {x0, y0, x1, y1}
                x0 = bbox.get('x0', bbox.get('x1', bbox.get('left', 0)))
                y0 = bbox.get('y0', bbox.get('y1', bbox.get('top', 0)))
                x1 = bbox.get('x1', bbox.get('x2', bbox.get('right', 0)))
                y1 = bbox.get('y1', bbox.get('y2', bbox.get('bottom', 0)))
            else:
                logger.warning(f"Invalid bbox format: {bbox}")
                return None
            
            # Validate page number (convert to 0-based for internal use)
            internal_page_num = page_number - 1 if page_number > 0 else 0
            if internal_page_num < 0 or internal_page_num >= len(doc):
                logger.warning(f"Invalid page number: {page_number}")
                return None
            
            page = doc[internal_page_num]
            
            # Create rectangle for extraction
            rect = fitz.Rect(float(x0), float(y0), float(x1), float(y1))
            
            # Get pixmap of the region with high quality
            mat = fitz.Matrix(2, 2)  # 2x zoom for better quality
            pix = page.get_pixmap(matrix=mat, clip=rect)
            
            # Convert to PNG bytes
            img_data = pix.tobytes("png")
            
            # Validate extracted data
            if len(img_data) == 0:
                logger.warning(f"Empty image data for coordinates: {coord_data}")
                return None
            
            # Create hash for deduplication
            img_hash = hashlib.md5(img_data).hexdigest()
            
            return ExtractedImage(
                image_data=img_data,
                page_num=page_number,  # Keep 1-based page numbering
                bbox=(x0, y0, x1, y1),
                method=ExtractionMethod.REGION,
                confidence=0.9,  # High confidence for coordinate-based extraction
                hash=img_hash,
                dimensions=(pix.width, pix.height)
            )
            
        except Exception as e:
            logger.error(f"Failed to extract from coordinates {coord_data}: {e}")
            return None

    def _extract_embedded_images(self, doc: fitz.Document, skip_cover_page: bool = True) -> List[ExtractedImage]:
        """Extract all embedded images from the PDF"""
        extracted = []
        
        # Detect cover page if needed
        cover_page_detected = False
        if skip_cover_page and len(doc) > 1:
            cover_page_detected = self._is_cover_page(doc, 0)
        
        for page_num, page in enumerate(doc):
            # Skip cover page if detected
            if skip_cover_page and page_num == 0 and cover_page_detected:
                logger.info(f"Skipping page {page_num + 1} (cover page)")
                continue
                
            image_list = page.get_images(full=True)
            
            for img_index, img in enumerate(image_list):
                try:
                    # Get image metadata
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)
                    
                    # Convert CMYK to RGB if needed
                    if pix.n - pix.alpha > 3:
                        pix = fitz.Pixmap(fitz.csRGB, pix)
                    
                    img_data = pix.tobytes("png")
                    
                    # Get image position on page
                    img_instances = page.get_image_rects(xref)
                    if img_instances:
                        bbox = img_instances[0]
                        bbox_tuple = (bbox.x0, bbox.y0, bbox.x1, bbox.y1)
                    else:
                        # Fallback: estimate position
                        bbox_tuple = self._estimate_image_position(page, img_index)
                    
                    # Create ExtractedImage
                    img_hash = hashlib.md5(img_data).hexdigest()
                    extracted.append(ExtractedImage(
                        image_data=img_data,
                        page_num=page_num + 1,  # 1-based page numbering
                        bbox=bbox_tuple,
                        method=ExtractionMethod.EMBEDDED,
                        confidence=1.0,
                        hash=img_hash,
                        dimensions=(pix.width, pix.height)
                    ))
                    
                    pix = None  # Free memory
                    
                except Exception as e:
                    logger.warning(f"Failed to extract embedded image on page {page_num + 1}: {e}")
        
        return extracted

    def _extract_visual_regions(self, doc: fitz.Document) -> List[ExtractedImage]:
        """Use computer vision to detect image regions"""
        extracted = []
        
        for page_num, page in enumerate(doc):
            try:
                # Render page at medium resolution
                mat = fitz.Matrix(2, 2)  # 2x zoom
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # Convert to numpy array for OpenCV
                nparr = np.frombuffer(img_data, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                
                # Detect image regions
                regions = self._detect_image_regions(img)
                
                for region in regions:
                    # Extract the region
                    x0, y0, x1, y1 = region['bbox']
                    region_img = img[int(y0):int(y1), int(x0):int(x1)]
                    
                    # Skip if too small
                    if region_img.shape[0] < 20 or region_img.shape[1] < 20:
                        continue
                    
                    # Convert back to bytes
                    _, buffer = cv2.imencode('.png', region_img)
                    region_data = buffer.tobytes()
                    
                    # Transform coordinates back to page space
                    page_bbox = (
                        x0 / mat.a,  # Divide by zoom factor
                        y0 / mat.d,
                        x1 / mat.a,
                        y1 / mat.d
                    )
                    
                    img_hash = hashlib.md5(region_data).hexdigest()
                    extracted.append(ExtractedImage(
                        image_data=region_data,
                        page_num=page_num + 1,  # 1-based page numbering
                        bbox=page_bbox,
                        method=ExtractionMethod.VISUAL,
                        confidence=region['confidence'],
                        hash=img_hash,
                        dimensions=(region_img.shape[1], region_img.shape[0])
                    ))
                    
            except Exception as e:
                logger.warning(f"Failed to extract visual regions from page {page_num + 1}: {e}")
        
        return extracted

    def _detect_image_regions(self, img: np.ndarray) -> List[Dict]:
        """Detect image regions using computer vision"""
        regions = []
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Edge detection with morphological operations
        edges = cv2.Canny(gray, 100, 200)
        kernel = np.ones((5, 5), np.uint8)
        closed = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        # Find contours
        contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 500:  # Minimum area threshold
                continue
            
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            
            # Filter for likely image regions
            if (0.3 < aspect_ratio < 3.0 and 
                w > 30 and h > 30 and
                w < img.shape[1] * 0.8 and
                h < img.shape[0] * 0.8):
                
                # Check variance to ensure it's not uniform content
                region = gray[y:y+h, x:x+w]
                variance = np.var(region)
                
                if variance > 500:
                    regions.append({
                        'bbox': (x, y, x + w, y + h),
                        'confidence': min(variance / 2000, 1.0),
                        'type': 'complex_region'
                    })
        
        return regions

    def _deduplicate_images(self, images: List[ExtractedImage]) -> List[ExtractedImage]:
        """Remove duplicate images based on hash"""
        seen_hashes = set()
        unique = []
        
        for img in images:
            if img.hash not in seen_hashes:
                seen_hashes.add(img.hash)
                unique.append(img)
        
        return unique

    def _estimate_image_position(self, page, img_index: int) -> Tuple[float, float, float, float]:
        """Estimate image position when exact coordinates aren't available"""
        page_rect = page.rect
        images_on_page = len(page.get_images())
        
        if images_on_page == 1:
            # Center the image
            margin = 50
            return (margin, margin, page_rect.width - margin, page_rect.height - margin)
        else:
            # Create a grid
            cols = min(2, images_on_page)
            rows = (images_on_page + cols - 1) // cols
            
            col = img_index % cols
            row = img_index // cols
            
            cell_width = page_rect.width / cols
            cell_height = page_rect.height / rows
            
            x0 = col * cell_width + 10
            y0 = row * cell_height + 10
            x1 = x0 + cell_width - 20
            y1 = y0 + cell_height - 20
            
            return (x0, y0, x1, y1)

    def _is_cover_page(self, doc: fitz.Document, page_num: int) -> bool:
        """Detect if a page is likely a cover page"""
        try:
            page = doc[page_num]
            text = page.get_text().lower()
            images = page.get_images()
            
            # Cover page indicators
            cover_keywords = [
                'tribunal', 'tcu', 'união', 'instituto', 'secretário',
                'logo', 'brasão', 'emblema', 'ministério', 'república'
            ]
            
            cover_phrases = [
                'auditor federal', 'controle externo', 'concurso público',
                'prova objetiva', 'caderno de provas', 'instruções'
            ]
            
            # Count small images (likely logos)
            small_images = 0
            for img in images:
                try:
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)
                    if pix.width < 400 and pix.height < 200:
                        small_images += 1
                    pix = None
                except:
                    pass
            
            keyword_matches = sum(1 for keyword in cover_keywords if keyword in text)
            phrase_matches = sum(1 for phrase in cover_phrases if phrase in text)
            
            # Cover page logic
            is_cover = (
                (keyword_matches >= 2 and (phrase_matches >= 1 or small_images >= 3)) or
                keyword_matches >= 4
            )
            
            return is_cover
            
        except Exception as e:
            logger.warning(f"Error analyzing page {page_num + 1} for cover detection: {e}")
            return False

    def _process_extracted_image(self, img: ExtractedImage, upload_to_cloudinary: bool, folder: str) -> Dict[str, Any]:
        """Process an extracted image and optionally upload to Cloudinary"""
        result = {
            "success": True,
            "page_number": img.page_num,
            "bounding_box": {"x0": img.bbox[0], "y0": img.bbox[1], "x1": img.bbox[2], "y1": img.bbox[3]},
            "method": img.method.value,
            "confidence": img.confidence,
            "image_size": {"width": img.dimensions[0], "height": img.dimensions[1]},
            "hash": img.hash
        }
        
        # Always save image locally first
        try:
            # Create a temporary directory for this session
            temp_dir = tempfile.mkdtemp(prefix="memo_images_")
            local_filename = f"page_{img.page_num}_{img.hash[:8]}.png"
            local_path = os.path.join(temp_dir, local_filename)
            
            # Save image data to local file
            with open(local_path, 'wb') as f:
                f.write(img.image_data)
            
            result["local_path"] = local_path
            result["local_filename"] = local_filename
            
            # Upload to Cloudinary if requested
            if upload_to_cloudinary and self.cloudinary_configured:
                try:
                    # Upload to Cloudinary
                    upload_result = cloudinary.uploader.upload(
                        local_path,
                        folder=folder,
                        public_id=f"page_{img.page_num}_{img.hash[:8]}",
                        context={
                            "page": str(img.page_num),
                            "bbox": f"{img.bbox[0]:.1f},{img.bbox[1]:.1f},{img.bbox[2]:.1f},{img.bbox[3]:.1f}",
                            "method": img.method.value,
                            "confidence": str(img.confidence),
                            "extraction_tool": "advanced_python_extractor"
                        },
                        tags=["pdf_extraction", "memo_app", img.method.value]
                    )
                    
                    result.update({
                        "cloudinary_url": upload_result['secure_url'],
                        "public_id": upload_result['public_id'],
                        "uploaded": True
                    })
                    
                except Exception as e:
                    logger.error(f"Failed to upload image to Cloudinary: {e}")
                    result.update({
                        "uploaded": False,
                        "upload_error": str(e)
                    })
            else:
                result["uploaded"] = False
                if not self.cloudinary_configured:
                    result["upload_error"] = "Cloudinary not configured"
                    
        except Exception as e:
            logger.error(f"Failed to save image locally: {e}")
            result.update({
                "success": False,
                "error": f"Local save failed: {e}"
            })
        
        return result

def main():
    parser = argparse.ArgumentParser(description='Advanced PDF Image Extraction with Cloudinary Upload')
    parser.add_argument('--pdf-path', required=True, help='Path to PDF file')
    parser.add_argument('--coordinates', help='JSON string with coordinates data (optional)')
    parser.add_argument('--cloudinary-cloud-name', help='Cloudinary cloud name')
    parser.add_argument('--cloudinary-api-key', help='Cloudinary API key') 
    parser.add_argument('--cloudinary-api-secret', help='Cloudinary API secret')
    parser.add_argument('--folder', default='memo-extracted-images', help='Cloudinary folder (default: memo-extracted-images)')
    parser.add_argument('--upload', action='store_true', help='Upload to Cloudinary')
    parser.add_argument('--visual-detection', action='store_true', help='Include visual detection')
    parser.add_argument('--skip-cover', action='store_true', default=True, help='Skip cover page (default: True)')
    parser.add_argument('--output', help='Output JSON file path (default: stdout)')
    
    args = parser.parse_args()
    
    try:
        # Read PDF file
        pdf_path = Path(args.pdf_path)
        if not pdf_path.exists():
            print(json.dumps({"success": False, "error": f"PDF file not found: {args.pdf_path}"}))
            sys.exit(1)
        
        pdf_bytes = pdf_path.read_bytes()
        
        # Parse coordinates if provided
        coordinates_list = None
        if args.coordinates:
            try:
                coordinates_list = json.loads(args.coordinates)
                if not isinstance(coordinates_list, list):
                    coordinates_list = [coordinates_list]
            except json.JSONDecodeError as e:
                print(json.dumps({"success": False, "error": f"Invalid coordinates JSON: {e}"}))
                sys.exit(1)
        
        # Initialize extractor
        extractor = AdvancedPDFImageExtractor(
            args.cloudinary_cloud_name,
            args.cloudinary_api_key, 
            args.cloudinary_api_secret
        )
        
        # Extract images
        results = extractor.extract_images_from_pdf(
            pdf_bytes,
            coordinates_list=coordinates_list,
            upload_to_cloudinary=args.upload,
            folder=args.folder,
            include_visual_detection=args.visual_detection,
            skip_cover_page=args.skip_cover
        )
        
        # Prepare output
        output_data = {
            "success": True,
            "extracted_images": results,
            "total_processed": len(coordinates_list) if coordinates_list else "auto",
            "successful_extractions": len([r for r in results if r.get("success", True)])
        }
        
        output_json = json.dumps(output_data, indent=2)
        
        if args.output:
            Path(args.output).write_text(output_json)
        else:
            print(output_json)
            
    except Exception as e:
        error_output = {"success": False, "error": f"Script failed: {e}"}
        print(json.dumps(error_output))
        sys.exit(1)

if __name__ == "__main__":
    main()