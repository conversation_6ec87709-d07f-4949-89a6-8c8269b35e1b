import { config } from 'dotenv';
import path from 'path';

// Load environment variables from .env.local
config({ path: path.resolve(process.cwd(), '.env.local') });

import { db } from '../db/client';
import { promptTemplates } from '../db/schema';
import { eq, and } from 'drizzle-orm';

const AI_STUDY_ASSISTANT_PROMPTS = [
  {
    name: 'Explicar questão',
    description: 'Explica a questão em termos simples',
    promptContent:
      'Por favor, explique esta questão em termos simples. Detalhe o que está sendo perguntado, quais conceitos preciso entender e me guie através do processo de raciocínio.',
    icon: 'Lightbulb',
    displayOrder: 0,
  },
  {
    name: '<PERSON>riar mnemônicos',
    description: 'Cria técnicas de memorização',
    promptContent:
      'Me ajude a criar técnicas de memorização, mnemônicos ou associações memoráveis para os conceitos-chave desta questão. Faça-os fáceis de lembrar e relevantes ao tópico.',
    icon: 'Brain',
    displayOrder: 1,
  },
  {
    name: 'Gerar mapa mental',
    description: 'Cria um mapa mental dos conceitos',
    promptContent:
      'Crie um mapa mental conceitual dos tópicos e relações desta questão. Mostre como os diferentes conceitos se conectam e se relacionam entre si.',
    icon: 'Map',
    displayOrder: 2,
  },
  {
    name: 'Por que erradas',
    description: 'Explica por que as opções incorretas estão erradas',
    promptContent:
      'Explique por que cada opção incorreta está errada. Me ajude a entender os erros comuns ou conceitos equivocados que podem levar alguém a escolher essas respostas erradas.',
    icon: 'HelpCircle',
    displayOrder: 3,
  },
  {
    name: 'Questões similares',
    description: 'Gera questões práticas similares',
    promptContent:
      'Gere 3-5 questões práticas similares sobre este tópico. Inclua variações que testem os mesmos conceitos mas de ângulos diferentes. Forneça respostas e explicações breves.',
    icon: 'BookOpen',
    displayOrder: 4,
  },
  {
    name: 'Aplicações reais',
    description: 'Mostra aplicações práticas do mundo real',
    promptContent:
      'Mostre-me aplicações do mundo real e exemplos práticos desses conceitos. Como esse conhecimento é usado em ambientes profissionais ou na vida cotidiana?',
    icon: 'Globe',
    displayOrder: 5,
  },
];

async function seedAIPrompts() {
  console.log('🌱 Seeding AI Study Assistant prompts...');

  try {
    // Check if prompts already exist
    const existingPrompts = await db
      .select()
      .from(promptTemplates)
      .where(
        and(
          eq(promptTemplates.feature, 'ai_study_assistant'),
          eq(promptTemplates.promptType, 'sample_prompts')
        )
      );

    if (existingPrompts.length > 0) {
      console.log('⚠️  AI Study Assistant prompts already exist. Skipping seed.');
      return;
    }

    // Insert all prompts
    for (const prompt of AI_STUDY_ASSISTANT_PROMPTS) {
      await db.insert(promptTemplates).values({
        ...prompt,
        feature: 'ai_study_assistant',
        promptType: 'sample_prompts',
        modelName: 'gemini-2.5-flash',
        isDefault: false,
        isSystem: true,
        isEnabled: true,
      });
      console.log(`✅ Created prompt: ${prompt.name}`);
    }

    console.log('✨ Successfully seeded AI Study Assistant prompts!');
  } catch (error) {
    console.error('❌ Error seeding prompts:', error);
    process.exit(1);
  }
}

// Run the seed function
seedAIPrompts()
  .then(() => {
    console.log('✅ Seed completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Seed failed:', error);
    process.exit(1);
  });
