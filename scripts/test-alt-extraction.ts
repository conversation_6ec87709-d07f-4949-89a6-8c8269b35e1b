import { extractAltTextFromHtml, extractAltTextFromQuestion } from '@/lib/utils/extract-alt-text';

// Test HTML content with images
const testHtml = `
<p>This is a test question with an image:</p>
<img src="https://res.cloudinary.com/test/image1.jpg" alt="Diagram showing the structure of a cell">
<p>Another paragraph with another image:</p>
<img src="https://res.cloudinary.com/test/image2.jpg" alt="Graph depicting economic growth over time">
<p>Image without alt text:</p>
<img src="https://res.cloudinary.com/test/image3.jpg">
`;

const testOptions = [
  {
    text: '<p>Option A with image: <img src="https://res.cloudinary.com/test/option1.jpg" alt="Option A illustration"></p>',
  },
  {
    text: '<p>Option B without image</p>',
  },
  {
    text: '<p>Option C with image: <img src="https://res.cloudinary.com/test/option2.jpg" alt="Option C diagram"></p>',
  },
];

console.log('Testing extractAltTextFromHtml...');
const results = extractAltTextFromHtml(testHtml);
console.log('Results:', results);
console.log('\n');

console.log('Testing extractAltTextFromQuestion...');
const altTextMap = extractAltTextFromQuestion(testHtml, testOptions);
console.log('Alt text map:');
for (const [url, altText] of altTextMap) {
  console.log(`  ${url} => "${altText}"`);
}

// Test with the actual URL from the database screenshot
const realTestHtml = `
<p>Question about something:</p>
<img src="https://res.cloudinary.com/..." alt="Manually assigned image for question 3">
`;

console.log('\n\nTesting with real URL pattern...');
const realResults = extractAltTextFromHtml(realTestHtml);
console.log('Real results:', realResults);
