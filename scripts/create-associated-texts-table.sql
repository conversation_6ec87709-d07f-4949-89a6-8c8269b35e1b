-- Create the question_associated_texts table
CREATE TABLE IF NOT EXISTS "question_associated_texts" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"content" text NOT NULL,
	"hash" varchar(64) NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "unique_content_hash" UNIQUE("hash")
);

-- Create index on hash for fast lookups
CREATE INDEX IF NOT EXISTS "idx_associated_texts_hash" ON "question_associated_texts" USING btree ("hash");

-- Add the foreign key column to questions table
ALTER TABLE "questions" ADD COLUMN IF NOT EXISTS "associated_text_id" uuid;

-- Add foreign key constraint
ALTER TABLE "questions" 
ADD CONSTRAINT "questions_associated_text_id_question_associated_texts_id_fk" 
FOREIGN KEY ("associated_text_id") 
REFERENCES "public"."question_associated_texts"("id") 
ON DELETE SET NULL ON UPDATE NO ACTION;

-- Create index on the foreign key
CREATE INDEX IF NOT EXISTS "idx_questions_associated_text" ON "questions" USING btree ("associated_text_id");