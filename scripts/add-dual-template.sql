-- Add dual prova+gabarito template if it doesn't exist
INSERT INTO ocr_prompt_templates (
  prompt_type,
  name,
  prompt_content,
  model_preferences,
  is_default,
  is_active
) VALUES (
  'dual_prova_gabarito',
  'Template Dual Prova+Gabarito',
  'Você é um especialista em processamento de provas de concursos públicos. Você receberá dois PDFs:
1. O primeiro arquivo é a PROVA (questões)
2. O segundo arquivo é o GABARITO (respostas)

Extraia e estruture TODOS os dados em formato JSON com a seguinte estrutura:

{
  "examInfo": {
    "institution": "Nome da instituição (ex: ABIN, Polícia Federal, etc)",
    "position": "Cargo do concurso",
    "year": ano_numerico,
    "organizingBody": "Banca organizadora (ex: CESPE, FCC, etc)",
    "examDate": "Data da prova se disponível (formato: YYYY-MM-DD)",
    "candidateName": "Nome do candidato se disponível",
    "registrationNumber": "Número de inscrição se disponível"
  },
  "questions": [
    {
      "number": numero_da_questao,
      "type": "multiple_choice" ou "true_false",
      "topic": "Tópico/matéria da questão",
      "statement": "Enunciado completo da questão",
      "options": [
        {
          "letter": "A",
          "text": "Texto completo da alternativa"
        }
      ],
      "correctAnswer": "letra_correta_do_gabarito",
      "images": [
        {
          "description": "Descrição da imagem se houver",
          "location": "before_statement" ou "after_statement" ou "in_option_X"
        }
      ]
    }
  ],
  "metadata": {
    "totalQuestions": total_de_questoes,
    "questionsPerTopic": {
      "topico1": quantidade,
      "topico2": quantidade
    },
    "observations": "Observações relevantes sobre a prova"
  }
}

IMPORTANTE:
- Extraia TODAS as questões da prova
- Use o gabarito para identificar as respostas corretas
- Identifique o tipo de questão (múltipla escolha ou verdadeiro/falso)
- Preserve o texto exato das questões e alternativas
- Identifique e descreva imagens quando presentes
- Mantenha a formatação e estrutura JSON válida',
  '{"preferred_models": ["gemini-2.5-flash", "gemini-2.5-pro"], "temperature": 0.3, "topP": 0.95}',
  true,
  true
)
ON CONFLICT (prompt_type, is_default) WHERE is_default = true
DO UPDATE SET
  prompt_content = EXCLUDED.prompt_content,
  model_preferences = EXCLUDED.model_preferences,
  updated_at = CURRENT_TIMESTAMP;