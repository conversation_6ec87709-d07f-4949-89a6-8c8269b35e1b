import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { DrizzleAdapter } from '@auth/drizzle-adapter';
import { db } from '@/db';
import {
  users,
  accounts,
  sessions,
  verificationTokens,
  userRoles,
  userFeatures,
  userSubscriptions,
} from '@/db/schema/auth';
import { eq } from 'drizzle-orm';
import type { Role, Feature, SubscriptionTier, SubscriptionStatus } from '@/lib/auth/features';
import { compare } from '@/lib/auth/password';
import { authConfig } from './auth.config';

export const { auth, handlers, signIn, signOut } = NextAuth({
  ...authConfig,
  useSecureCookies: process.env.NODE_ENV === 'production',
  adapter: DrizzleAdapter(db, {
    usersTable: users,
    accountsTable: accounts,
    sessionsTable: sessions,
    verificationTokensTable: verificationTokens,
  }),
  providers: [
    ...authConfig.providers.filter((provider) => provider.id !== 'credentials'),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // Find user by email
        const user = await db
          .select()
          .from(users)
          .where(eq(users.email, credentials.email as string))
          .limit(1);

        if (user.length === 0) {
          return null;
        }

        // Check password
        const passwordMatch = await compare(
          credentials.password as string,
          user[0].passwordHash || ''
        );

        if (!passwordMatch) {
          return null;
        }

        return {
          id: user[0].id,
          email: user[0].email,
          name: user[0].name,
          image: user[0].image,
        };
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      // Include user ID in token on initial login
      if (user) {
        token.userId = user.id;
      }

      // Add role, features, and subscription data to JWT
      if (token.userId) {
        // Set default values first
        token.role = 'student_free';
        token.features = [];
        token.tier = 'free';
        token.subscription = { status: 'active' };

        try {
          // Get user role
          let userRole: { role: string }[] = [];
          try {
            userRole = await db
              .select({ role: userRoles.role })
              .from(userRoles)
              .where(eq(userRoles.userId, token.userId as string))
              .limit(1);
          } catch (roleError) {
            console.warn(
              'Failed to fetch user role, using default. This may be due to Edge runtime limitations.'
            );
            userRole = [];
          }

          if (userRole[0]?.role) {
            token.role = userRole[0].role as Role;
          }

          // Get user features
          let userFeaturesList: { feature: string }[] = [];
          try {
            userFeaturesList = await db
              .select({ feature: userFeatures.feature })
              .from(userFeatures)
              .where(eq(userFeatures.userId, token.userId as string));
          } catch (featuresError) {
            console.warn(
              'Failed to fetch user features, using default. This may be due to Edge runtime limitations.'
            );
            userFeaturesList = [];
          }

          if (userFeaturesList.length > 0) {
            token.features = userFeaturesList.map((f) => f.feature as Feature);
          }

          // Get user subscription
          let userSubscription: { tier: string; status: string; expiresAt: Date | null }[] = [];
          try {
            userSubscription = await db
              .select({
                tier: userSubscriptions.tier,
                status: userSubscriptions.status,
                expiresAt: userSubscriptions.expiresAt,
              })
              .from(userSubscriptions)
              .where(eq(userSubscriptions.userId, token.userId as string))
              .limit(1);
          } catch (subscriptionError) {
            console.warn(
              'Failed to fetch user subscription, using default. This may be due to Edge runtime limitations.'
            );
            userSubscription = [];
          }

          if (userSubscription[0]) {
            token.tier = userSubscription[0].tier as SubscriptionTier;
            token.subscription = {
              status: userSubscription[0].status as SubscriptionStatus,
              expiresAt: userSubscription[0].expiresAt?.toISOString(),
            };
          }
        } catch (error) {
          console.error('Unexpected error in JWT callback:', error);
          // Default values are already set at the beginning
        }
      }

      return token;
    },
    async session({ session, token }) {
      // Add custom fields to session from token
      if (token.userId && session.user) {
        session.user.id = token.userId as string;
        session.user.role = token.role as Role;
        session.user.features = token.features as Feature[];
        session.user.tier = token.tier as SubscriptionTier;
        session.user.subscription = token.subscription as {
          status: SubscriptionStatus;
          expiresAt?: string;
        };

        // Observability: Log user info for monitoring (example)
        if (process.env.NODE_ENV === 'production') {
          // Example: Send to monitoring service
          // console.log('[Auth] Session created for user:', {
          //   userId: session.user.id,
          //   email: session.user.email,
          //   role: session.user.role
          // });
        }
      }

      // Session rotation check (rotate after 24 hours)
      if (token.iat && typeof token.iat === 'number') {
        const sessionAge = Date.now() / 1000 - token.iat;
        if (sessionAge > 24 * 60 * 60) {
          // Session is older than 24 hours, trigger rotation
          token.iat = Math.floor(Date.now() / 1000);
        }
      }

      return session;
    },
    async signIn() {
      // Allow all sign-ins - we'll handle user creation through the adapter
      return true;
    },
  },
  events: {
    async createUser({ user }) {
      // Set default role for new users
      try {
        await db.insert(userRoles).values({
          userId: user.id!,
          role: 'student_free',
        });

        // Production: Log new user creation
        if (process.env.NODE_ENV === 'production') {
          console.log('[Auth] New user created:', {
            userId: user.id,
            email: user.email,
            provider: user.email?.endsWith('@gmail.com') ? 'google' : 'credentials',
          });
        }
      } catch (error) {
        console.error('Error setting default role for new user:', error);
      }
    },
    async signIn({ user, account }) {
      // Production: Log sign-in events
      if (process.env.NODE_ENV === 'production') {
        console.log('[Auth] User signed in:', {
          userId: user.id,
          provider: account?.provider || 'credentials',
          timestamp: new Date().toISOString(),
        });
      }
    },
    async signOut() {
      // Production: Log sign-out events
      if (process.env.NODE_ENV === 'production') {
        // The signOut event might have different shapes depending on the context
        console.log('[Auth] User signed out:', {
          timestamp: new Date().toISOString(),
        });
      }
    },
  },
  // Production: Additional security settings
  cookies: {
    sessionToken: {
      name:
        process.env.NODE_ENV === 'production'
          ? '__Secure-authjs.session-token'
          : 'authjs.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    pkceCodeVerifier: {
      name:
        process.env.NODE_ENV === 'production'
          ? '__Secure-authjs.pkce.code_verifier'
          : 'authjs.pkce.code_verifier',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        domain: process.env.NODE_ENV === 'production' ? undefined : 'localhost',
        secure: process.env.NODE_ENV === 'production',
        maxAge: 60 * 15, // 15 minutes
      },
    },
    state: {
      name: process.env.NODE_ENV === 'production' ? '__Secure-authjs.state' : 'authjs.state',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        domain: process.env.NODE_ENV === 'production' ? undefined : 'localhost',
        secure: process.env.NODE_ENV === 'production',
        maxAge: 60 * 15, // 15 minutes
      },
    },
  },
  // Production: Custom error pages
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
  },
});
