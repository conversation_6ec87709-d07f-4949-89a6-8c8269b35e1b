import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  // Allow 127.0.0.1 for Google OAuth during development
  allowedDevOrigins: ['127.0.0.1'],

  // Configure external image domains
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'res.cloudinary.com',
        port: '',
        pathname: '/**',
      },
    ],
  },

  // Note: Turbopack handles pg-native/cloudflare:sockets differently than Webpack
  // These modules are automatically ignored in Edge runtime environments
};

export default nextConfig;
