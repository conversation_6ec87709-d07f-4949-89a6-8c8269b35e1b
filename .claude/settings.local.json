{"permissions": {"allow": ["Bash(pnpm typecheck:*)", "Bash(pnpm prettier:check:*)", "mcp__Context7__resolve-library-id", "mcp__Context7__get-library-docs", "mcp__supabase__search_docs", "WebFetch(domain:docs.docker.com)", "Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(./scripts/setup-git-aliases.sh:*)", "Bash(pnpm tsc:*)", "Bash(pnpm eslint:*)", "Bash(pnpm prettier:*)", "Bash(find:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(pnpm add:*)", "<PERSON><PERSON>(cat:*)", "Bash(pnpm lint:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm run:*)", "<PERSON><PERSON>(npx prettier:*)", "Bash(cp:*)", "Bash(grep:*)", "WebFetch(domain:orm.drizzle.team)", "mcp__playwright__browser_click", "WebFetch(domain:github.com)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_close", "WebFetch(domain:www.npmjs.com)", "WebFetch(domain:cdn.jsdelivr.net)", "WebFetch(domain:www.jsdelivr.com)", "Bash(node:*)", "Bash(rm:*)", "Bash(npx tsx:*)", "Bash(pnpm build:*)", "Bash(pnpm dlx shadcn-ui@latest add:*)", "Bash(pnpm dlx shadcn@latest add:*)", "Bash(npm install:*)", "WebFetch(domain:ai.google.dev)", "Bash(pnpm run:*)", "Bash(./node_modules/.bin/next lint)", "Bash(./node_modules/.bin/prettier:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(tsc:*)", "Bash(./node_modules/.bin/tsc:*)", "Bash(./node_modules/.bin/eslint . --ext .js,.jsx,.ts,.tsx)", "Bash(./node_modules/.bin/eslint:*)", "<PERSON><PERSON>(test:*)", "Bash(pnpm dev:*)", "Bash(./fix-role-check.sh:*)", "Bash(pnpm drizzle-kit generate:pg:*)", "Bash(pnpm drizzle-kit:*)", "<PERSON><PERSON>(docker compose:*)", "Bash(pnpm db:push:*)", "Bash(rg:*)", "WebFetch(domain:authjs.dev)", "Bash(pnpm remove:*)", "<PERSON><PERSON>(python3:*)", "Bash(pnpm upgrade:*)"], "deny": []}}