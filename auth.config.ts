import type { NextAuthConfig } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import CredentialsProvider from 'next-auth/providers/credentials';

// This is the base auth configuration without database adapter
// Safe to use in Edge runtime (middleware)
export const authConfig = {
  trustHost: true, // Trust the host for local development
  providers: [
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID || process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.AUTH_GOOGLE_SECRET || process.env.GOOGLE_CLIENT_SECRET!,
      // Production best practice: Use authorization params for better security
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        // In Edge runtime, we can't access the database
        // The actual credential validation will be handled in auth.ts
        // This is just to define the provider structure
        return null;
      },
    }),
  ],
  session: {
    strategy: 'jwt',
  },
  pages: {
    signIn: '/auth/signin',
  },
  callbacks: {
    // Basic callbacks without database access
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const publicPaths = ['/', '/auth/signin', '/auth/signup', '/auth/forgot-password'];
      const isPublicPath = publicPaths.some(
        (path) => nextUrl.pathname === path || nextUrl.pathname.startsWith('/api/auth/')
      );

      if (isPublicPath) {
        return true;
      }

      // Allow API routes for authenticated users
      if (nextUrl.pathname.startsWith('/api/') && isLoggedIn) {
        return true;
      }

      return isLoggedIn;
    },
  },
} satisfies NextAuthConfig;
