{"name": "memo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "prettier": "prettier --write .", "prettier:check": "prettier --check .", "db:up": "docker compose up -d", "db:down": "docker compose down", "db:reset": "docker compose down -v && docker compose up -d", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@auth/drizzle-adapter": "^1.10.0", "@google/genai": "^1.10.0", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-code-block-lowlight": "^3.0.7", "@tiptap/extension-highlight": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-placeholder": "^3.0.7", "@tiptap/extension-table": "^3.0.7", "@tiptap/extension-table-cell": "^3.0.7", "@tiptap/extension-table-header": "^3.0.7", "@tiptap/extension-table-row": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-underline": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.20", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookie": "^1.0.2", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "drizzle-orm": "^0.44.3", "highlight.js": "^11.11.1", "jose": "^6.0.12", "jsdom": "^26.1.0", "katex": "^0.16.22", "lodash": "^4.17.21", "lowlight": "^3.3.0", "lucide-react": "^0.525.0", "marked": "^16.1.1", "next": "15.4.3", "next-auth": "5.0.0-beta.29", "next-themes": "^0.4.6", "openai": "^5.10.2", "postgres": "^3.4.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "ts-fsrs": "^5.2.1", "tsx": "^4.20.3", "uuid": "^11.1.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/katex": "^0.16.7", "@types/node": "^24.0.15", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^17.2.0", "drizzle-kit": "^0.31.4", "eslint": "^9.31.0", "eslint-config-next": "15.4.2", "eslint-config-prettier": "^10.1.8", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}