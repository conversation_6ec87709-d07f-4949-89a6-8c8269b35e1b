'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { MessageSquare, Plus, Trash2 } from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import type { Conversation } from '@/db/schema';
import { ConversationEvents, addConversationEventListener } from '@/lib/events';

interface ChatSidebarProps {
  selectedConversationId?: string;
  onSelectConversation: (conversationId: string | null) => void;
  onNewConversation: () => void;
}

export function ChatSidebar({
  selectedConversationId,
  onSelectConversation,
  onNewConversation,
}: ChatSidebarProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const fetchConversations = useCallback(
    async (silent = false) => {
      try {
        if (!silent && isInitialLoad) {
          setLoading(true);
        }

        const response = await fetch('/api/conversations');
        if (response.ok) {
          const data = await response.json();
          // Sort conversations by most recent first
          const sortedData = data.sort(
            (a: Conversation, b: Conversation) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          );
          setConversations(sortedData);
        }
      } catch (error) {
        console.error('Error fetching conversations:', error);
      } finally {
        if (!silent && isInitialLoad) {
          setLoading(false);
          setIsInitialLoad(false);
        }
      }
    },
    [isInitialLoad]
  );

  useEffect(() => {
    fetchConversations();

    // Set up polling for realtime updates (less frequent since we have events)
    const interval = setInterval(() => {
      fetchConversations(true); // silent update
    }, 10000); // Poll every 10 seconds as a fallback

    // Listen to conversation events
    const unsubscribeUpdated = addConversationEventListener(
      ConversationEvents.CONVERSATION_UPDATED,
      () => fetchConversations(true)
    );

    const unsubscribeCreated = addConversationEventListener(
      ConversationEvents.CONVERSATION_CREATED,
      () => fetchConversations(true)
    );

    const unsubscribeDeleted = addConversationEventListener(
      ConversationEvents.CONVERSATION_DELETED,
      () => fetchConversations(true)
    );

    const unsubscribeMessage = addConversationEventListener(ConversationEvents.MESSAGE_ADDED, () =>
      fetchConversations(true)
    );

    return () => {
      clearInterval(interval);
      unsubscribeUpdated();
      unsubscribeCreated();
      unsubscribeDeleted();
      unsubscribeMessage();
    };
  }, [fetchConversations]);

  const handleDelete = async (conversationId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (!confirm('Are you sure you want to delete this conversation?')) return;

    try {
      const response = await fetch(`/api/conversations/${conversationId}`, {
        method: 'DELETE',
      });
      if (response.ok) {
        fetchConversations();
        if (selectedConversationId === conversationId) {
          onSelectConversation(null);
        }
      }
    } catch (error) {
      console.error('Error deleting conversation:', error);
    }
  };

  const formatDate = (date: string) => {
    const d = new Date(date);
    const now = new Date();
    const diffMs = now.getTime() - d.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return d.toLocaleDateString([], { weekday: 'short' });
    } else {
      return d.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <Sidebar collapsible="icon" className="bg-background">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton onClick={onNewConversation} tooltip="New Chat" className="mt-2">
              <Plus className="h-4 w-4" />
              <span>New Chat</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Chat History</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {loading ? (
                <div className="text-muted-foreground p-4 text-center text-sm">
                  Loading conversations...
                </div>
              ) : conversations.length === 0 ? (
                <div className="text-muted-foreground p-4 text-center text-sm">
                  No conversations yet. Start a new chat!
                </div>
              ) : (
                conversations.map((conversation) => (
                  <SidebarMenuItem key={conversation.id} className="group relative">
                    <SidebarMenuButton
                      isActive={selectedConversationId === conversation.id}
                      onClick={() => onSelectConversation(conversation.id)}
                      className="h-auto justify-start py-2 pr-10"
                    >
                      <MessageSquare className="mr-2 h-4 w-4 flex-shrink-0" />
                      <div className="min-w-0 flex-1 text-left">
                        <p className="truncate text-sm">{conversation.title}</p>
                        <p className="text-muted-foreground truncate text-xs">
                          {formatDate(conversation.updatedAt.toString())}
                        </p>
                      </div>
                    </SidebarMenuButton>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute top-1/2 right-1 h-6 w-6 -translate-y-1/2 opacity-0 transition-opacity group-hover:opacity-100"
                      onClick={(e) => handleDelete(conversation.id, e)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </SidebarMenuItem>
                ))
              )}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}
