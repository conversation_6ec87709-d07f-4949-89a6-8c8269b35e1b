'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { MessageSquare, Plus, Trash2, ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import type { Conversation } from '@/db/schema';
import { ConversationEvents, addConversationEventListener } from '@/lib/events';

interface ConversationListProps {
  selectedConversationId?: string;
  onSelectConversation: (conversationId: string | null) => void;
  onNewConversation: () => void;
  onClose?: () => void;
}

export function ConversationList({
  selectedConversationId,
  onSelectConversation,
  onNewConversation,
  onClose,
}: ConversationListProps) {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const fetchConversations = useCallback(
    async (silent = false) => {
      try {
        if (!silent && isInitialLoad) {
          setLoading(true);
        }

        const response = await fetch('/api/conversations');
        if (response.ok) {
          const data = await response.json();
          // Sort conversations by most recent first
          const sortedData = data.sort(
            (a: Conversation, b: Conversation) =>
              new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          );
          setConversations(sortedData);
        }
      } catch (error) {
        console.error('Error fetching conversations:', error);
      } finally {
        if (!silent && isInitialLoad) {
          setLoading(false);
          setIsInitialLoad(false);
        }
      }
    },
    [isInitialLoad]
  );

  useEffect(() => {
    fetchConversations();

    // Set up polling for realtime updates (less frequent since we have events)
    const interval = setInterval(() => {
      fetchConversations(true); // silent update
    }, 10000); // Poll every 10 seconds as a fallback

    // Listen to conversation events
    const unsubscribeUpdated = addConversationEventListener(
      ConversationEvents.CONVERSATION_UPDATED,
      () => fetchConversations(true)
    );

    const unsubscribeCreated = addConversationEventListener(
      ConversationEvents.CONVERSATION_CREATED,
      () => fetchConversations(true)
    );

    const unsubscribeDeleted = addConversationEventListener(
      ConversationEvents.CONVERSATION_DELETED,
      () => fetchConversations(true)
    );

    const unsubscribeMessage = addConversationEventListener(ConversationEvents.MESSAGE_ADDED, () =>
      fetchConversations(true)
    );

    return () => {
      clearInterval(interval);
      unsubscribeUpdated();
      unsubscribeCreated();
      unsubscribeDeleted();
      unsubscribeMessage();
    };
  }, [fetchConversations]);

  const handleDelete = async (conversationId: string) => {
    if (!confirm('Are you sure you want to delete this conversation?')) return;

    try {
      const response = await fetch(`/api/conversations/${conversationId}`, {
        method: 'DELETE',
      });
      if (response.ok) {
        fetchConversations();
        if (selectedConversationId === conversationId) {
          onSelectConversation(null);
        }
      }
    } catch (error) {
      console.error('Error deleting conversation:', error);
    }
  };

  const handleSelectAndClose = (conversationId: string) => {
    onSelectConversation(conversationId);
    // Close sidebar on mobile after selection
    if (window.innerWidth < 768 && onClose) {
      onClose();
    }
  };

  const formatDate = (date: string) => {
    const d = new Date(date);
    const now = new Date();
    const diffMs = now.getTime() - d.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return d.toLocaleDateString([], { weekday: 'short' });
    } else {
      return d.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <div className="bg-muted/30 flex h-full flex-col border-r">
      <div className="flex items-center justify-between border-b p-4">
        <div className="flex items-center gap-2">
          {/* Mobile close button */}
          <Button variant="ghost" size="icon" className="h-8 w-8 md:hidden" onClick={onClose}>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-lg font-semibold">Chat</h2>
        </div>
        <div className="flex items-center gap-2">
          {/* Desktop collapse button */}
          <Button
            variant="ghost"
            size="icon"
            className="hidden h-8 w-8 md:flex"
            onClick={onClose}
            title="Collapse sidebar"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          {/* New chat button - icon only */}
          <Button size="icon" onClick={onNewConversation} className="h-8 w-8" title="New Chat">
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1">
        {loading ? (
          <div className="text-muted-foreground p-4 text-center text-sm">
            Loading conversations...
          </div>
        ) : conversations.length === 0 ? (
          <div className="text-muted-foreground p-4 text-center text-sm">
            No conversations yet. Start a new chat!
          </div>
        ) : (
          <div className="space-y-1 p-2">
            {conversations.map((conversation) => (
              <div
                key={conversation.id}
                className={cn(
                  'group hover:bg-muted/50 relative flex cursor-pointer items-center gap-2 rounded-lg px-3 py-2 transition-all duration-200',
                  selectedConversationId === conversation.id && 'bg-muted'
                )}
                onClick={() => handleSelectAndClose(conversation.id)}
              >
                <MessageSquare className="text-muted-foreground h-4 w-4 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <p className="truncate text-sm font-medium">{conversation.title}</p>
                  <p className="text-muted-foreground truncate text-xs">
                    {formatDate(conversation.updatedAt.toString())}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 flex-shrink-0 opacity-0 transition-opacity group-hover:opacity-100"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDelete(conversation.id);
                  }}
                  title="Delete conversation"
                >
                  <Trash2 className="h-3.5 w-3.5" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
