'use client';

import React, { useEffect, useState, useRef } from 'react';
import { createPortal } from 'react-dom';
import { cn } from '@/lib/utils';

interface AnimatedCounterProps {
  value: number;
  duration?: number;
  className?: string;
  suffix?: string;
  showChange?: boolean;
}

export function AnimatedCounter({
  value,
  duration = 1000,
  className,
  suffix = '',
  showChange = true,
}: AnimatedCounterProps) {
  const [displayValue, setDisplayValue] = useState(value);
  const [isAnimating, setIsAnimating] = useState(false);
  const [changeAmount, setChangeAmount] = useState(0);
  const [showPulse, setShowPulse] = useState(false);
  const [changeIndicatorPosition, setChangeIndicatorPosition] = useState<{
    top: number;
    left: number;
  } | null>(null);
  const previousValueRef = useRef(value);
  const animationRef = useRef<number | undefined>(undefined);
  const counterRef = useRef<HTMLSpanElement | null>(null);

  useEffect(() => {
    const previousValue = previousValueRef.current;
    const change = value - previousValue;

    if (change === 0) return;

    // Cancel any ongoing animation
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    setChangeAmount(change);
    setIsAnimating(true);
    setShowPulse(true);

    // Calculate position for the change indicator
    if (counterRef.current && showChange) {
      const rect = counterRef.current.getBoundingClientRect();
      setChangeIndicatorPosition({
        top: rect.top + window.scrollY,
        left: rect.left + rect.width / 2 + window.scrollX,
      });
    }

    const startTime = performance.now();
    const startValue = displayValue;
    const endValue = value;

    // Easing function for smooth animation
    const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3);

    const animate = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easedProgress = easeOutCubic(progress);

      const currentValue = Math.round(startValue + (endValue - startValue) * easedProgress);
      setDisplayValue(currentValue);

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      } else {
        setIsAnimating(false);
        previousValueRef.current = value;
        // Hide pulse effect
        setTimeout(() => setShowPulse(false), 300);
        // Hide change indicator after animation
        setTimeout(() => {
          setChangeAmount(0);
          setChangeIndicatorPosition(null);
        }, 2000);
      }
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [value, duration, displayValue, showChange]);

  return (
    <>
      <span ref={counterRef} className={cn('relative inline-flex items-center', className)}>
        {/* Pulse effect background */}
        {showPulse && (
          <span
            className={cn(
              'absolute inset-0 animate-ping rounded-md',
              changeAmount > 0 ? 'bg-green-400/20' : 'bg-red-400/20'
            )}
          />
        )}

        <span
          className={cn(
            'relative transition-all duration-300',
            isAnimating && 'scale-110 font-bold',
            isAnimating && changeAmount > 0 && 'text-green-600 dark:text-green-400',
            isAnimating && changeAmount < 0 && 'text-red-600 dark:text-red-400'
          )}
        >
          {displayValue}
          {suffix}
        </span>
      </span>

      {/* Change indicator rendered as portal */}
      {showChange &&
        changeAmount !== 0 &&
        changeIndicatorPosition &&
        typeof document !== 'undefined' &&
        createPortal(
          <div
            className={cn(
              'pointer-events-none fixed z-50',
              'animate-in fade-in-0 slide-in-from-bottom-4 zoom-in-95 duration-500'
            )}
            style={{
              top: changeIndicatorPosition.top - 20,
              left: changeIndicatorPosition.left,
              transform: 'translateX(-50%)',
              animation: 'floatUp 2s ease-out forwards',
            }}
          >
            <span
              className={cn(
                'text-sm font-bold',
                changeAmount > 0 ? 'text-green-500' : 'text-red-500'
              )}
              style={{
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.2))',
              }}
            >
              {changeAmount > 0 ? '+' : ''}
              {changeAmount}
            </span>
          </div>,
          document.body
        )}

      <style jsx>{`
        @keyframes floatUp {
          0% {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
          }
          70% {
            opacity: 1;
            transform: translateX(-50%) translateY(-12px);
          }
          100% {
            opacity: 0;
            transform: translateX(-50%) translateY(-16px);
          }
        }
      `}</style>
    </>
  );
}
