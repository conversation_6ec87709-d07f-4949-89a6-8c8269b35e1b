'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function ForgotPasswordBlock() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Falha ao enviar e-mail de redefinição');
      }

      setSuccess(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Falha ao enviar e-mail de redefinição');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-sm space-y-6 p-6">
      {/* Main heading and subheading */}
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-bold tracking-tight">Esqueceu sua senha?</h1>
        <p className="text-muted-foreground text-sm">
          Digite seu e-mail e enviaremos um link para redefinir sua senha
        </p>
      </div>

      {/* Success message */}
      {success && (
        <div className="auth-success">
          Verifique seu e-mail para instruções de redefinição de senha.
        </div>
      )}

      {/* Error message */}
      {error && <div className="auth-error">{error}</div>}

      {/* Forgot password form */}
      <form className="space-y-4" onSubmit={handleSubmit}>
        {/* Email input */}
        <div className="space-y-2">
          <Label htmlFor="email">E-mail</Label>
          <Input
            id="email"
            type="email"
            placeholder="Digite seu e-mail"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={success}
          />
        </div>

        {/* Submit button */}
        <Button
          type="submit"
          className="w-full bg-[#2563eb] text-white hover:bg-[#3b82f6]"
          disabled={loading || success}
        >
          {loading ? 'Enviando...' : success ? 'E-mail enviado' : 'Enviar link de redefinição'}
        </Button>
      </form>

      {/* Back to login */}
      <div className="text-center text-sm">
        <Link href="/auth/signin" className="text-primary font-medium hover:underline">
          Voltar para login
        </Link>
      </div>
    </div>
  );
}
