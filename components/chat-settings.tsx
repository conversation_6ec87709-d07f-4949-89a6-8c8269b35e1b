'use client';

import React, { useState, useEffect } from 'react';
import { Settings2, Save, Plus, Trash2, Edit2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogT<PERSON>le,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/use-auth';
import { ModelConfig } from '@/lib/openrouter/client';

interface ModelLimits {
  temperature?: { min: number; max: number; default: number };
  maxTokens?: { min: number; max: number; default: number };
  topP?: { min: number; max: number; default: number };
  frequencyPenalty?: { min: number; max: number; default: number };
  presencePenalty?: { min: number; max: number; default: number };
  contextLength?: number;
  maxCompletionTokens?: number;
  reasoningMaxTokens?: { min: number; max: number; default: number };
}

export interface ModelConfigWithLimits extends ModelConfig {
  customLimits?: ModelLimits;
}

export interface ChatSettingsConfig {
  systemPrompt: string;
  temperature: number;
  maxTokens: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
  reasoning?: {
    enabled?: boolean;
    effort?: 'low' | 'medium' | 'high';
    maxTokens?: number;
    exclude?: boolean;
  };
  availableModels: ModelConfigWithLimits[];
  defaultModel?: string;
}

interface ChatSettingsProps {
  settings: ChatSettingsConfig;
  onSettingsChange: (settings: ChatSettingsConfig) => void;
}

export function ChatSettings({ settings, onSettingsChange }: ChatSettingsProps) {
  const { hasRole } = useAuth();
  const [open, setOpen] = useState(false);
  const [localSettings, setLocalSettings] = useState<ChatSettingsConfig>(settings);
  const [newModelValue, setNewModelValue] = useState('');
  const [newModelLabel, setNewModelLabel] = useState('');
  const [newModelCategory, setNewModelCategory] = useState('');
  const [saving, setSaving] = useState(false);
  const [selectedModelForParameters, setSelectedModelForParameters] = useState<string>('');
  const [editingModelIndex, setEditingModelIndex] = useState<number | null>(null);

  // Update selected model when default model changes
  useEffect(() => {
    if (localSettings.defaultModel) {
      setSelectedModelForParameters(localSettings.defaultModel);
    }
  }, [localSettings.defaultModel]);

  // Default limits for all models
  const defaultLimits: Required<ModelLimits> = {
    temperature: { min: 0, max: 2, default: 0.7 },
    maxTokens: { min: 1, max: 4000, default: 1000 },
    topP: { min: 0, max: 1, default: 1 },
    frequencyPenalty: { min: -2, max: 2, default: 0 },
    presencePenalty: { min: -2, max: 2, default: 0 },
    contextLength: 128000,
    maxCompletionTokens: 4000,
    reasoningMaxTokens: { min: 100, max: 4000, default: 1000 },
  };

  // Get limits for selected model or use defaults
  const getModelLimits = (modelValue: string): Required<ModelLimits> => {
    const model = localSettings.availableModels.find((m) => m.value === modelValue);
    if (model?.customLimits) {
      return {
        ...defaultLimits,
        ...model.customLimits,
      };
    }
    return defaultLimits;
  };

  const modelLimits = selectedModelForParameters
    ? getModelLimits(selectedModelForParameters)
    : defaultLimits;

  // Only show for admin users
  if (!hasRole('admin')) {
    return null;
  }

  // Don't show if no settings are available
  if (!settings.availableModels || settings.availableModels.length === 0) {
    return null;
  }

  const handleSave = async () => {
    setSaving(true);
    try {
      // Get current active settings ID
      const activeResponse = await fetch('/api/model-settings/active');
      if (!activeResponse.ok) {
        alert('Failed to fetch active settings.');
        return;
      }

      const activeSettings = await activeResponse.json();
      if (!activeSettings || !activeSettings.id) {
        alert(
          'No active settings found. Please go to /admin/model-settings to initialize settings first.'
        );
        return;
      }

      // Update the settings
      const updateResponse = await fetch(`/api/admin/model-settings/${activeSettings.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          systemPrompt: localSettings.systemPrompt,
          defaultModel: localSettings.defaultModel,
          temperature: localSettings.temperature,
          maxTokens: localSettings.maxTokens,
          topP: localSettings.topP,
          frequencyPenalty: localSettings.frequencyPenalty,
          presencePenalty: localSettings.presencePenalty,
          reasoning: localSettings.reasoning,
          availableModels: localSettings.availableModels,
        }),
      });

      if (updateResponse.ok) {
        onSettingsChange(localSettings);
        setOpen(false);
        // Reload the page to get updated settings
        window.location.reload();
      } else {
        const error = await updateResponse.text();
        alert(`Failed to save settings: ${error}`);
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Error saving settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    setLocalSettings(settings);
  };

  const handleAddModel = () => {
    if (newModelValue.trim()) {
      const newModel: ModelConfigWithLimits = {
        value: newModelValue.trim(),
        label: newModelLabel.trim() || newModelValue.trim(),
        category: newModelCategory.trim() || 'Custom',
        enabled: true,
      };
      setLocalSettings({
        ...localSettings,
        availableModels: [...localSettings.availableModels, newModel],
      });
      setNewModelValue('');
      setNewModelLabel('');
      setNewModelCategory('');
    }
  };

  const handleUpdateModelLimits = (index: number, field: string, value: number) => {
    const updatedModels = [...localSettings.availableModels];
    if (!updatedModels[index].customLimits) {
      updatedModels[index].customLimits = {};
    }

    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      const parentKey = parent as keyof ModelLimits;

      if (parentKey === 'contextLength' || parentKey === 'maxCompletionTokens') {
        updatedModels[index].customLimits![parentKey] = value;
      } else if (parent === 'reasoningMaxTokens') {
        if (!updatedModels[index].customLimits!.reasoningMaxTokens) {
          updatedModels[index].customLimits!.reasoningMaxTokens = {
            min: 100,
            max: 4000,
            default: 1000,
          };
        }
        const limitObj = updatedModels[index].customLimits!.reasoningMaxTokens;
        if (child === 'min') limitObj.min = value;
        else if (child === 'max') limitObj.max = value;
        else if (child === 'default') limitObj.default = value;
      } else {
        if (!updatedModels[index].customLimits![parentKey]) {
          updatedModels[index].customLimits![parentKey] = {
            min: 0,
            max: 1,
            default: 0.5,
          };
        }
        const limitObj = updatedModels[index].customLimits![parentKey] as {
          min: number;
          max: number;
          default: number;
        };
        if (child === 'min') limitObj.min = value;
        else if (child === 'max') limitObj.max = value;
        else if (child === 'default') limitObj.default = value;
      }
    }

    setLocalSettings({
      ...localSettings,
      availableModels: updatedModels,
    });
  };

  const handleRemoveModel = (index: number) => {
    setLocalSettings({
      ...localSettings,
      availableModels: localSettings.availableModels.filter((_, i) => i !== index),
    });
  };

  const handleToggleModel = (index: number) => {
    const updatedModels = [...localSettings.availableModels];
    updatedModels[index] = {
      ...updatedModels[index],
      enabled: !updatedModels[index].enabled,
    };
    setLocalSettings({
      ...localSettings,
      availableModels: updatedModels,
    });
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen) {
      // Reset local settings to current settings when opening
      setLocalSettings(settings);
    }
    setOpen(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" title="Admin Settings">
          <Settings2 className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Chat Settings (Admin)</DialogTitle>
          <DialogDescription>
            Configure system prompt, model parameters, and manage available models.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="system" className="mt-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="system">System Prompt</TabsTrigger>
            <TabsTrigger value="parameters">Parameters</TabsTrigger>
            <TabsTrigger value="models">Models</TabsTrigger>
          </TabsList>

          <TabsContent value="system" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="system-prompt">System Prompt</Label>
              <Textarea
                id="system-prompt"
                value={localSettings.systemPrompt}
                onChange={(e) =>
                  setLocalSettings({ ...localSettings, systemPrompt: e.target.value })
                }
                rows={8}
                placeholder="Enter system instructions here..."
                className="font-mono text-sm"
              />
              <p className="text-muted-foreground text-sm">
                Define the AI assistant&apos;s behavior and knowledge scope.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="parameters" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Model Parameters</CardTitle>
                <CardDescription>
                  Fine-tune the model&apos;s behavior and output characteristics.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Model Selection for Parameters */}
                <div className="space-y-2">
                  <Label htmlFor="parameter-model">Select Model for Parameter Limits</Label>
                  <select
                    id="parameter-model"
                    className="border-input bg-background w-full rounded-md border px-3 py-2"
                    value={selectedModelForParameters}
                    onChange={(e) => setSelectedModelForParameters(e.target.value)}
                  >
                    <option value="">Select a model...</option>
                    {localSettings.availableModels
                      .filter((m) => m.enabled)
                      .map((model) => (
                        <option key={model.value} value={model.value}>
                          {model.label} ({model.category})
                        </option>
                      ))}
                  </select>
                  {selectedModelForParameters && modelLimits.contextLength && (
                    <p className="text-muted-foreground text-xs">
                      Context Length: {modelLimits.contextLength.toLocaleString()} tokens
                      {modelLimits.maxCompletionTokens &&
                        `, Max Output: ${modelLimits.maxCompletionTokens.toLocaleString()} tokens`}
                    </p>
                  )}
                </div>
                {/* Temperature */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="temperature">Temperature</Label>
                    <span className="text-muted-foreground text-sm">
                      {localSettings.temperature.toFixed(2)}
                    </span>
                  </div>
                  <Slider
                    id="temperature"
                    min={modelLimits.temperature.min}
                    max={modelLimits.temperature.max}
                    step={0.01}
                    value={[localSettings.temperature]}
                    onValueChange={([value]) =>
                      setLocalSettings({ ...localSettings, temperature: value })
                    }
                  />
                  <p className="text-muted-foreground text-xs">
                    Controls randomness. Lower = more focused, Higher = more creative.
                    {selectedModelForParameters &&
                      ` (${modelLimits.temperature.min}-${modelLimits.temperature.max})`}
                  </p>
                </div>

                {/* Max Tokens */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="max-tokens">Max Tokens</Label>
                    <span className="text-muted-foreground text-sm">{localSettings.maxTokens}</span>
                  </div>
                  <Slider
                    id="max-tokens"
                    min={modelLimits.maxTokens.min}
                    max={modelLimits.maxTokens.max}
                    step={100}
                    value={[localSettings.maxTokens]}
                    onValueChange={([value]) =>
                      setLocalSettings({ ...localSettings, maxTokens: value })
                    }
                  />
                  <p className="text-muted-foreground text-xs">
                    Maximum number of tokens in the response.
                    {selectedModelForParameters &&
                      ` (${modelLimits.maxTokens.min}-${modelLimits.maxTokens.max})`}
                  </p>
                </div>

                {/* Top P */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="top-p">Top P</Label>
                    <span className="text-muted-foreground text-sm">
                      {localSettings.topP.toFixed(2)}
                    </span>
                  </div>
                  <Slider
                    id="top-p"
                    min={modelLimits.topP.min}
                    max={modelLimits.topP.max}
                    step={0.01}
                    value={[localSettings.topP]}
                    onValueChange={([value]) => setLocalSettings({ ...localSettings, topP: value })}
                  />
                  <p className="text-muted-foreground text-xs">
                    Nucleus sampling threshold. Alternative to temperature.
                    {selectedModelForParameters &&
                      ` (${modelLimits.topP.min}-${modelLimits.topP.max})`}
                  </p>
                </div>

                {/* Frequency Penalty */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="frequency-penalty">Frequency Penalty</Label>
                    <span className="text-muted-foreground text-sm">
                      {localSettings.frequencyPenalty.toFixed(2)}
                    </span>
                  </div>
                  <Slider
                    id="frequency-penalty"
                    min={modelLimits.frequencyPenalty.min}
                    max={modelLimits.frequencyPenalty.max}
                    step={0.01}
                    value={[localSettings.frequencyPenalty]}
                    onValueChange={([value]) =>
                      setLocalSettings({ ...localSettings, frequencyPenalty: value })
                    }
                  />
                  <p className="text-muted-foreground text-xs">
                    Reduces repetition of token sequences.
                    {selectedModelForParameters &&
                      ` (${modelLimits.frequencyPenalty.min} to ${modelLimits.frequencyPenalty.max})`}
                  </p>
                </div>

                {/* Presence Penalty */}
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor="presence-penalty">Presence Penalty</Label>
                    <span className="text-muted-foreground text-sm">
                      {localSettings.presencePenalty.toFixed(2)}
                    </span>
                  </div>
                  <Slider
                    id="presence-penalty"
                    min={modelLimits.presencePenalty.min}
                    max={modelLimits.presencePenalty.max}
                    step={0.01}
                    value={[localSettings.presencePenalty]}
                    onValueChange={([value]) =>
                      setLocalSettings({ ...localSettings, presencePenalty: value })
                    }
                  />
                  <p className="text-muted-foreground text-xs">
                    Reduces repetition of topics.
                    {selectedModelForParameters &&
                      ` (${modelLimits.presencePenalty.min} to ${modelLimits.presencePenalty.max})`}
                  </p>
                </div>

                {/* Reasoning Settings */}
                <div className="space-y-4 border-t pt-4">
                  <h4 className="font-medium">Reasoning Settings</h4>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="reasoning-enabled"
                      checked={localSettings.reasoning?.enabled || false}
                      onCheckedChange={(checked) =>
                        setLocalSettings({
                          ...localSettings,
                          reasoning: {
                            ...localSettings.reasoning,
                            enabled: checked,
                            effort: localSettings.reasoning?.effort || 'medium',
                            maxTokens: localSettings.reasoning?.maxTokens || 1000,
                          },
                        })
                      }
                    />
                    <Label htmlFor="reasoning-enabled">Enable Reasoning</Label>
                  </div>

                  {localSettings.reasoning?.enabled && (
                    <>
                      <div className="space-y-2">
                        <Label htmlFor="reasoning-effort">Reasoning Effort</Label>
                        <select
                          id="reasoning-effort"
                          className="border-input bg-background w-full rounded-md border px-3 py-2"
                          value={localSettings.reasoning.effort}
                          onChange={(e) =>
                            setLocalSettings({
                              ...localSettings,
                              reasoning: {
                                ...localSettings.reasoning!,
                                effort: e.target.value as 'low' | 'medium' | 'high',
                              },
                            })
                          }
                        >
                          <option value="low">Low</option>
                          <option value="medium">Medium</option>
                          <option value="high">High</option>
                        </select>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <Label htmlFor="reasoning-max-tokens">Reasoning Max Tokens</Label>
                          <span className="text-muted-foreground text-sm">
                            {localSettings.reasoning.maxTokens}
                          </span>
                        </div>
                        <Slider
                          id="reasoning-max-tokens"
                          min={
                            selectedModelForParameters &&
                            localSettings.availableModels.find(
                              (m) => m.value === selectedModelForParameters
                            )?.customLimits?.reasoningMaxTokens?.min
                              ? localSettings.availableModels.find(
                                  (m) => m.value === selectedModelForParameters
                                )!.customLimits!.reasoningMaxTokens!.min
                              : 100
                          }
                          max={
                            selectedModelForParameters &&
                            localSettings.availableModels.find(
                              (m) => m.value === selectedModelForParameters
                            )?.customLimits?.reasoningMaxTokens?.max
                              ? localSettings.availableModels.find(
                                  (m) => m.value === selectedModelForParameters
                                )!.customLimits!.reasoningMaxTokens!.max
                              : Math.min(modelLimits.maxTokens.max, 4000)
                          }
                          step={100}
                          value={[localSettings.reasoning.maxTokens || 1000]}
                          onValueChange={([value]) =>
                            setLocalSettings({
                              ...localSettings,
                              reasoning: {
                                ...localSettings.reasoning!,
                                maxTokens: value,
                              },
                            })
                          }
                        />
                        <p className="text-muted-foreground text-xs">
                          Maximum tokens for reasoning/thinking process.
                          {selectedModelForParameters &&
                          localSettings.availableModels.find(
                            (m) => m.value === selectedModelForParameters
                          )?.customLimits?.reasoningMaxTokens
                            ? ` (${localSettings.availableModels.find((m) => m.value === selectedModelForParameters)!.customLimits!.reasoningMaxTokens!.min}-${localSettings.availableModels.find((m) => m.value === selectedModelForParameters)!.customLimits!.reasoningMaxTokens!.max})`
                            : ` (100-${Math.min(modelLimits.maxTokens.max, 4000)})`}
                        </p>
                      </div>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="models" className="space-y-4">
            {/* Default Model Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Default Model</CardTitle>
                <CardDescription>Select the default model for new conversations.</CardDescription>
              </CardHeader>
              <CardContent>
                <select
                  className="border-input bg-background w-full rounded-md border px-3 py-2"
                  value={localSettings.defaultModel || ''}
                  onChange={(e) =>
                    setLocalSettings({ ...localSettings, defaultModel: e.target.value })
                  }
                >
                  <option value="">Select a default model...</option>
                  {localSettings.availableModels
                    .filter((m) => m.enabled)
                    .map((model) => (
                      <option key={model.value} value={model.value}>
                        {model.label} ({model.category})
                      </option>
                    ))}
                </select>
              </CardContent>
            </Card>

            {/* Available Models Management */}
            <Card>
              <CardHeader>
                <CardTitle>Available Models</CardTitle>
                <CardDescription>
                  Configure which models are available to users. Add new models or toggle existing
                  ones.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Add new model form */}
                <div className="space-y-4 border-b pb-4">
                  <div className="grid gap-4 sm:grid-cols-3">
                    <div className="space-y-2">
                      <Label htmlFor="model-value">Model ID</Label>
                      <Input
                        id="model-value"
                        value={newModelValue}
                        onChange={(e) => setNewModelValue(e.target.value)}
                        placeholder="provider/model-name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="model-label">Display Name</Label>
                      <Input
                        id="model-label"
                        value={newModelLabel}
                        onChange={(e) => setNewModelLabel(e.target.value)}
                        placeholder="Friendly name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="model-category">Category</Label>
                      <Input
                        id="model-category"
                        value={newModelCategory}
                        onChange={(e) => setNewModelCategory(e.target.value)}
                        placeholder="e.g., Custom, OpenAI"
                      />
                    </div>
                  </div>
                  <Button onClick={handleAddModel} className="w-full sm:w-auto">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Model
                  </Button>
                </div>

                {/* Models list grouped by category */}
                <div className="space-y-4">
                  {Object.entries(
                    localSettings.availableModels.reduce(
                      (acc, model, index) => {
                        const category = model.category || 'Other';
                        if (!acc[category]) acc[category] = [];
                        acc[category].push({ ...model, index });
                        return acc;
                      },
                      {} as Record<string, (ModelConfigWithLimits & { index: number })[]>
                    )
                  ).map(([category, models]) => (
                    <div key={category} className="space-y-2">
                      <h4 className="text-sm font-medium">{category}</h4>
                      <div className="space-y-1">
                        {models.map((model) => {
                          const modelLimits = model.customLimits
                            ? {
                                ...getModelLimits(model.value),
                                ...model.customLimits,
                              }
                            : getModelLimits(model.value);

                          return (
                            <div key={model.value}>
                              <div className="flex items-center justify-between rounded-lg border p-2">
                                <div className="flex items-center space-x-2">
                                  <Switch
                                    checked={model.enabled}
                                    onCheckedChange={() => handleToggleModel(model.index)}
                                  />
                                  <div>
                                    <p className="text-sm font-medium">{model.label}</p>
                                    <p className="text-muted-foreground text-xs">{model.value}</p>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() =>
                                      setEditingModelIndex(
                                        editingModelIndex === model.index ? null : model.index
                                      )
                                    }
                                    title="Edit model limits"
                                  >
                                    <Edit2 className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleRemoveModel(model.index)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>

                              {editingModelIndex === model.index && (
                                <div className="bg-muted/50 mt-2 space-y-4 rounded-lg border p-4">
                                  <h4 className="text-sm font-medium">
                                    Custom Parameter Limits for {model.label}
                                  </h4>

                                  {/* Temperature */}
                                  <div className="space-y-2">
                                    <Label>Temperature Range</Label>
                                    <div className="grid grid-cols-2 gap-2">
                                      <div>
                                        <Label
                                          htmlFor={`temp-min-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Min
                                        </Label>
                                        <Input
                                          id={`temp-min-${model.index}`}
                                          type="number"
                                          step="0.1"
                                          value={
                                            model.customLimits?.temperature?.min ??
                                            modelLimits.temperature.min
                                          }
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'temperature.min',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                      <div>
                                        <Label
                                          htmlFor={`temp-max-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Max
                                        </Label>
                                        <Input
                                          id={`temp-max-${model.index}`}
                                          type="number"
                                          step="0.1"
                                          value={
                                            model.customLimits?.temperature?.max ??
                                            modelLimits.temperature.max
                                          }
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'temperature.max',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                    </div>
                                  </div>

                                  {/* Max Tokens */}
                                  <div className="space-y-2">
                                    <Label>Max Tokens Range</Label>
                                    <div className="grid grid-cols-2 gap-2">
                                      <div>
                                        <Label
                                          htmlFor={`tokens-min-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Min
                                        </Label>
                                        <Input
                                          id={`tokens-min-${model.index}`}
                                          type="number"
                                          value={
                                            model.customLimits?.maxTokens?.min ??
                                            modelLimits.maxTokens.min
                                          }
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'maxTokens.min',
                                              parseInt(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                      <div>
                                        <Label
                                          htmlFor={`tokens-max-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Max
                                        </Label>
                                        <Input
                                          id={`tokens-max-${model.index}`}
                                          type="number"
                                          value={
                                            model.customLimits?.maxTokens?.max ??
                                            modelLimits.maxTokens.max
                                          }
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'maxTokens.max',
                                              parseInt(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                    </div>
                                  </div>

                                  {/* Top P */}
                                  <div className="space-y-2">
                                    <Label>Top P Range</Label>
                                    <div className="grid grid-cols-2 gap-2">
                                      <div>
                                        <Label
                                          htmlFor={`topp-min-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Min
                                        </Label>
                                        <Input
                                          id={`topp-min-${model.index}`}
                                          type="number"
                                          step="0.01"
                                          value={
                                            model.customLimits?.topP?.min ?? modelLimits.topP.min
                                          }
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'topP.min',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                      <div>
                                        <Label
                                          htmlFor={`topp-max-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Max
                                        </Label>
                                        <Input
                                          id={`topp-max-${model.index}`}
                                          type="number"
                                          step="0.01"
                                          value={
                                            model.customLimits?.topP?.max ?? modelLimits.topP.max
                                          }
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'topP.max',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                    </div>
                                  </div>

                                  {/* Frequency Penalty */}
                                  <div className="space-y-2">
                                    <Label>Frequency Penalty Range</Label>
                                    <div className="grid grid-cols-2 gap-2">
                                      <div>
                                        <Label
                                          htmlFor={`freq-min-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Min
                                        </Label>
                                        <Input
                                          id={`freq-min-${model.index}`}
                                          type="number"
                                          step="0.01"
                                          value={
                                            model.customLimits?.frequencyPenalty?.min ??
                                            modelLimits.frequencyPenalty.min
                                          }
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'frequencyPenalty.min',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                      <div>
                                        <Label
                                          htmlFor={`freq-max-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Max
                                        </Label>
                                        <Input
                                          id={`freq-max-${model.index}`}
                                          type="number"
                                          step="0.01"
                                          value={
                                            model.customLimits?.frequencyPenalty?.max ??
                                            modelLimits.frequencyPenalty.max
                                          }
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'frequencyPenalty.max',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                    </div>
                                  </div>

                                  {/* Presence Penalty */}
                                  <div className="space-y-2">
                                    <Label>Presence Penalty Range</Label>
                                    <div className="grid grid-cols-2 gap-2">
                                      <div>
                                        <Label
                                          htmlFor={`pres-min-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Min
                                        </Label>
                                        <Input
                                          id={`pres-min-${model.index}`}
                                          type="number"
                                          step="0.01"
                                          value={
                                            model.customLimits?.presencePenalty?.min ??
                                            modelLimits.presencePenalty.min
                                          }
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'presencePenalty.min',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                      <div>
                                        <Label
                                          htmlFor={`pres-max-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Max
                                        </Label>
                                        <Input
                                          id={`pres-max-${model.index}`}
                                          type="number"
                                          step="0.01"
                                          value={
                                            model.customLimits?.presencePenalty?.max ??
                                            modelLimits.presencePenalty.max
                                          }
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'presencePenalty.max',
                                              parseFloat(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                    </div>
                                  </div>

                                  {/* Reasoning Max Tokens */}
                                  <div className="space-y-2">
                                    <Label>Reasoning Max Tokens Range</Label>
                                    <div className="grid grid-cols-2 gap-2">
                                      <div>
                                        <Label
                                          htmlFor={`reasoning-min-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Min
                                        </Label>
                                        <Input
                                          id={`reasoning-min-${model.index}`}
                                          type="number"
                                          value={model.customLimits?.reasoningMaxTokens?.min ?? 100}
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'reasoningMaxTokens.min',
                                              parseInt(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                      <div>
                                        <Label
                                          htmlFor={`reasoning-max-${model.index}`}
                                          className="text-muted-foreground text-xs"
                                        >
                                          Max
                                        </Label>
                                        <Input
                                          id={`reasoning-max-${model.index}`}
                                          type="number"
                                          value={
                                            model.customLimits?.reasoningMaxTokens?.max ?? 4000
                                          }
                                          onChange={(e) =>
                                            handleUpdateModelLimits(
                                              model.index,
                                              'reasoningMaxTokens.max',
                                              parseInt(e.target.value)
                                            )
                                          }
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ))}
                  {localSettings.availableModels.length === 0 && (
                    <p className="text-muted-foreground py-4 text-center">
                      No models configured. Add some models to get started.
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={handleReset}>
            Reset
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            <Save className="mr-2 h-4 w-4" />
            {saving ? 'Saving...' : 'Save Settings'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
