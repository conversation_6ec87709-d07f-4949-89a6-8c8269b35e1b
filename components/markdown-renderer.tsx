'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { cn } from '@/lib/utils';
import { Copy, Check, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import './markdown-renderer.css';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export function MarkdownRenderer({ content, className }: MarkdownRendererProps) {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [collapsedBlocks, setCollapsedBlocks] = useState<Set<number>>(new Set());

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const toggleCollapse = (index: number) => {
    setCollapsedBlocks((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  let codeBlockIndex = 0;

  return (
    <div className={cn('markdown-chat-content', className)}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          // Customize code blocks
          pre: ({ children, node, ...props }) => {
            // Extract language from the code element
            const codeElement = node?.children[0];
            let language = '';

            // Check for language in className array
            if (codeElement?.type === 'element' && codeElement?.properties?.className) {
              const classNames = codeElement.properties.className;
              // Look for the actual language class, not just 'hljs'
              if (Array.isArray(classNames)) {
                const langClass = classNames.find(
                  (cls) =>
                    typeof cls === 'string' && cls.startsWith('language-') && !cls.includes('hljs')
                );

                if (langClass && typeof langClass === 'string') {
                  language = langClass.replace('language-', '');
                } else {
                  // Try to find language from 'hljs' combined classes like 'hljs-html'
                  const hlsjClass = classNames.find(
                    (cls) => typeof cls === 'string' && cls.startsWith('hljs-') && cls !== 'hljs'
                  );
                  if (hlsjClass && typeof hlsjClass === 'string') {
                    language = hlsjClass.replace('hljs-', '');
                  }
                }
              }
            }

            // Get the raw text content from the code element
            let codeText = '';
            if (codeElement?.type === 'element' && codeElement?.children) {
              const extractText = (nodes: unknown[]): string => {
                return nodes
                  .map((node) => {
                    if (typeof node === 'string') return node;
                    if (typeof node === 'object' && node !== null) {
                      const nodeObj = node as {
                        type?: string;
                        value?: string;
                        children?: unknown[];
                      };
                      if (nodeObj.type === 'text' && nodeObj.value) return nodeObj.value;
                      if (nodeObj.children) return extractText(nodeObj.children);
                    }
                    return '';
                  })
                  .join('');
              };
              codeText = extractText(codeElement.children);
            }

            const currentIndex = codeBlockIndex++;
            const isCollapsed = collapsedBlocks.has(currentIndex);

            // Count lines for collapse decision
            const lineCount = codeText.split('\n').length;
            const shouldShowCollapse = lineCount > 10;

            return (
              <Collapsible open={!isCollapsed} className="my-4">
                <div className="group border-border bg-muted/30 relative overflow-hidden rounded-lg border">
                  <div className="border-border bg-muted/50 flex items-center justify-between border-b px-4 py-2">
                    <div className="flex items-center gap-2">
                      {language && language !== 'hljs' && (
                        <span className="text-muted-foreground font-mono text-xs">{language}</span>
                      )}
                      {!language && (
                        <span className="text-muted-foreground font-mono text-xs">plaintext</span>
                      )}
                      {shouldShowCollapse && (
                        <span className="text-muted-foreground text-xs">• {lineCount} lines</span>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-7 w-7"
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          copyToClipboard(codeText, currentIndex);
                        }}
                        type="button"
                        title="Copy code"
                      >
                        {copiedIndex === currentIndex ? (
                          <Check className="h-3.5 w-3.5 text-green-500" />
                        ) : (
                          <Copy className="h-3.5 w-3.5" />
                        )}
                      </Button>
                      {shouldShowCollapse && (
                        <CollapsibleTrigger asChild>
                          <Button
                            size="icon"
                            variant="ghost"
                            className="h-7 w-7"
                            onClick={() => toggleCollapse(currentIndex)}
                            type="button"
                            title={isCollapsed ? 'Expand code' : 'Collapse code'}
                          >
                            {isCollapsed ? (
                              <ChevronDown className="h-3.5 w-3.5" />
                            ) : (
                              <ChevronUp className="h-3.5 w-3.5" />
                            )}
                          </Button>
                        </CollapsibleTrigger>
                      )}
                    </div>
                  </div>
                  <CollapsibleContent>
                    <div className="relative overflow-hidden">
                      <pre
                        {...props}
                        className="bg-background/50 m-0 overflow-x-auto text-sm"
                        data-language={language}
                      >
                        {children}
                      </pre>
                    </div>
                  </CollapsibleContent>
                  {isCollapsed && (
                    <div className="text-muted-foreground bg-muted/30 px-4 py-2 text-center text-xs">
                      Code collapsed • Click to expand
                    </div>
                  )}
                </div>
              </Collapsible>
            );
          },
          code: ({ className, children, ...props }) => {
            const match = /language-(\w+)/.exec(className || '');
            const isInline = !match;

            if (isInline) {
              return (
                <code {...props} className="bg-muted rounded px-1.5 py-0.5 font-mono text-sm">
                  {children}
                </code>
              );
            }

            return (
              <code {...props} className={className}>
                {children}
              </code>
            );
          },
          // Customize links
          a: ({ children, href, ...props }) => (
            <a
              {...props}
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary underline hover:no-underline"
            >
              {children}
            </a>
          ),
          // Customize lists
          ul: ({ children, ...props }) => (
            <ul {...props} className="my-4 list-disc space-y-2 pl-5">
              {children}
            </ul>
          ),
          ol: ({ children, ...props }) => (
            <ol {...props} className="my-4 list-decimal space-y-2 pl-5">
              {children}
            </ol>
          ),
          // Customize blockquotes
          blockquote: ({ children, ...props }) => (
            <blockquote
              {...props}
              className="border-primary/30 text-muted-foreground my-4 border-l-4 pl-4"
            >
              {children}
            </blockquote>
          ),
          // Customize tables
          table: ({ children, ...props }) => (
            <div className="overflow-x-auto">
              <table {...props} className="w-full border-collapse">
                {children}
              </table>
            </div>
          ),
          th: ({ children, ...props }) => (
            <th
              {...props}
              className="border-muted bg-muted/50 border px-4 py-2 text-left font-semibold"
            >
              {children}
            </th>
          ),
          td: ({ children, ...props }) => (
            <td {...props} className="border-muted border px-4 py-2">
              {children}
            </td>
          ),
          // Customize horizontal rules
          hr: ({ ...props }) => (
            <hr {...props} className="bg-muted-foreground/20 my-4 h-0.5 border-0" />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}
