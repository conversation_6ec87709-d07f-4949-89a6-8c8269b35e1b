import React from 'react';
import { ChatMessage } from '@/lib/openrouter/client';
import { cn } from '@/lib/utils';

interface ChatMessageMetadataProps {
  metadata?: ChatMessage['metadata'];
  className?: string;
}

export function ChatMessageMetadata({ metadata, className }: ChatMessageMetadataProps) {
  if (!metadata) return null;

  const formatModel = (model: string) => {
    // Extract the model name after the provider
    const parts = model.split('/');
    return parts[parts.length - 1] || model;
  };

  const formatSpeed = (speed: number) => {
    return speed.toFixed(2);
  };

  const formatTime = (seconds: number) => {
    if (seconds < 1) {
      return `${(seconds * 1000).toFixed(0)}ms`;
    }
    return `${seconds.toFixed(1)}s`;
  };

  const formatCost = (cost: number) => {
    return `$${cost.toFixed(4)}`;
  };

  return (
    <div
      className={cn(
        'text-muted-foreground mt-2 flex flex-wrap items-center gap-3 text-xs',
        className
      )}
    >
      {metadata.model && <span className="font-medium">{formatModel(metadata.model)}</span>}

      {/* Usage Information */}
      {metadata.usage && (
        <>
          {metadata.usage.completionTokens !== undefined && (
            <span>{metadata.usage.completionTokens} tokens</span>
          )}
          {metadata.usage.totalTokens !== undefined && (
            <span className="text-muted-foreground/70">
              ({metadata.usage.promptTokens || 0} + {metadata.usage.completionTokens || 0})
            </span>
          )}
          {metadata.usage.cost !== undefined && <span>{formatCost(metadata.usage.cost)}</span>}
          {metadata.usage.cachedTokens !== undefined && metadata.usage.cachedTokens > 0 && (
            <span className="text-green-600 dark:text-green-400">
              {metadata.usage.cachedTokens} cached
            </span>
          )}
        </>
      )}

      {/* Performance Metrics */}
      {metadata.performance?.tokensPerSecond !== undefined && (
        <span>{formatSpeed(metadata.performance.tokensPerSecond)} tok/s</span>
      )}

      {/* Latency Information */}
      {metadata.latency && (
        <>
          {metadata.latency.timeToFirstToken !== undefined && (
            <span>TTFT: {formatTime(metadata.latency.timeToFirstToken)}</span>
          )}
          {metadata.latency.totalTime !== undefined && (
            <span>Total: {formatTime(metadata.latency.totalTime)}</span>
          )}
        </>
      )}
    </div>
  );
}
