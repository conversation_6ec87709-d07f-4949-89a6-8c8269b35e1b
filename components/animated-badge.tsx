'use client';

import React, { useEffect, useState, useRef } from 'react';
import { cn } from '@/lib/utils';

interface AnimatedBadgeProps {
  value: number;
  maxValue?: number;
  className?: string;
}

export function AnimatedBadge({ value, maxValue = 99, className }: AnimatedBadgeProps) {
  // Ensure value is a valid number
  const safeValue = typeof value === 'number' && !isNaN(value) ? value : 0;

  const [displayValue, setDisplayValue] = useState(safeValue);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationDirection, setAnimationDirection] = useState<'up' | 'down'>('up');
  const previousValueRef = useRef(safeValue);
  const animationRef = useRef<number | undefined>(undefined);
  const isInitialMount = useRef(true);

  useEffect(() => {
    const currentSafeValue = typeof value === 'number' && !isNaN(value) ? value : 0;

    // Skip animation on initial mount
    if (isInitialMount.current) {
      isInitialMount.current = false;
      previousValueRef.current = currentSafeValue;
      setDisplayValue(currentSafeValue);
      return;
    }

    const previousValue = previousValueRef.current;
    const change = currentSafeValue - previousValue;

    if (change === 0) return;

    // Cancel any ongoing animation
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }

    setAnimationDirection(change > 0 ? 'up' : 'down');
    setIsAnimating(true);
    previousValueRef.current = currentSafeValue;

    // For small changes, use a simple roll animation
    if (Math.abs(change) <= 5) {
      const duration = 600; // milliseconds
      const startTime = performance.now();
      const startValue = displayValue;
      const endValue = currentSafeValue;

      const animate = (currentTime: number) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function for smooth animation
        const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3);
        const easedProgress = easeOutCubic(progress);

        const currentValue = Math.round(startValue + (endValue - startValue) * easedProgress);
        setDisplayValue(currentValue);

        if (progress < 1) {
          animationRef.current = requestAnimationFrame(animate);
        } else {
          setIsAnimating(false);
        }
      };

      animationRef.current = requestAnimationFrame(animate);
    } else {
      // For larger changes, use a simpler animation without slot machine effect
      const duration = 800;
      const startTime = performance.now();
      const startValue = displayValue;
      const endValue = currentSafeValue;

      const animate = (currentTime: number) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Use a smooth easing for the entire animation
        const easeOutQuad = (t: number) => t * (2 - t);
        const easedProgress = easeOutQuad(progress);

        const currentValue = Math.round(startValue + (endValue - startValue) * easedProgress);
        setDisplayValue(currentValue);

        if (progress < 1) {
          animationRef.current = requestAnimationFrame(animate);
        } else {
          setDisplayValue(endValue);
          setIsAnimating(false);
        }
      };

      animationRef.current = requestAnimationFrame(animate);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]); // Remove displayValue and maxValue from dependencies

  const displayText = displayValue > maxValue ? `${maxValue}+` : displayValue.toString();

  return (
    <span
      className={cn(
        'absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-[10px] font-bold text-white',
        'animate-in zoom-in-50 duration-200',
        isAnimating && 'transition-transform',
        isAnimating && animationDirection === 'up' && 'scale-110',
        isAnimating && animationDirection === 'down' && 'scale-95',
        className
      )}
    >
      <span className={cn('relative', isAnimating && 'animate-bounce-subtle')}>{displayText}</span>
    </span>
  );
}
