'use client';

import React, { useState, useMemo } from 'react';
import {
  ChevronUp,
  ChevronDown,
  MessageSquare,
  ChevronRight,
  Send,
  Trash2,
  Edit,
  ArrowUpDown,
  Calendar,
  TrendingUp,
  AlertTriangle,
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { getRenderedContent } from '@/lib/render-content';
import { RichTextEditor } from './rich-text-editor';
import { useReputation } from '@/contexts/reputation-context';
import { AnimatedCounter } from './animated-counter';

export interface Comment {
  id: string;
  userId: string;
  username: string;
  userAvatar?: string;
  userReputation: number;
  content: string;
  timestamp: Date;
  votes: number;
  userVote?: 'up' | 'down' | null;
  parentId?: string;
  replies?: Comment[];
  isDeleted?: boolean;
  editHistory?: string[];
  editNotifications?: Array<{
    editTimestamp: Date;
    commentId: string;
  }>;
}

interface CommentSectionProps {
  comments: Comment[];
  onVote?: (commentId: string, voteType: 'up' | 'down') => void;
  onReply?: (parentId: string, content: string) => void;
  onNewComment?: (content: string) => void;
  onDelete?: (commentId: string) => void;
  onEdit?: (commentId: string, content: string) => void;
  currentUserId?: string;
  layout?: 'top' | 'side';
  className?: string;
}

function formatTimestamp(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSecs = Math.floor(diffMs / 1000);
  const diffMins = Math.floor(diffSecs / 60);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffDays > 0) return `${diffDays}d atrás`;
  if (diffHours > 0) return `${diffHours}h atrás`;
  if (diffMins > 0) return `${diffMins}min atrás`;
  return 'agora';
}

function formatAbsoluteTimestamp(date: Date): string {
  return date.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
}

function CommentItem({
  comment,
  onVote,
  onReply,
  onDelete,
  onEdit,
  currentUserId,
  level = 0,
  maxLevel = 3,
  parentIsDeleted = false,
}: {
  comment: Comment;
  onVote?: (commentId: string, voteType: 'up' | 'down') => void;
  onReply?: (parentId: string, content: string) => void;
  onDelete?: (commentId: string) => void;
  onEdit?: (commentId: string, content: string) => void;
  currentUserId?: string;
  level?: number;
  maxLevel?: number;
  parentIsDeleted?: boolean;
}) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [showReplyBox, setShowReplyBox] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);
  const [replyContent, setReplyContent] = useState('');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const hasReplies = comment.replies && comment.replies.length > 0;
  const canReply = level < maxLevel - 1;
  const isOwner = currentUserId === comment.userId;
  const { getUserReputation } = useReputation();
  const isDeletedOrHasDeletedParent = comment.isDeleted || parentIsDeleted;

  // Get the real-time reputation value
  const displayReputation = getUserReputation(comment.userId, comment.userReputation);

  // Update edit content when comment changes
  React.useEffect(() => {
    setEditContent(comment.content);
  }, [comment.content]);

  return (
    <div className={cn('relative', level > 0 && 'mt-4 ml-10')}>
      <Card className="border-border bg-background overflow-hidden p-4">
        <div className="flex gap-4">
          {/* Avatar and Voting Column */}
          <div className="flex flex-col gap-1">
            <div className="flex flex-col items-center">
              <Avatar className={cn('h-10 w-10', comment.isDeleted && 'opacity-50')}>
                {!comment.isDeleted && comment.userAvatar && (
                  <AvatarImage src={comment.userAvatar} alt={comment.username} />
                )}
                <AvatarFallback className="text-sm">
                  {comment.isDeleted ? '?' : comment.username.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>

              {/* Vote Buttons */}
              <div className="mt-2 flex flex-col items-center">
                <button
                  onClick={() => !isDeletedOrHasDeletedParent && onVote?.(comment.id, 'up')}
                  disabled={isDeletedOrHasDeletedParent}
                  className={cn(
                    'rounded p-1 transition-colors',
                    isDeletedOrHasDeletedParent && 'cursor-not-allowed opacity-50',
                    comment.userVote === 'up'
                      ? 'text-green-600 dark:text-green-400'
                      : 'text-muted-foreground hover:text-foreground'
                  )}
                >
                  <ChevronUp className="h-5 w-5" />
                </button>

                <span
                  className={cn(
                    'py-0.5 text-sm font-medium',
                    isDeletedOrHasDeletedParent && 'opacity-50',
                    comment.votes > 0 && 'text-green-600 dark:text-green-400',
                    comment.votes < 0 && 'text-red-600 dark:text-red-400',
                    comment.votes === 0 && 'text-muted-foreground'
                  )}
                >
                  {comment.votes}
                </span>

                <button
                  onClick={() => !isDeletedOrHasDeletedParent && onVote?.(comment.id, 'down')}
                  disabled={isDeletedOrHasDeletedParent}
                  className={cn(
                    'rounded p-1 transition-colors',
                    isDeletedOrHasDeletedParent && 'cursor-not-allowed opacity-50',
                    comment.userVote === 'down'
                      ? 'text-red-600 dark:text-red-400'
                      : 'text-muted-foreground hover:text-foreground'
                  )}
                >
                  <ChevronDown className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>

          {/* Comment Content */}
          <div className="flex min-w-0 flex-1 flex-col">
            <div className="flex items-center gap-2 text-sm">
              <span
                className={cn(
                  'font-medium',
                  comment.isDeleted ? 'text-muted-foreground' : 'text-foreground'
                )}
              >
                {comment.username}
              </span>
              {!comment.isDeleted && (
                <>
                  <span className="text-muted-foreground">•</span>
                  <span className="text-muted-foreground inline-flex items-center text-sm">
                    <AnimatedCounter
                      value={displayReputation}
                      duration={800}
                      suffix=" rep"
                      showChange={true}
                    />
                  </span>
                </>
              )}
              <span className="text-muted-foreground">•</span>
              <span
                className="text-muted-foreground cursor-help text-sm"
                title={formatAbsoluteTimestamp(comment.timestamp)}
              >
                {formatTimestamp(comment.timestamp)}
              </span>
              {comment.editHistory && comment.editHistory.length > 0 && (
                <>
                  <span className="text-muted-foreground">•</span>
                  <span
                    className="text-muted-foreground text-sm italic"
                    title={`Editado ${comment.editHistory.length} ${comment.editHistory.length === 1 ? 'vez' : 'vezes'}`}
                  >
                    editado
                  </span>
                </>
              )}
              {comment.isDeleted && (
                <>
                  <span className="text-muted-foreground">•</span>
                  <span className="text-sm text-red-500 italic">comentário removido</span>
                </>
              )}
            </div>

            {isEditing ? (
              <div className="flex-grow">
                <div className="mt-3">
                  <RichTextEditor
                    content={editContent}
                    onChange={setEditContent}
                    placeholder="Editar comentário..."
                    className="min-h-[100px]"
                    onSubmit={() => {
                      if (editContent.trim() && editContent !== comment.content) {
                        onEdit?.(comment.id, editContent.trim());
                        setIsEditing(false);
                      }
                    }}
                  />
                  <div className="mt-3 flex items-center justify-between">
                    <span className="text-muted-foreground text-xs">Ctrl+Enter para salvar</span>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => {
                          setEditContent(comment.content);
                          setIsEditing(false);
                        }}
                        className="text-muted-foreground hover:text-foreground px-3 py-1.5 text-xs"
                      >
                        Cancelar
                      </button>
                      <button
                        onClick={() => {
                          if (editContent.trim() && editContent !== comment.content) {
                            onEdit?.(comment.id, editContent.trim());
                            setIsEditing(false);
                          }
                        }}
                        className="rounded-md bg-black/70 px-3 py-1.5 text-xs font-medium text-white hover:bg-black/80 dark:bg-white/70 dark:text-black dark:hover:bg-white/80"
                      >
                        Salvar
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex-grow">
                {/* Edit Notification Alert */}
                {comment.editNotifications && comment.editNotifications.length > 0 && (
                  <div className="mt-2 mb-2 flex items-center gap-2 rounded-md bg-yellow-50 p-2 text-xs text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200">
                    <span className="flex-shrink-0 text-lg leading-none">⚠️</span>
                    <div className="flex-1">
                      <div className="font-medium">
                        O comentário original foi editado após esta resposta
                      </div>
                      <div className="text-xs opacity-80">
                        Última edição:{' '}
                        <span
                          className="cursor-help"
                          title={formatAbsoluteTimestamp(
                            new Date(
                              comment.editNotifications[
                                comment.editNotifications.length - 1
                              ].editTimestamp
                            )
                          )}
                        >
                          {formatTimestamp(
                            new Date(
                              comment.editNotifications[
                                comment.editNotifications.length - 1
                              ].editTimestamp
                            )
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
                <div
                  className={cn(
                    'text-foreground/90 mt-2 min-h-[3rem]',
                    comment.isDeleted && 'opacity-75'
                  )}
                  {...getRenderedContent(comment.content)}
                />
              </div>
            )}

            {/* Action Buttons */}
            <div className="mt-auto flex items-center gap-4 pt-2">
              {canReply && !isDeletedOrHasDeletedParent && (
                <button
                  onClick={() => setShowReplyBox(!showReplyBox)}
                  className="text-muted-foreground hover:text-foreground flex items-center gap-1 text-xs transition-colors"
                >
                  <MessageSquare className="h-3 w-3" />
                  Responder
                </button>
              )}

              {hasReplies && (
                <button
                  onClick={() => setIsCollapsed(!isCollapsed)}
                  className="text-muted-foreground hover:text-foreground flex items-center gap-1 text-xs transition-colors"
                >
                  <ChevronRight
                    className={cn('h-3 w-3 transition-transform', !isCollapsed && 'rotate-90')}
                  />
                  {comment.replies!.length}{' '}
                  {comment.replies!.length === 1 ? 'resposta' : 'respostas'}
                </button>
              )}

              {/* Edit button - only show for comment owner and not deleted */}
              {isOwner && onEdit && !isEditing && !comment.isDeleted && (
                <button
                  onClick={() => setIsEditing(true)}
                  className="text-muted-foreground hover:text-foreground flex items-center gap-1 text-xs transition-colors"
                  title="Editar comentário"
                >
                  <Edit className="h-3 w-3" />
                  Editar
                </button>
              )}

              {/* Delete button - only show for comment owner and not already deleted */}
              {isOwner && onDelete && !isEditing && !comment.isDeleted && (
                <button
                  onClick={() => {
                    if (hasReplies) {
                      setShowDeleteDialog(true);
                    } else {
                      onDelete(comment.id);
                    }
                  }}
                  className="flex items-center gap-1 text-xs text-red-600 transition-colors hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                  title={
                    hasReplies
                      ? 'Excluir comentário - O conteúdo será preservado para manter a integridade da discussão'
                      : 'Excluir comentário'
                  }
                >
                  <Trash2 className="h-3 w-3" />
                  Excluir
                </button>
              )}
            </div>

            {/* Reply Box with smooth animation */}
            <div
              className={`overflow-hidden transition-all duration-300 ease-in-out ${
                showReplyBox ? 'mt-4 max-h-[400px] opacity-100' : 'max-h-0 opacity-0'
              }`}
            >
              <div className="space-y-2">
                <RichTextEditor
                  content={replyContent}
                  onChange={setReplyContent}
                  placeholder="Digite sua resposta..."
                  className="min-h-[80px]"
                  onSubmit={() => {
                    if (replyContent.trim()) {
                      onReply?.(comment.id, replyContent.trim());
                      setReplyContent('');
                      setShowReplyBox(false);
                    }
                  }}
                />
                <div className="flex items-center justify-between pt-2">
                  <span className="text-muted-foreground text-xs">Ctrl+Enter para enviar</span>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => {
                        setReplyContent('');
                        setShowReplyBox(false);
                      }}
                      className="text-muted-foreground hover:text-foreground px-3 py-1.5 text-xs"
                    >
                      Cancelar
                    </button>
                    <button
                      onClick={() => {
                        if (replyContent.trim()) {
                          onReply?.(comment.id, replyContent.trim());
                          setReplyContent('');
                          setShowReplyBox(false);
                        }
                      }}
                      disabled={!replyContent.trim()}
                      className={`rounded-md px-3 py-1.5 text-xs font-medium transition-all ${
                        replyContent.trim()
                          ? 'bg-black/70 text-white hover:bg-black/80 dark:bg-white/70 dark:text-black dark:hover:bg-white/80'
                          : 'bg-muted text-muted-foreground cursor-not-allowed'
                      }`}
                    >
                      Responder
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Nested Replies with smooth animation */}
      <div
        className={`overflow-hidden transition-all duration-300 ease-in-out ${
          hasReplies && !isCollapsed ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
        }`}
      >
        <div className="mt-3">
          {comment.replies?.map((reply) => (
            <CommentItem
              key={reply.id}
              comment={reply}
              onVote={onVote}
              onReply={onReply}
              onDelete={onDelete}
              onEdit={onEdit}
              currentUserId={currentUserId}
              level={level + 1}
              maxLevel={maxLevel}
              parentIsDeleted={isDeletedOrHasDeletedParent}
            />
          ))}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              Excluir comentário com respostas
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p>Este comentário possui respostas. Ao excluir:</p>
              <ul className="list-inside list-disc space-y-1 text-sm">
                <li>
                  O conteúdo do comentário será mantido para preservar o contexto da discussão
                </li>
                <li>Sua identificação (nome e foto) será removida</li>
                <li>Não será mais possível votar ou responder a este comentário</li>
                <li>As respostas existentes permanecerão visíveis</li>
              </ul>
              <p className="mt-3 font-medium">Deseja continuar?</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                onDelete?.(comment.id);
                setShowDeleteDialog(false);
              }}
              className="bg-red-600 text-white hover:bg-red-700"
            >
              Excluir mesmo assim
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

function CommentInput({
  onSubmit,
  onCancel,
}: {
  onSubmit: (content: string) => void;
  onCancel?: () => void;
}) {
  const [content, setContent] = useState('');

  const handleSubmit = () => {
    if (content.trim()) {
      onSubmit(content.trim());
      setContent('');
    }
  };

  const handleCancel = () => {
    setContent('');
    onCancel?.();
  };

  return (
    <div className="bg-background rounded-lg">
      <RichTextEditor
        content={content}
        onChange={setContent}
        placeholder="Escreva um comentário..."
        onSubmit={handleSubmit}
      />
      <div className="flex items-center justify-between p-3 pt-2">
        <span className="text-muted-foreground text-xs">Ctrl+Enter para enviar</span>
        <div className="flex items-center gap-3">
          {onCancel && (
            <button
              onClick={handleCancel}
              className="text-muted-foreground hover:text-foreground px-4 py-2 text-sm font-medium transition-all"
            >
              Cancelar
            </button>
          )}
          <button
            onClick={handleSubmit}
            disabled={!content.trim()}
            className={`flex items-center gap-2 rounded-lg px-4 py-2 text-sm font-medium transition-all ${
              content.trim()
                ? 'bg-black/70 text-white hover:bg-black/80 dark:bg-white/70 dark:text-black dark:hover:bg-white/80'
                : 'bg-muted text-muted-foreground cursor-not-allowed'
            }`}
          >
            <Send className="h-4 w-4" />
            Enviar
          </button>
        </div>
      </div>
    </div>
  );
}

export function CommentSection({
  comments,
  onVote,
  onReply,
  onNewComment,
  onDelete,
  onEdit,
  currentUserId,
  layout = 'top',
  className,
}: CommentSectionProps) {
  const [showCommentInput, setShowCommentInput] = useState(false);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'score'>('newest');
  const isIntegrated = className?.includes('border-0');

  // Sort comments based on selected option
  const sortedComments = useMemo(() => {
    const sortComments = (commentList: Comment[]): Comment[] => {
      const sorted = [...commentList].sort((a, b) => {
        switch (sortBy) {
          case 'newest':
            return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
          case 'oldest':
            return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
          case 'score':
            return b.votes - a.votes;
          default:
            return 0;
        }
      });

      // Sort replies recursively
      return sorted.map((comment) => ({
        ...comment,
        replies: comment.replies ? sortComments(comment.replies) : [],
      }));
    };

    return sortComments(comments);
  }, [comments, sortBy]);

  const content = (
    <div className="space-y-3 p-4">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <h3 className="text-foreground text-lg font-semibold">Discussão</h3>
          <span className="text-muted-foreground text-sm">
            {comments.length} {comments.length === 1 ? 'comentário' : 'comentários'}
          </span>
        </div>

        {comments.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2">
                <ArrowUpDown className="h-4 w-4" />
                <span className="hidden sm:inline">
                  {sortBy === 'newest' && 'Mais recentes'}
                  {sortBy === 'oldest' && 'Mais antigos'}
                  {sortBy === 'score' && 'Pontuação'}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setSortBy('newest')}>
                <Calendar className="mr-2 h-4 w-4" />
                Mais recentes primeiro
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('oldest')}>
                <Calendar className="mr-2 h-4 w-4" />
                Mais antigos primeiro
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('score')}>
                <TrendingUp className="mr-2 h-4 w-4" />
                Maior pontuação primeiro
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {comments.length === 0 ? (
        <div className="text-muted-foreground py-8 text-center">Seja o primeiro a comentar!</div>
      ) : (
        <div className="mb-4 space-y-4">
          {sortedComments.map((comment) => (
            <CommentItem
              key={comment.id}
              comment={comment}
              onVote={onVote}
              onReply={onReply}
              onDelete={onDelete}
              onEdit={onEdit}
              currentUserId={currentUserId}
            />
          ))}
        </div>
      )}

      {/* Comment Input Box or Button */}
      {onNewComment && (
        <div className="mt-4">
          {showCommentInput ? (
            <CommentInput
              onSubmit={(content) => {
                onNewComment(content);
                setShowCommentInput(false);
              }}
              onCancel={() => setShowCommentInput(false)}
            />
          ) : (
            <div className="flex justify-end">
              <button
                onClick={() => setShowCommentInput(true)}
                className="flex items-center gap-2 rounded-lg bg-black/70 px-4 py-2 font-medium text-white transition-all hover:bg-black/80 dark:bg-white/70 dark:text-black dark:hover:bg-white/80"
              >
                <MessageSquare className="h-4 w-4" />
                Comentar
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );

  // If integrated, return content without Card wrapper
  if (isIntegrated) {
    return <div className={className}>{content}</div>;
  }

  // Otherwise, wrap in Card
  const containerClass = cn('comment-section', layout === 'side' && 'h-full', className);

  if (layout === 'side') {
    return (
      <Card className={containerClass}>
        <ScrollArea className="h-full">{content}</ScrollArea>
      </Card>
    );
  }

  return <Card className={containerClass}>{content}</Card>;
}
