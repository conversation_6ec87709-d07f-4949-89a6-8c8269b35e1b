/* Custom Markdown Styling for Chat Component */

.markdown-chat-content {
  font-size: 15px;
  line-height: 1.8;
  color: hsl(var(--foreground));
}

/* Paragraphs - better spacing */
.markdown-chat-content p {
  margin-bottom: 1rem;
}

.markdown-chat-content p:last-child {
  margin-bottom: 0;
}

.markdown-chat-content p:last-of-type {
  margin-bottom: 0;
}

/* Headings - cleaner hierarchy */
.markdown-chat-content h1,
.markdown-chat-content h2,
.markdown-chat-content h3,
.markdown-chat-content h4,
.markdown-chat-content h5,
.markdown-chat-content h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: hsl(var(--foreground));
}

.markdown-chat-content h1:first-child,
.markdown-chat-content h2:first-child,
.markdown-chat-content h3:first-child,
.markdown-chat-content h4:first-child,
.markdown-chat-content h5:first-child,
.markdown-chat-content h6:first-child {
  margin-top: 0;
}

.markdown-chat-content h1 {
  font-size: 1.5rem;
}
.markdown-chat-content h2 {
  font-size: 1.25rem;
}
.markdown-chat-content h3 {
  font-size: 1.125rem;
}
.markdown-chat-content h4 {
  font-size: 1rem;
}
.markdown-chat-content h5 {
  font-size: 0.875rem;
}
.markdown-chat-content h6 {
  font-size: 0.8125rem;
}

/* Links - subtle styling */
.markdown-chat-content a {
  color: hsl(var(--primary));
  text-decoration: none;
  border-bottom: 1px solid hsl(var(--primary) / 0.3);
  transition: border-color 0.2s ease;
}

.markdown-chat-content a:hover {
  border-bottom-color: hsl(var(--primary));
}

/* Strong and emphasis */
.markdown-chat-content strong,
.markdown-chat-content b {
  font-weight: 600;
  color: hsl(var(--foreground));
}

.markdown-chat-content em,
.markdown-chat-content i {
  font-style: italic;
}

/* Lists - cleaner spacing */
.markdown-chat-content ul,
.markdown-chat-content ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.markdown-chat-content li {
  margin-bottom: 0.4rem;
  line-height: 1.7;
}

.markdown-chat-content li:last-child {
  margin-bottom: 0;
}

.markdown-chat-content li > ul,
.markdown-chat-content li > ol {
  margin: 0.4rem 0;
}

/* Blockquotes - modern style */
.markdown-chat-content blockquote {
  border-left: 3px solid hsl(var(--primary) / 0.3);
  padding-left: 1rem;
  margin: 1.25rem 0;
  color: hsl(var(--muted-foreground));
  font-style: normal;
}

.markdown-chat-content blockquote p {
  margin-bottom: 0.5rem;
}

.markdown-chat-content blockquote p:last-child {
  margin-bottom: 0;
}

/* Tables - clean design */
.markdown-chat-content table {
  width: 100%;
  margin: 1.5rem 0;
  font-size: 0.875rem;
  border-collapse: collapse;
}

.markdown-chat-content th,
.markdown-chat-content td {
  text-align: left;
  padding: 0.75rem;
  border: 1px solid hsl(var(--border));
}

.markdown-chat-content th {
  background-color: hsl(var(--muted) / 0.5);
  font-weight: 600;
}

.markdown-chat-content tr:nth-child(even) td {
  background-color: hsl(var(--muted) / 0.2);
}

/* Horizontal rules - styles are handled by the component */

/* Images */
.markdown-chat-content img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
}

/* Code - inline */
.markdown-chat-content :not(pre) > code {
  background-color: hsl(var(--muted));
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: var(--font-mono);
  color: hsl(var(--foreground));
}

/* Remove extra margins */
.markdown-chat-content > *:first-child {
  margin-top: 0;
}

.markdown-chat-content > *:last-child {
  margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .markdown-chat-content {
    font-size: 14px;
  }

  .markdown-chat-content h1 {
    font-size: 1.25rem;
  }
  .markdown-chat-content h2 {
    font-size: 1.125rem;
  }
  .markdown-chat-content h3 {
    font-size: 1rem;
  }
}

/* Dark mode optimizations */
.dark .markdown-chat-content {
  color: hsl(var(--foreground));
}

.dark .markdown-chat-content strong,
.dark .markdown-chat-content b {
  color: hsl(var(--foreground));
}

.dark .markdown-chat-content blockquote {
  border-left-color: hsl(var(--primary) / 0.4);
  color: hsl(var(--muted-foreground));
}

.dark .markdown-chat-content :not(pre) > code {
  background-color: hsl(var(--muted) / 0.3);
}

.dark .markdown-chat-content th {
  background-color: hsl(var(--muted) / 0.3);
}

.dark .markdown-chat-content tr:nth-child(even) td {
  background-color: hsl(var(--muted) / 0.1);
}

/* Chat-specific compact mode */
.markdown-chat-content.text-sm {
  font-size: 14px;
}

.markdown-chat-content.text-sm p {
  margin-bottom: 0.75rem;
}

.markdown-chat-content.text-sm ul,
.markdown-chat-content.text-sm ol {
  margin: 0.75rem 0;
}

.markdown-chat-content.text-sm li {
  margin-bottom: 0.25rem;
}

/* Override prose classes for chat */
.markdown-chat-content.prose-p\:my-2 p {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.markdown-chat-content.prose-pre\:my-2 pre {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.markdown-chat-content.prose-ul\:my-2 ul,
.markdown-chat-content.prose-ol\:my-2 ol {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.markdown-chat-content.prose-headings\:mt-3 h1,
.markdown-chat-content.prose-headings\:mt-3 h2,
.markdown-chat-content.prose-headings\:mt-3 h3,
.markdown-chat-content.prose-headings\:mt-3 h4,
.markdown-chat-content.prose-headings\:mt-3 h5,
.markdown-chat-content.prose-headings\:mt-3 h6 {
  margin-top: 0.75rem;
}

.markdown-chat-content.prose-headings\:mb-2 h1,
.markdown-chat-content.prose-headings\:mb-2 h2,
.markdown-chat-content.prose-headings\:mb-2 h3,
.markdown-chat-content.prose-headings\:mb-2 h4,
.markdown-chat-content.prose-headings\:mb-2 h5,
.markdown-chat-content.prose-headings\:mb-2 h6 {
  margin-bottom: 0.5rem;
}
