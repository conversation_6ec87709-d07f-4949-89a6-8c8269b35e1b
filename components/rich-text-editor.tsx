'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Link from '@tiptap/extension-link';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import { Table } from '@tiptap/extension-table';
import { TableRow } from '@tiptap/extension-table-row';
import { TableCell } from '@tiptap/extension-table-cell';
import { TableHeader } from '@tiptap/extension-table-header';
import TextAlign from '@tiptap/extension-text-align';
import Highlight from '@tiptap/extension-highlight';
import Underline from '@tiptap/extension-underline';
import { createLowlight, common } from 'lowlight';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Code,
  List,
  ListOrdered,
  Quote,
  Link2,
  Table as TableIcon,
  Highlighter,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Undo,
  Redo,
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Create lowlight instance with common languages
// The 'common' export includes popular languages like JavaScript, TypeScript, CSS, etc.
const lowlight = createLowlight(common);

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  editable?: boolean;
  onSubmit?: () => void;
}

export function RichTextEditor({
  content,
  onChange,
  placeholder = 'Escreva um comentário...',
  className,
  editable = true,
  onSubmit,
}: RichTextEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // We'll use CodeBlockLowlight instead
      }),
      Placeholder.configure({
        placeholder,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary underline',
        },
      }),
      CodeBlockLowlight.configure({
        lowlight,
        HTMLAttributes: {
          class: 'bg-muted rounded-md p-3 my-2 overflow-x-auto font-mono text-sm',
        },
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'border-collapse table-auto w-full my-2',
        },
      }),
      TableRow,
      TableCell.configure({
        HTMLAttributes: {
          class: 'border border-border p-2',
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class: 'border border-border p-2 bg-muted font-semibold',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 dark:bg-yellow-900',
        },
      }),
      Underline,
    ],
    content,
    editable,
    immediatelyRender: false, // Prevent SSR hydration mismatch
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm dark:prose-invert max-w-none focus:outline-none min-h-[100px] p-3',
          'prose-p:my-1 prose-headings:mt-2 prose-headings:mb-1',
          'prose-ul:my-1 prose-ol:my-1 prose-li:my-0',
          'prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:bg-muted',
          'prose-pre:my-2 prose-blockquote:my-2',
          className
        ),
      },
      handleKeyDown: (view, event) => {
        if (event.key === 'Enter' && event.ctrlKey && onSubmit) {
          event.preventDefault();
          onSubmit();
          return true;
        }
        return false;
      },
    },
  });

  if (!editor) {
    return null;
  }

  const addLink = () => {
    const url = window.prompt('URL:');
    if (url) {
      editor.chain().focus().setLink({ href: url }).run();
    }
  };

  const insertTable = () => {
    editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
  };

  return (
    <div className="border-border bg-background rounded-lg border shadow-sm dark:shadow-[0_1px_3px_0_rgba(255,255,255,0.05),0_1px_2px_0_rgba(255,255,255,0.03)]">
      {editable && (
        <div className="border-border flex flex-wrap items-center gap-1 border-b p-2">
          {/* Text formatting */}
          <button
            onClick={() => editor.chain().focus().toggleBold().run()}
            disabled={!editor.can().chain().focus().toggleBold().run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive('bold') && 'bg-accent'
            )}
            title="Negrito (Ctrl+B)"
          >
            <Bold className="h-4 w-4" />
          </button>
          <button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            disabled={!editor.can().chain().focus().toggleItalic().run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive('italic') && 'bg-accent'
            )}
            title="Itálico (Ctrl+I)"
          >
            <Italic className="h-4 w-4" />
          </button>
          <button
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            disabled={!editor.can().chain().focus().toggleUnderline().run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive('underline') && 'bg-accent'
            )}
            title="Sublinhado (Ctrl+U)"
          >
            <UnderlineIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => editor.chain().focus().toggleHighlight().run()}
            disabled={!editor.can().chain().focus().toggleHighlight().run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive('highlight') && 'bg-accent'
            )}
            title="Destacar"
          >
            <Highlighter className="h-4 w-4" />
          </button>
          <button
            onClick={() => editor.chain().focus().toggleCode().run()}
            disabled={!editor.can().chain().focus().toggleCode().run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive('code') && 'bg-accent'
            )}
            title="Código"
          >
            <Code className="h-4 w-4" />
          </button>

          <div className="bg-border mx-1 h-6 w-px" />

          {/* Lists */}
          <button
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive('bulletList') && 'bg-accent'
            )}
            title="Lista"
          >
            <List className="h-4 w-4" />
          </button>
          <button
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive('orderedList') && 'bg-accent'
            )}
            title="Lista numerada"
          >
            <ListOrdered className="h-4 w-4" />
          </button>

          <div className="bg-border mx-1 h-6 w-px" />

          {/* Block formatting */}
          <button
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive('blockquote') && 'bg-accent'
            )}
            title="Citação"
          >
            <Quote className="h-4 w-4" />
          </button>
          <button
            onClick={() => editor.chain().focus().toggleCodeBlock().run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive('codeBlock') && 'bg-accent'
            )}
            title="Bloco de código"
          >
            <Code className="h-4 w-4" />
          </button>

          <div className="bg-border mx-1 h-6 w-px" />

          {/* Alignment */}
          <button
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive({ textAlign: 'left' }) && 'bg-accent'
            )}
            title="Alinhar à esquerda"
          >
            <AlignLeft className="h-4 w-4" />
          </button>
          <button
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive({ textAlign: 'center' }) && 'bg-accent'
            )}
            title="Centralizar"
          >
            <AlignCenter className="h-4 w-4" />
          </button>
          <button
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            className={cn(
              'hover:bg-accent rounded p-1.5 transition-colors',
              editor.isActive({ textAlign: 'right' }) && 'bg-accent'
            )}
            title="Alinhar à direita"
          >
            <AlignRight className="h-4 w-4" />
          </button>

          <div className="bg-border mx-1 h-6 w-px" />

          {/* Insert */}
          <button
            onClick={addLink}
            className="hover:bg-accent rounded p-1.5 transition-colors"
            title="Inserir link"
          >
            <Link2 className="h-4 w-4" />
          </button>
          <button
            onClick={insertTable}
            className="hover:bg-accent rounded p-1.5 transition-colors"
            title="Inserir tabela"
          >
            <TableIcon className="h-4 w-4" />
          </button>

          <div className="bg-border mx-1 h-6 w-px" />

          {/* History */}
          <button
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().chain().focus().undo().run()}
            className="hover:bg-accent rounded p-1.5 transition-colors disabled:opacity-50"
            title="Desfazer"
          >
            <Undo className="h-4 w-4" />
          </button>
          <button
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().chain().focus().redo().run()}
            className="hover:bg-accent rounded p-1.5 transition-colors disabled:opacity-50"
            title="Refazer"
          >
            <Redo className="h-4 w-4" />
          </button>
        </div>
      )}
      <EditorContent editor={editor} />
    </div>
  );
}
