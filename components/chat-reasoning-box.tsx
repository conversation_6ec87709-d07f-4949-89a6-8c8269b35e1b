'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, ChevronRight, Brain } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ChatReasoningBoxProps {
  reasoning: string;
  className?: string;
  defaultOpen?: boolean;
}

export function ChatReasoningBox({
  reasoning,
  className,
  defaultOpen = false,
}: ChatReasoningBoxProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  if (!reasoning) return null;

  return (
    <div className={cn('bg-muted/50 mt-2 mb-3 rounded-lg border', className)}>
      <Button
        variant="ghost"
        size="sm"
        className="w-full justify-start px-2 py-1 text-xs font-medium"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Brain className="mr-1 h-3 w-3" />
        Reasoning
        {isOpen ? (
          <ChevronDown className="ml-auto h-3 w-3" />
        ) : (
          <ChevronRight className="ml-auto h-3 w-3" />
        )}
      </Button>
      {isOpen && (
        <div className="px-3 pb-2">
          <p className="text-muted-foreground text-xs whitespace-pre-wrap">{reasoning}</p>
        </div>
      )}
    </div>
  );
}
