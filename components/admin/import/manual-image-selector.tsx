'use client';

import { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { X, Check } from 'lucide-react';
import Image from 'next/image';
import type { ImportData } from '@/types/import';

interface ExtractedImage {
  success: boolean;
  page_number?: number;
  cloudinary_url?: string;
  method?: string;
  public_id?: string;
  local_path?: string;
  local_filename?: string;
  hash?: string;
}

interface ManualImageSelectorProps {
  extractedImages: ExtractedImage[];
  parsedData: ImportData;
  onUpdate: (updatedData: ImportData) => void;
}

interface ImageAssignment {
  imageIndex: number;
  questionNumber: number;
}

export function ManualImageSelector({
  extractedImages,
  parsedData,
  onUpdate,
}: ManualImageSelectorProps) {
  const [assignments, setAssignments] = useState<ImageAssignment[]>([]);
  const [selectedImage, setSelectedImage] = useState<number | null>(null);
  const [uploadingImages, setUploadingImages] = useState<Set<number>>(new Set());
  const [uploadedImages, setUploadedImages] = useState<
    Map<number, { cloudinary_url: string; public_id: string }>
  >(new Map());

  const validImages = extractedImages.filter(
    (img) => img.success && (img.cloudinary_url || img.local_path)
  );

  const uploadImageToCloudinary = useCallback(
    async (imageIndex: number): Promise<{ cloudinary_url: string; public_id: string } | null> => {
      const image = validImages[imageIndex];
      if (!image || !image.local_path || image.cloudinary_url) {
        return null; // Already uploaded or no local path
      }

      setUploadingImages((prev) => new Set(prev).add(imageIndex));

      try {
        const response = await fetch('/api/admin/upload-image', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            local_path: image.local_path,
            page_number: image.page_number,
            hash: image.hash,
            method: image.method,
          }),
        });

        if (!response.ok) {
          throw new Error('Upload failed');
        }

        const result = await response.json();
        const uploadData = {
          cloudinary_url: result.cloudinary_url,
          public_id: result.public_id,
        };

        setUploadedImages((prev) => new Map(prev).set(imageIndex, uploadData));
        return uploadData;
      } catch (error) {
        console.error('Failed to upload image:', error);
        return null;
      } finally {
        setUploadingImages((prev) => {
          const newSet = new Set(prev);
          newSet.delete(imageIndex);
          return newSet;
        });
      }
    },
    [validImages]
  );

  const assignImageToQuestion = useCallback(
    async (imageIndex: number, questionNumber: number) => {
      const image = validImages[imageIndex];

      // Upload to Cloudinary if it's a local image
      if (image.local_path && !image.cloudinary_url && !uploadedImages.has(imageIndex)) {
        const uploadResult = await uploadImageToCloudinary(imageIndex);
        if (!uploadResult) {
          // Upload failed, don't assign
          return;
        }
      }

      setAssignments((prev) => {
        // Remove any existing assignment for this image
        const filtered = prev.filter((a) => a.imageIndex !== imageIndex);
        // Add new assignment
        return [...filtered, { imageIndex, questionNumber }];
      });
    },
    [validImages, uploadedImages, uploadImageToCloudinary]
  );

  const removeAssignment = useCallback((imageIndex: number) => {
    setAssignments((prev) => prev.filter((a) => a.imageIndex !== imageIndex));
  }, []);

  const getAssignedQuestion = useCallback(
    (imageIndex: number) => {
      return assignments.find((a) => a.imageIndex === imageIndex)?.questionNumber;
    },
    [assignments]
  );

  const getImageDisplayUrl = useCallback(
    (imageIndex: number) => {
      const image = validImages[imageIndex];
      const uploadedData = uploadedImages.get(imageIndex);

      // Priority: uploaded URL > original cloudinary URL > local path as data URL
      if (uploadedData?.cloudinary_url) {
        return uploadedData.cloudinary_url;
      }
      if (image.cloudinary_url) {
        return image.cloudinary_url;
      }
      if (image.local_path) {
        // For local images, we'll need to serve them through a static endpoint
        // For now, show a placeholder
        return '/api/admin/local-image?path=' + encodeURIComponent(image.local_path);
      }
      return null;
    },
    [validImages, uploadedImages]
  );

  const applyAssignments = useCallback(() => {
    const updatedData = { ...parsedData };

    // Clear existing image assignments
    updatedData.questions = updatedData.questions.map((q) => ({
      ...q,
      image_url: undefined,
      image_public_id: undefined,
    }));

    // Apply new assignments
    assignments.forEach(({ imageIndex, questionNumber }) => {
      const image = validImages[imageIndex];
      const questionIndex = updatedData.questions.findIndex((q) => q.number === questionNumber);

      if (questionIndex !== -1 && image) {
        // Use uploaded URL if available, otherwise use original cloudinary_url
        const uploadedData = uploadedImages.get(imageIndex);
        const imageUrl = uploadedData?.cloudinary_url || image.cloudinary_url;
        const publicId = uploadedData?.public_id || image.public_id;

        if (imageUrl) {
          // Update the manual image assignment fields
          updatedData.questions[questionIndex] = {
            ...updatedData.questions[questionIndex],
            image_url: imageUrl,
            image_public_id: publicId,
          };

          // Also replace placeholder <img> tags in the question stem with actual Cloudinary URLs
          let updatedStem = updatedData.questions[questionIndex].stem;

          // For now, replace the first <img> tag found in the question stem
          // In the future, this could be enhanced to match based on alt text or other attributes
          updatedStem = updatedStem.replace(
            /<img([^>]*?)src="[^"]*"([^>]*?)>/,
            (match, beforeAttrs, afterAttrs) => {
              // Replace the src attribute with the actual Cloudinary URL while preserving other attributes
              return `<img${beforeAttrs}src="${imageUrl}"${afterAttrs}>`;
            }
          );

          updatedData.questions[questionIndex].stem = updatedStem;

          // Also check and update associated_text_id if it contains HTML content (incorrect structure)
          if (
            updatedData.questions[questionIndex].associated_text_id &&
            updatedData.questions[questionIndex].associated_text_id.includes('<img')
          ) {
            let updatedAssociatedTextId = updatedData.questions[questionIndex].associated_text_id;
            updatedAssociatedTextId = updatedAssociatedTextId.replace(
              /<img([^>]*?)src="[^"]*"([^>]*?)>/,
              (match, beforeAttrs, afterAttrs) => {
                return `<img${beforeAttrs}src="${imageUrl}"${afterAttrs}>`;
              }
            );
            updatedData.questions[questionIndex].associated_text_id = updatedAssociatedTextId;
          }

          // Also check and update associated_texts_catalog if they contain the same question reference
          if (
            updatedData.associated_texts_catalog &&
            updatedData.associated_texts_catalog.length > 0
          ) {
            updatedData.associated_texts_catalog = updatedData.associated_texts_catalog.map(
              (textBlock) => {
                // Replace <img> tags in associated text content as well
                const updatedContent = textBlock.content.replace(
                  /<img([^>]*?)src="[^"]*"([^>]*?)>/,
                  (match, beforeAttrs, afterAttrs) => {
                    return `<img${beforeAttrs}src="${imageUrl}"${afterAttrs}>`;
                  }
                );

                return {
                  ...textBlock,
                  content: updatedContent,
                };
              }
            );
          }
        }
      }
    });

    onUpdate(updatedData);
  }, [assignments, validImages, parsedData, onUpdate, uploadedImages]);

  if (validImages.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Manual Image Assignment</CardTitle>
        <p className="text-muted-foreground text-sm">
          Assign extracted images to specific questions. Click on an image to select it, then choose
          which question it belongs to.
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Image Grid */}
        <div className="space-y-4">
          <Label>Extracted Images ({validImages.length} total)</Label>
          <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
            {validImages.map((image, index) => {
              const assignedQuestion = getAssignedQuestion(index);
              const isSelected = selectedImage === index;
              const isUploading = uploadingImages.has(index);
              const displayUrl = getImageDisplayUrl(index);

              return (
                <div
                  key={index}
                  className={`relative cursor-pointer rounded-lg border-2 p-2 transition-all ${
                    isSelected
                      ? 'border-blue-500 ring-2 ring-blue-200'
                      : assignedQuestion
                        ? 'border-green-500'
                        : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedImage(isSelected ? null : index)}
                >
                  <div className="relative">
                    <Image
                      src={displayUrl || '/placeholder-image.png'}
                      alt={`Extracted image ${index + 1}`}
                      width={300}
                      height={128}
                      className="h-32 w-full rounded bg-gray-50 object-contain"
                    />

                    {/* Upload overlay */}
                    {isUploading && (
                      <div className="absolute inset-0 flex items-center justify-center rounded bg-black/50">
                        <div className="text-xs text-white">Uploading...</div>
                      </div>
                    )}
                  </div>

                  {/* Image info overlay */}
                  <div className="absolute top-1 left-1 rounded bg-black/70 px-1 py-0.5 text-xs text-white">
                    Page {image.page_number}
                  </div>

                  <div className="absolute top-1 right-1 rounded bg-black/70 px-1 py-0.5 text-xs text-white">
                    {image.method}
                  </div>

                  {/* Assignment status */}
                  {assignedQuestion && (
                    <div className="absolute right-1 bottom-1 left-1">
                      <Badge variant="secondary" className="w-full justify-center text-xs">
                        Q{assignedQuestion}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-1 h-4 w-4 p-0 hover:bg-red-500 hover:text-white"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeAssignment(index);
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Assignment Controls */}
        {selectedImage !== null && (
          <div className="space-y-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
            <div className="flex items-center gap-4">
              <div className="flex-shrink-0">
                <Image
                  src={getImageDisplayUrl(selectedImage) || '/placeholder-image.png'}
                  alt={`Selected image ${selectedImage + 1}`}
                  width={80}
                  height={80}
                  className="h-20 w-20 rounded border bg-white object-contain"
                />
              </div>
              <div className="flex-1 space-y-2">
                <Label>Assign to Question:</Label>
                <Select
                  value={getAssignedQuestion(selectedImage)?.toString() || ''}
                  onValueChange={async (value) => {
                    if (value) {
                      await assignImageToQuestion(selectedImage, parseInt(value));
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a question..." />
                  </SelectTrigger>
                  <SelectContent>
                    {parsedData.questions.map((question) => (
                      <SelectItem key={question.number} value={question.number.toString()}>
                        Question {question.number} - {question.subject}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}

        {/* Assignment Summary */}
        {assignments.length > 0 && (
          <div className="space-y-4">
            <Label>Assignment Summary ({assignments.length} assignments)</Label>
            <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
              {assignments.map(({ imageIndex, questionNumber }) => {
                const image = validImages[imageIndex];
                return (
                  <div
                    key={imageIndex}
                    className="flex items-center gap-2 rounded border border-green-200 bg-green-50 p-2"
                  >
                    <Image
                      src={getImageDisplayUrl(imageIndex) || '/placeholder-image.png'}
                      alt={`Assigned image`}
                      width={32}
                      height={32}
                      className="h-8 w-8 rounded bg-white object-contain"
                    />
                    <span className="flex-1 text-sm">
                      Page {image.page_number} → Question {questionNumber}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 hover:bg-red-500 hover:text-white"
                      onClick={() => removeAssignment(imageIndex)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Apply Button */}
        <div className="flex justify-end">
          <Button
            onClick={applyAssignments}
            disabled={assignments.length === 0}
            className="flex items-center gap-2"
          >
            <Check className="h-4 w-4" />
            Apply Assignments ({assignments.length})
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
