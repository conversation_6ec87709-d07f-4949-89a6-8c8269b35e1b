'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { InfoIcon, CheckCircle2, Search, ArrowRight } from 'lucide-react';
import type { ImportData, MappedData } from '@/types/import';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';

interface ExamMatcherProps {
  importData: ImportData;
  onExamMatched: (examId: string, testId: string, mappedData: MappedData) => void;
  onBack: () => void;
}

interface ExistingExam {
  id: string;
  testId: string;
  institutionId: string;
  examBoardId: string;
  positionId: string;
  specializationId?: string;
  institution: string;
  examBoard: string;
  position: string;
  specialization?: string;
  year: number;
  testType: string;
  booklet?: string;
  applicationDate?: string;
  questionCount: number;
}

export function ExamMatcher({ importData, onExamMatched, onBack }: ExamMatcherProps) {
  const [loading, setLoading] = useState(true);
  const [existingExams, setExistingExams] = useState<ExistingExam[]>([]);
  const [selectedExamId, setSelectedExamId] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    searchExistingExams('', false);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const searchExistingExams = async (customSearch?: string, showAll: boolean = false) => {
    setLoading(true);
    setError(null);

    try {
      let params;

      if (customSearch) {
        // Use custom search term
        params = new URLSearchParams({
          search: customSearch,
        });
      } else if (showAll) {
        // Show all exams without filtering
        params = new URLSearchParams();
      } else {
        // Search for exams matching the metadata but without year for looser matching
        params = new URLSearchParams({
          institution: importData.metadata.institution,
          examBoard: importData.metadata.exam_board,
          position: importData.metadata.position,
        });
      }

      const response = await fetch(`/api/admin/import/match-exams?${params}`);
      if (!response.ok) {
        throw new Error('Failed to search for existing exams');
      }

      const data = await response.json();
      setExistingExams(data.exams || []);

      // Auto-select if only one match
      if (data.exams && data.exams.length === 1) {
        setSelectedExamId(data.exams[0].testId);
      }
    } catch (err) {
      console.error('Error searching exams:', err);
      setError('Failed to search for existing exams. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleContinue = () => {
    const selectedExam = existingExams.find((exam) => exam.testId === selectedExamId);
    if (!selectedExam) return;

    // Create mapped data from the selected exam with IDs from the database
    const mappedData: MappedData = {
      institutionId: selectedExam.institutionId,
      institutionIsNew: false,
      examBoardId: selectedExam.examBoardId,
      examBoardIsNew: false,
      positionId: selectedExam.positionId,
      positionIsNew: false,
      specializationId: selectedExam.specializationId || '',
      specializationIsNew: false,
      year: selectedExam.year,
      testType: selectedExam.testType,
      booklet: selectedExam.booklet || '',
      applicationDate: selectedExam.applicationDate || '',
      subjectMappings: {},
      topicMappings: {},
      matchedExamId: selectedExam.id,
      matchedTestId: selectedExam.testId,
    };

    onExamMatched(selectedExam.id, selectedExam.testId, mappedData);
  };

  return (
    <div className="space-y-6">
      {importData.questions.length > 0 &&
      importData.questions.every((q) => q.is_null || q.correct_answer_order === null) ? (
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            This file contains only question keys. Please select an existing exam to match these
            answers with.
          </AlertDescription>
        </Alert>
      ) : (
        <Alert>
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            Select an existing exam to add these questions to. The system will check for duplicate
            questions before importing.
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Select Existing Exam</CardTitle>
          <CardDescription>
            {importData.questions.length > 0 &&
            importData.questions.every((q) => q.is_null || q.correct_answer_order === null)
              ? 'Select the exam that these answer keys belong to'
              : 'Select the exam to add these questions to'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-4">
              <div className="bg-muted/50 rounded-lg border p-4">
                <h4 className="mb-2 font-medium">Looking for exams matching:</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span className="text-muted-foreground">Institution:</span>{' '}
                    <span className="font-medium">{importData.metadata.institution}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Exam Board:</span>{' '}
                    <span className="font-medium">{importData.metadata.exam_board}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Position:</span>{' '}
                    <span className="font-medium">{importData.metadata.position}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Year:</span>{' '}
                    <span className="font-medium">{importData.metadata.year}</span>
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                  <Input
                    type="text"
                    placeholder="Search for any exam..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        searchExistingExams(searchQuery, false);
                      }
                    }}
                    className="pl-9"
                  />
                </div>
                <Button
                  variant="outline"
                  onClick={() => searchExistingExams(searchQuery, false)}
                  disabled={loading}
                >
                  Search
                </Button>
                <Button
                  variant="outline"
                  onClick={() => searchExistingExams('', true)}
                  disabled={loading}
                >
                  Show All
                </Button>
              </div>
            </div>

            {loading ? (
              <div className="space-y-2">
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
            ) : error ? (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : existingExams.length === 0 ? (
              <div className="py-8 text-center">
                <Search className="text-muted-foreground mx-auto mb-3 h-12 w-12" />
                <p className="text-muted-foreground">
                  {searchQuery
                    ? 'No exams found for that search. Try a different search term or show all exams.'
                    : 'No matching exams found. Try searching or showing all exams.'}
                </p>
              </div>
            ) : (
              <RadioGroup value={selectedExamId} onValueChange={setSelectedExamId}>
                <div className="space-y-2">
                  {existingExams.map((exam) => (
                    <Label
                      key={exam.testId}
                      htmlFor={exam.testId}
                      className={`hover:bg-accent flex cursor-pointer items-start space-x-3 rounded-lg border p-4 ${
                        selectedExamId === exam.testId ? 'border-primary bg-accent' : ''
                      }`}
                    >
                      <RadioGroupItem value={exam.testId} id={exam.testId} />
                      <div className="flex-1 space-y-1">
                        <div className="flex items-center gap-2">
                          <p className="font-medium">
                            {exam.position}
                            {exam.specialization && ` - ${exam.specialization}`}
                          </p>
                          {exam.booklet && (
                            <Badge variant="secondary" className="text-xs">
                              {exam.booklet}
                            </Badge>
                          )}
                        </div>
                        <div className="text-muted-foreground flex items-center gap-4 text-sm">
                          <span>{exam.institution}</span>
                          <span>•</span>
                          <span>{exam.examBoard}</span>
                          <span>•</span>
                          <span>{exam.year}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <CheckCircle2 className="h-4 w-4 text-green-600" />
                          <span>{exam.questionCount} questions</span>
                          {exam.applicationDate && (
                            <>
                              <span>•</span>
                              <span>
                                Applied on {new Date(exam.applicationDate).toLocaleDateString()}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </Label>
                  ))}
                </div>
              </RadioGroup>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button onClick={handleContinue} disabled={!selectedExamId || loading}>
          Continue
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
