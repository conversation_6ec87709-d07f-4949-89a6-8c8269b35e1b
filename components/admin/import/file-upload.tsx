'use client';

import { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Upload,
  X,
  CheckCircle,
  Loader2,
  FileText,
  InfoIcon,
  Settings,
  Image as ImageIcon,
  FolderOpen,
  Eye,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { ImportData, ImportQuestion, AssociatedTextBlock } from '@/types/import';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useOcrTemplates } from '@/hooks/use-ocr-templates';
import { parseMarkdownJSON } from '@/lib/utils/json-parser';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { getModelByName, getModelThinkingBudgetRange } from '@/lib/gemini/models';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { ManualImageSelector } from './manual-image-selector';

interface FileUploadProps {
  onFileUploaded: (data: ImportData) => void;
  onPdfProcess?: (
    file: File,
    promptType: string,
    templateId?: string,
    modelName?: string,
    modelParams?: {
      temperature?: number;
      topP?: number;
      topK?: number;
      thinkingBudget?: number;
      customPrompt?: string;
    },
    keysFile?: File,
    imageExtractionOptions?: {
      extractImages?: boolean;
      imageFolder?: string;
      includeVisualDetection?: boolean;
    }
  ) => Promise<void>;
}

// Function to automatically replace <img> tags with Cloudinary URLs based on page numbers
function autoReplaceImageTags(
  data: ImportData,
  extractedImages: Array<{
    success: boolean;
    page_number?: number;
    cloudinary_url?: string;
    method?: string;
  }>
): ImportData {
  // Create a mapping of page numbers to Cloudinary URLs
  const pageToUrlMap = new Map<number, string>();
  extractedImages.forEach((img) => {
    if (img.success && img.page_number && img.cloudinary_url) {
      pageToUrlMap.set(img.page_number, img.cloudinary_url);
    }
  });

  console.log('Page to URL mapping:', Object.fromEntries(pageToUrlMap));

  // Create a copy of the data to avoid mutations
  const updatedData = { ...data };

  // Process questions and their options
  updatedData.questions = data.questions.map((question) => {
    const updatedQuestion = { ...question };

    // Replace <img> tags in question stem
    updatedQuestion.stem = replaceImgTagsInText(updatedQuestion.stem, pageToUrlMap);

    // Replace <img> tags in question options
    if (updatedQuestion.options) {
      updatedQuestion.options = updatedQuestion.options.map((option) => ({
        ...option,
        text: replaceImgTagsInText(option.text, pageToUrlMap),
      }));
    }

    return updatedQuestion;
  });

  // Also process associated text blocks
  if (updatedData.associated_texts_catalog) {
    updatedData.associated_texts_catalog = updatedData.associated_texts_catalog.map(
      (textBlock) => ({
        ...textBlock,
        content: replaceImgTagsInText(textBlock.content, pageToUrlMap),
      })
    );
  }

  return updatedData;
}

// Helper function to replace <img> tags in a text string
function replaceImgTagsInText(text: string, pageToUrlMap: Map<number, string>): string {
  if (!text) return text;

  return text.replace(/<img([^>]*?)>/g, (match, attributes) => {
    // Extract data-page attribute
    const pageMatch = attributes.match(/data-page="?(\d+)"?/);
    if (pageMatch) {
      const pageNumber = parseInt(pageMatch[1]);
      const cloudinaryUrl = pageToUrlMap.get(pageNumber);

      if (cloudinaryUrl) {
        console.log(`Replacing <img> tag for page ${pageNumber} with ${cloudinaryUrl}`);
        // Replace the src attribute with the Cloudinary URL
        let updatedAttributes = attributes;
        if (updatedAttributes.includes('src=')) {
          updatedAttributes = updatedAttributes.replace(/src="[^"]*"/g, `src="${cloudinaryUrl}"`);
        } else {
          updatedAttributes = ` src="${cloudinaryUrl}"${updatedAttributes}`;
        }
        return `<img${updatedAttributes}>`;
      }
    }

    // Return original tag if no replacement found
    return match;
  });
}

// Deep extraction function to handle various object structures
function deepExtractText(obj: unknown, depth: number = 0): string {
  // Prevent infinite recursion
  if (depth > 10) {
    console.error('Max depth reached in deepExtractText');
    return JSON.stringify(obj);
  }

  // If it's already a string, return it
  if (typeof obj === 'string') return obj;

  // If it's null or undefined, return empty string
  if (obj === null || obj === undefined) return '';

  // If it's a number or boolean, convert to string
  if (typeof obj === 'number' || typeof obj === 'boolean') return String(obj);

  // If it's an array, try to extract text from elements
  if (Array.isArray(obj)) {
    // If it's an array of strings, join them
    if (obj.every((item) => typeof item === 'string')) {
      return obj.join(' ');
    }

    // Otherwise, try to extract from each element
    for (const item of obj) {
      const extracted = deepExtractText(item, depth + 1);
      if (extracted && extracted !== '{}' && extracted !== '[]') {
        return extracted;
      }
    }
    return JSON.stringify(obj); // Fallback for arrays
  }

  // If it's an object, try various extraction strategies
  if (typeof obj === 'object' && obj !== null) {
    // Log the object structure for debugging (only at top level)
    if (depth === 0) {
      console.warn('Extracting text from object:', JSON.stringify(obj, null, 2));
    }

    // Check if it's a React element or similar (has $$typeof)
    if ('$$typeof' in obj && obj.$$typeof) {
      console.warn('Detected React element, extracting props');
      if ('props' in obj && obj.props && typeof obj.props === 'object' && 'children' in obj.props) {
        return deepExtractText(obj.props.children, depth + 1);
      }
    }

    // Common property names that might contain text (ordered by priority)
    const textProperties = [
      'text',
      'content',
      'value',
      'html',
      'body',
      'message',
      'description',
      'stem',
      'question',
      'answer',
      'option',
      'children',
      'label',
      'title',
      'name',
      'display',
      'innerHTML',
      'textContent',
      'innerText',
      '_text',
      '__text',
      'Text',
      'Content',
    ];

    // Try direct properties first
    const objRecord = obj as Record<string, unknown>;
    for (const prop of textProperties) {
      if (prop in objRecord && objRecord[prop] !== undefined && objRecord[prop] !== null) {
        const extracted = deepExtractText(objRecord[prop], depth + 1);
        if (extracted && extracted !== '{}' && extracted !== '[]') {
          return extracted;
        }
      }
    }

    // Try nested common patterns
    const nestedPatterns = [
      ['data', 'text'],
      ['data', 'content'],
      ['attributes', 'text'],
      ['props', 'children'],
      ['fields', 'text'],
      ['value', 'text'],
    ];

    for (const pattern of nestedPatterns) {
      let current: unknown = obj;
      let valid = true;
      for (const key of pattern) {
        if (current && typeof current === 'object' && current !== null && key in current) {
          current = (current as Record<string, unknown>)[key];
        } else {
          valid = false;
          break;
        }
      }
      if (valid && current) {
        const extracted = deepExtractText(current, depth + 1);
        if (extracted && extracted !== '{}' && extracted !== '[]') {
          return extracted;
        }
      }
    }

    // Check all properties recursively (but limit to avoid circular references)
    const keys = Object.keys(obj);
    for (const key of keys) {
      // Skip special properties that might cause issues
      if (key.startsWith('_') || key.startsWith('$') || key === 'constructor') {
        continue;
      }

      const extracted = deepExtractText(objRecord[key], depth + 1);
      if (
        extracted &&
        typeof extracted === 'string' &&
        extracted !== '{}' &&
        extracted !== '[]' &&
        extracted.length > 0
      ) {
        return extracted;
      }
    }

    // If no text found, return a formatted JSON string
    return JSON.stringify(obj, null, 2);
  }

  // Fallback: convert to string
  return String(obj);
}

export function FileUpload({ onFileUploaded, onPdfProcess }: FileUploadProps) {
  const { loading: templatesLoading, templates, getTemplateByType } = useOcrTemplates();
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [validationResult, setValidationResult] = useState<{
    valid: boolean;
    totalQuestions: number;
    metadata: ImportData['metadata'];
    cancelledQuestionsCount: number;
  } | null>(null);
  const [validating, setValidating] = useState(false);
  const [mode, setMode] = useState<'json' | 'pdf'>('json');
  const [jsonPdfMode, setJsonPdfMode] = useState<'paste' | 'paste-with-pdf'>('paste');

  // PDF processing states
  const [pdfFiles, setPdfFiles] = useState<{ exam?: File; keys?: File }>({});
  const [jsonPasteFile, setJsonPasteFile] = useState<File | null>(null);
  const [pendingJsonData, setPendingJsonData] = useState<ImportData | null>(null);
  const [extractingText, setExtractingText] = useState(false);
  const [ocrResult, setOcrResult] = useState<{
    parsedData: ImportData | null;
    rawText: string;
    status: 'parsed' | 'unparsed';
    extractedImages?: Array<{
      success: boolean;
      page_number?: number;
      cloudinary_url?: string;
      method?: string;
    }>;
    imgTagCount?: number;
  } | null>(null);
  const [updatedParsedData, setUpdatedParsedData] = useState<ImportData | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string | undefined>(undefined);
  const [selectedModel, setSelectedModel] = useState<string>('gemini-2.5-flash');
  const [promptType, setPromptType] = useState<'questions_and_keys' | 'keys_only'>(
    'questions_and_keys'
  );
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Model parameters
  const [temperature, setTemperature] = useState(0.7);
  const [topP, setTopP] = useState(0.95);
  const [topK, setTopK] = useState(40);
  const [thinkingBudget, setThinkingBudget] = useState(32768);
  const [customPrompt, setCustomPrompt] = useState('');

  // Image extraction parameters
  const [extractImages, setExtractImages] = useState(true);
  const [imageFolder, setImageFolder] = useState('memo-extracted-images');
  const [includeVisualDetection, setIncludeVisualDetection] = useState(false);

  // Load template when type changes
  useEffect(() => {
    if (!templatesLoading) {
      const defaultTemplate = getTemplateByType(promptType);
      setSelectedTemplate(defaultTemplate?.id);
    }
  }, [promptType, templatesLoading, getTemplateByType]);

  // Update thinking budget range when model changes
  useEffect(() => {
    const budgetRange = getModelThinkingBudgetRange(selectedModel);
    if (budgetRange) {
      // If current thinking budget exceeds the model's max, adjust it
      if (thinkingBudget > budgetRange[1]) {
        setThinkingBudget(budgetRange[1]);
      }
    }
  }, [selectedModel, thinkingBudget]);

  const parseFile = useCallback(
    async (input: string | File) => {
      setError(null);
      setValidating(true);
      setValidationResult(null);

      try {
        const text = typeof input === 'string' ? input : await input.text();

        // Use parseMarkdownJSON to handle markdown code blocks from Google AI Studio
        const data = parseMarkdownJSON<ImportData>(text);
        console.log('Parsed JSON data:', JSON.stringify(data, null, 2));

        // Parse the new format
        let importData: ImportData;

        if (
          data.metadata &&
          Array.isArray(data.questions) &&
          Array.isArray(data.associated_texts_catalog)
        ) {
          // Standard format with questions and associated texts catalog
          importData = data as ImportData;

          // Fix any questions that have objects instead of strings for stem/text
          importData.questions = importData.questions.map((question) => {
            // Use deep extraction for stem
            if (typeof question.stem !== 'string') {
              question.stem = deepExtractText(question.stem);
            }

            // Fix options
            if (question.options && Array.isArray(question.options)) {
              question.options = question.options.map((option) => {
                if (typeof option.text !== 'string') {
                  option.text = deepExtractText(option.text);
                }
                return option;
              });
            }

            // Fix change_reason if it's not a string
            if (typeof question.change_reason !== 'string') {
              question.change_reason = deepExtractText(question.change_reason);
            }

            // Note: images field is deprecated - now uses <img> tag detection

            // Ensure correct_answer_order is a number or null
            if (question.correct_answer_order === undefined) {
              question.correct_answer_order = null;
            }

            // Ensure is_null is a boolean
            if (question.is_null === undefined) {
              question.is_null = false;
            }

            // Ensure change_reason is a string
            if (!question.change_reason) {
              question.change_reason = '';
            }

            // Preserve associated_text_references if present
            // This field contains text snippets and their line references
            if (question.associated_text_references) {
              // Ensure it's properly structured
              if (Array.isArray(question.associated_text_references)) {
                question.associated_text_references = question.associated_text_references.filter(
                  (ref) =>
                    ref &&
                    typeof ref.snippet === 'string' &&
                    typeof ref.location_pointer === 'string'
                );
              } else {
                question.associated_text_references = null;
              }
            }

            return question;
          });

          // Also fix associated texts if they have objects
          if (
            importData.associated_texts_catalog &&
            Array.isArray(importData.associated_texts_catalog)
          ) {
            importData.associated_texts_catalog = importData.associated_texts_catalog.map(
              (textBlock) => {
                if (typeof textBlock.content !== 'string') {
                  textBlock.content = deepExtractText(textBlock.content);
                }
                return textBlock;
              }
            );
          }
        } else {
          throw new Error(
            'Invalid JSON structure. Expected metadata, associated_texts_catalog, and questions.'
          );
        }

        // Check required metadata fields
        const requiredMetadata = ['institution', 'exam_board', 'position', 'year', 'test_type'];
        const missingFields = requiredMetadata.filter(
          (field) => !(importData.metadata as any)[field] // eslint-disable-line @typescript-eslint/no-explicit-any
        );

        if (missingFields.length > 0) {
          throw new Error(
            `Missing required metadata fields: ${missingFields.join(', ')}. Please ensure your JSON includes all required fields: institution, exam_board, position, year, test_type`
          );
        }

        // Validate associated texts catalog
        const textIds = new Set<string>();
        importData.associated_texts_catalog.forEach((block: AssociatedTextBlock) => {
          if (!block.id || !block.content) {
            throw new Error('Each text block must have an id and content');
          }
          if (textIds.has(block.id)) {
            throw new Error(`Duplicate text block ID: ${block.id}`);
          }
          textIds.add(block.id);
        });

        // Validate questions array
        if (!importData.questions || importData.questions.length === 0) {
          throw new Error('JSON must contain questions.');
        }

        // Validate each question
        importData.questions.forEach((question: ImportQuestion, index: number) => {
          if (!question.stem) {
            throw new Error(`Question ${index + 1} is missing stem`);
          }
          if (!question.options || question.options.length < 2) {
            throw new Error(`Question ${index + 1} must have at least 2 options`);
          }
          // Validate associated_text_id reference
          if (question.associated_text_id) {
            // Check if it's a valid reference to the catalog
            if (!textIds.has(question.associated_text_id)) {
              // Check if it contains HTML (backward compatibility)
              if (
                !question.associated_text_id.includes('<') ||
                !question.associated_text_id.includes('>')
              ) {
                throw new Error(
                  `Question ${index + 1} references unknown text block: ${question.associated_text_id}`
                );
              }
              // If it contains HTML, log a warning but allow it
              console.warn(
                `Question ${index + 1}: associated_text_id contains HTML instead of ID reference`
              );
            }
          }
        });

        // Count cancelled questions
        const cancelledCount = importData.questions.filter((q: ImportQuestion) => q.is_null).length;

        setValidationResult({
          valid: true,
          totalQuestions: importData.questions.length,
          metadata: importData.metadata,
          cancelledQuestionsCount: cancelledCount,
        });

        // Validate using backend
        const response = await fetch('/api/admin/import/validate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(importData),
        });

        let validationData;
        try {
          validationData = await response.json();
        } catch {
          // If JSON parsing fails, treat it as a validation error
          throw new Error(`Backend validation failed: Response status ${response.status}`);
        }

        if (!response.ok || validationData.error) {
          // Handle different response structures
          let errorMessage = 'Backend validation failed';

          if (validationData.error) {
            errorMessage = validationData.error;

            // Try to extract more specific error information
            if (validationData.details?.questions) {
              // Find the first question with an error
              const questionKeys = Object.keys(validationData.details.questions);
              for (const key of questionKeys) {
                const questionErrors = validationData.details.questions[key];
                if (questionErrors && typeof questionErrors === 'object') {
                  const errorKeys = Object.keys(questionErrors);
                  for (const errorKey of errorKeys) {
                    const specificError = questionErrors[errorKey];
                    if (
                      specificError &&
                      specificError._errors &&
                      specificError._errors.length > 0
                    ) {
                      errorMessage = `Question ${key} ${errorKey}: ${specificError._errors[0].message || specificError._errors[0]}`;
                      break;
                    }
                  }
                }
                if (errorMessage !== validationData.error) break;
              }
            }
          } else if (validationData.errors && validationData.errors.length > 0) {
            // Handle the expected format
            errorMessage = validationData.errors[0].message || 'Backend validation failed';
          }

          throw new Error(errorMessage);
        }

        if (typeof input === 'string') {
          setFile(null);
        }

        // In JSON + PDF mode, don't immediately call onFileUploaded
        // Wait for PDF processing to complete first
        if (jsonPdfMode === 'paste-with-pdf') {
          // Store the parsed data for later use with PDF processing
          setPendingJsonData(importData);
        } else {
          onFileUploaded(importData);
        }
      } catch (err) {
        console.error('Parsing error:', err);
        let errorMessage = (err as Error).message;

        // Provide better error messages for common issues
        if (errorMessage.includes('Unexpected token')) {
          errorMessage =
            'Invalid JSON format. Make sure to paste valid JSON, optionally wrapped in markdown code blocks (```json ... ```)';
        } else if (errorMessage.includes('JSON.parse')) {
          errorMessage =
            'Failed to parse JSON. Please check that your pasted content is valid JSON from Google AI Studio';
        }

        setError(errorMessage);
        setValidationResult(null);
      } finally {
        setValidating(false);
      }
    },
    [onFileUploaded, jsonPdfMode]
  );

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles[0]) {
        const file = acceptedFiles[0];
        setFile(file);
        parseFile(file);
      }
    },
    [parseFile]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { 'application/json': ['.json'] },
    maxFiles: 1,
  });

  const clearFile = () => {
    setFile(null);
    setError(null);
    setValidationResult(null);
  };

  // PDF handling
  const handlePdfDrop = useCallback((acceptedFiles: File[], type: 'exam' | 'keys') => {
    if (acceptedFiles[0]) {
      setPdfFiles((prev) => ({ ...prev, [type]: acceptedFiles[0] }));
    }
  }, []);

  const { getRootProps: getExamRootProps, getInputProps: getExamInputProps } = useDropzone({
    onDrop: (files) => handlePdfDrop(files, 'exam'),
    accept: { 'application/pdf': ['.pdf'] },
    maxFiles: 1,
    disabled: mode !== 'pdf',
  });

  const { getRootProps: getKeysRootProps, getInputProps: getKeysInputProps } = useDropzone({
    onDrop: (files) => handlePdfDrop(files, 'keys'),
    accept: { 'application/pdf': ['.pdf'] },
    maxFiles: 1,
    disabled: mode !== 'pdf',
  });

  const clearPdfFile = (type: 'exam' | 'keys') => {
    setPdfFiles((prev) => ({ ...prev, [type]: undefined }));
    if (type === 'exam') {
      setOcrResult(null);
      setUpdatedParsedData(null);
      setError(null);
    }
  };

  const handleExtractText = async () => {
    if (!pdfFiles.exam || !onPdfProcess) return;

    setExtractingText(true);
    setError(null);
    setOcrResult(null);
    setUpdatedParsedData(null);

    try {
      await onPdfProcess(
        pdfFiles.exam,
        promptType,
        selectedTemplate,
        selectedModel,
        {
          temperature,
          topP,
          topK,
          thinkingBudget,
          customPrompt: customPrompt || undefined,
        },
        pdfFiles.keys,
        {
          extractImages,
          imageFolder,
          includeVisualDetection,
        }
      );
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setExtractingText(false);
    }
  };

  // Handle OCR results passed from parent
  const handleOcrResult = useCallback(
    (result: {
      parsedData: ImportData | null;
      rawText: string;
      extractedImages?: Array<{
        success: boolean;
        page_number?: number;
        cloudinary_url?: string;
        method?: string;
      }>;
      imgTagCount?: number;
    }) => {
      // Check if we have pending JSON data from "JSON + PDF" mode
      if (pendingJsonData && result.extractedImages) {
        console.log('Processing JSON + PDF mode, extracted images:', result.extractedImages);
        console.log('Sample extracted image:', result.extractedImages[0]);

        // Automatically replace <img> tags with Cloudinary URLs based on page numbers
        const updatedJsonData = autoReplaceImageTags(pendingJsonData, result.extractedImages);

        // Combine the pending JSON data with the extracted images
        setOcrResult({
          parsedData: updatedJsonData,
          rawText: 'JSON + PDF processing completed',
          status: 'parsed',
          extractedImages: result.extractedImages,
          imgTagCount: result.imgTagCount,
        });
        setUpdatedParsedData(updatedJsonData);

        // Auto-fill validation result
        const cancelledCount = updatedJsonData.questions.filter(
          (q: ImportQuestion) => q.is_null
        ).length;
        setValidationResult({
          valid: true,
          totalQuestions: updatedJsonData.questions.length,
          metadata: updatedJsonData.metadata,
          cancelledQuestionsCount: cancelledCount,
        });

        // Clear pending data and proceed with the import
        setPendingJsonData(null);

        return;
      }

      if (result.parsedData) {
        // Fix any questions that have objects instead of strings for stem/text
        const fixedData = { ...result.parsedData };

        // Log the data structure for debugging
        console.log('OCR Result Data Structure:', JSON.stringify(result.parsedData, null, 2));

        fixedData.questions = fixedData.questions.map((question, index) => {
          // Log problematic questions
          if (
            typeof question.stem !== 'string' ||
            (question.options && question.options.some((opt) => typeof opt.text !== 'string'))
          ) {
            console.warn(
              `Question ${index + 1} has object fields:`,
              JSON.stringify(question, null, 2)
            );
          }

          // Use deep extraction for stem
          if (typeof question.stem !== 'string') {
            question.stem = deepExtractText(question.stem);
          }

          // Fix options
          if (question.options && Array.isArray(question.options)) {
            question.options = question.options.map((option) => {
              if (typeof option.text !== 'string') {
                option.text = deepExtractText(option.text);
              }
              return option;
            });
          }

          // Fix change_reason if it's not a string
          if (typeof question.change_reason !== 'string') {
            question.change_reason = deepExtractText(question.change_reason);
          }

          // Note: images field is deprecated - now uses <img> tag detection

          // Ensure correct_answer_order is a number or null
          if (question.correct_answer_order === undefined) {
            question.correct_answer_order = null;
          }

          // Ensure is_null is a boolean
          if (question.is_null === undefined) {
            question.is_null = false;
          }

          // Ensure change_reason is a string
          if (!question.change_reason) {
            question.change_reason = '';
          }

          return question;
        });

        // Also fix associated texts if they have objects
        if (
          fixedData.associated_texts_catalog &&
          Array.isArray(fixedData.associated_texts_catalog)
        ) {
          fixedData.associated_texts_catalog = fixedData.associated_texts_catalog.map(
            (textBlock) => {
              if (typeof textBlock.content !== 'string') {
                console.warn(
                  'Associated text has object content:',
                  JSON.stringify(textBlock, null, 2)
                );
                textBlock.content = deepExtractText(textBlock.content);
              }
              return textBlock;
            }
          );
        }

        setOcrResult({
          parsedData: fixedData,
          rawText: result.rawText,
          status: 'parsed',
          extractedImages: result.extractedImages,
          imgTagCount: result.imgTagCount,
        });
        setUpdatedParsedData(fixedData);
        // Auto-fill validation result
        const cancelledCount = fixedData.questions.filter((q: ImportQuestion) => q.is_null).length;

        setValidationResult({
          valid: true,
          totalQuestions: fixedData.questions.length,
          metadata: fixedData.metadata,
          cancelledQuestionsCount: cancelledCount,
        });
      } else {
        setOcrResult({
          parsedData: null,
          rawText: result.rawText,
          status: 'unparsed',
          extractedImages: result.extractedImages,
          imgTagCount: result.imgTagCount,
        });
        setUpdatedParsedData(null);
      }
    },
    [pendingJsonData]
  );

  // Expose handleOcrResult to parent if needed
  useEffect(() => {
    if (
      onPdfProcess &&
      (window as unknown as { handleOcrResult?: typeof handleOcrResult }).handleOcrResult !==
        handleOcrResult
    ) {
      (window as unknown as { handleOcrResult: typeof handleOcrResult }).handleOcrResult =
        handleOcrResult;
    }
  }, [handleOcrResult, onPdfProcess]);

  // Add beforeunload event listener when extracting text
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (extractingText) {
        e.preventDefault();
        e.returnValue =
          'A Gemini processing request is in progress. Are you sure you want to leave?';
        return e.returnValue;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [extractingText]);

  return (
    <div className="space-y-4">
      <Tabs value={mode} onValueChange={(v) => setMode(v as 'json' | 'pdf')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="json">Enviar JSON</TabsTrigger>
          <TabsTrigger value="pdf">Processar PDF</TabsTrigger>
        </TabsList>

        <TabsContent value="json" className="space-y-4">
          {/* JSON Mode Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Modo de Importação JSON</CardTitle>
              <CardDescription>Escolha como você deseja importar seus dados JSON</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Método de Importação</Label>
                <Select
                  value={jsonPdfMode}
                  onValueChange={(v) => setJsonPdfMode(v as 'paste' | 'paste-with-pdf')}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="paste">Envio de Arquivo JSON ou Colar</SelectItem>
                    <SelectItem value="paste-with-pdf">Colar JSON + PDF para Imagens</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-muted-foreground text-xs">
                  {jsonPdfMode === 'paste-with-pdf'
                    ? 'Cole JSON do Google AI Studio e faça upload de PDF para processamento automático de imagens'
                    : 'Faça upload de arquivo JSON ou cole JSON diretamente'}
                </p>
              </div>
            </CardContent>
          </Card>

          {jsonPdfMode === 'paste' && (
            <>
              <Card>
                <CardContent className="p-6">
                  <div
                    {...getRootProps()}
                    className={cn(
                      'cursor-pointer rounded-lg border-2 border-dashed p-8 text-center transition-colors',
                      isDragActive ? 'border-primary bg-primary/5' : 'border-border',
                      file && !error && validationResult?.valid && 'border-green-500 bg-green-50',
                      error && 'border-red-500 bg-red-50'
                    )}
                  >
                    <input {...getInputProps()} />
                    <div className="flex flex-col items-center gap-2">
                      {!file && (
                        <>
                          <Upload className="text-muted-foreground h-10 w-10" />
                          <p className="text-muted-foreground text-sm">
                            {isDragActive
                              ? 'Solte o arquivo JSON aqui'
                              : 'Arraste e solte um arquivo JSON aqui, ou clique para selecionar'}
                          </p>
                        </>
                      )}
                      {file && !error && validationResult?.valid && (
                        <>
                          <CheckCircle className="h-10 w-10 text-green-600" />
                          <p className="text-sm font-medium">{file.name}</p>
                          <p className="text-muted-foreground text-xs">
                            {validationResult.totalQuestions} questions •{' '}
                            {validationResult.cancelledQuestionsCount} cancelled
                          </p>
                        </>
                      )}
                      {error && (
                        <>
                          <X className="h-10 w-10 text-red-600" />
                          <p className="text-sm text-red-600">{error}</p>
                        </>
                      )}
                    </div>
                  </div>

                  {file && (
                    <div className="mt-4 flex justify-center">
                      <Button variant="outline" size="sm" onClick={clearFile}>
                        Limpar Arquivo
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <h3 className="mb-4 font-semibold">Ou cole JSON diretamente:</h3>
                  <Textarea
                    placeholder='Cole seu JSON aqui...

Supports:
• Raw JSON: {"metadata": {...}, "questions": [...]}
• Markdown wrapped: ```json {"metadata": {...}} ```'
                    className="min-h-[200px] font-mono text-sm"
                    onChange={(e) => {
                      if (e.target.value) {
                        parseFile(e.target.value);
                      }
                    }}
                  />
                </CardContent>
              </Card>
            </>
          )}

          {jsonPdfMode === 'paste-with-pdf' && (
            <>
              {/* JSON Paste Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Cole JSON do Google AI Studio</CardTitle>
                  <CardDescription>
                    Cole a saída JSON do Google AI Studio contendo suas questões
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    placeholder='Cole seu JSON do Google AI Studio aqui...

Supports:
• Raw JSON: {"metadata": {...}, "questions": [...]}
• Markdown wrapped: ```json {"metadata": {...}} ```'
                    className="min-h-[200px] font-mono text-sm"
                    onChange={(e) => {
                      if (e.target.value) {
                        parseFile(e.target.value);
                      }
                    }}
                  />
                </CardContent>
              </Card>

              {/* PDF Upload Section for Images */}
              <Card>
                <CardHeader>
                  <CardTitle>Enviar PDF para Processamento de Imagens</CardTitle>
                  <CardDescription>
                    Envie o PDF original para extrair e processar imagens automaticamente
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div
                    className={cn(
                      'cursor-pointer rounded-lg border-2 border-dashed p-6 text-center transition-colors',
                      'hover:border-primary hover:bg-primary/5',
                      jsonPasteFile && 'border-primary bg-primary/5'
                    )}
                    onClick={() => {
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.accept = '.pdf';
                      input.onchange = (e) => {
                        const file = (e.target as HTMLInputElement).files?.[0];
                        if (file) {
                          setJsonPasteFile(file);
                        }
                      };
                      input.click();
                    }}
                  >
                    {!jsonPasteFile ? (
                      <>
                        <FileText className="text-muted-foreground mx-auto mb-2 h-10 w-10" />
                        <p className="text-muted-foreground text-sm">
                          Clique para selecionar PDF para extração de imagens
                        </p>
                      </>
                    ) : (
                      <div className="space-y-2">
                        <FileText className="text-primary mx-auto h-10 w-10" />
                        <p className="text-sm font-medium">{jsonPasteFile.name}</p>
                        <p className="text-muted-foreground text-xs">
                          {(jsonPasteFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setJsonPasteFile(null);
                          }}
                        >
                          Removerr
                        </Button>
                      </div>
                    )}
                  </div>

                  {/* Process Button for JSON + PDF */}
                  {jsonPasteFile && validationResult?.valid && (
                    <div className="mt-4 space-y-4">
                      {/* Image extraction options */}
                      <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/20">
                        <CardHeader className="pb-3">
                          <CardTitle className="flex items-center gap-2 text-base">
                            <ImageIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                            Opções de Extração de Imagens
                          </CardTitle>
                          <CardDescription className="text-sm">
                            Configure como as imagens são extraídas e processadas do seu PDF
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="flex items-center space-x-3">
                            <Checkbox
                              id="extractImagesJsonPdf"
                              checked={extractImages}
                              onCheckedChange={(checked) => setExtractImages(checked as boolean)}
                            />
                            <Label htmlFor="extractImagesJsonPdf" className="font-medium">
                              Extrair imagens do PDF
                            </Label>
                          </div>

                          {extractImages && (
                            <div className="ml-7 space-y-4 border-l-2 border-blue-200 pl-4 dark:border-blue-800">
                              <div className="space-y-2">
                                <Label className="flex items-center gap-2 text-sm font-medium">
                                  <FolderOpen className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                                  Pasta do Cloudinary
                                </Label>
                                <Input
                                  value={imageFolder}
                                  onChange={(e) => setImageFolder(e.target.value)}
                                  placeholder="memo-extracted-images"
                                  className="w-full"
                                />
                                <p className="text-muted-foreground text-xs">
                                  Nome da pasta para organizar imagens extraídas no Cloudinary
                                </p>
                              </div>

                              <div className="flex items-start space-x-3">
                                <Checkbox
                                  id="includeVisualDetectionJsonPdf"
                                  checked={includeVisualDetection}
                                  onCheckedChange={(checked) =>
                                    setIncludeVisualDetection(checked as boolean)
                                  }
                                  className="mt-0.5"
                                />
                                <div className="space-y-1">
                                  <Label
                                    htmlFor="includeVisualDetectionJsonPdf"
                                    className="flex items-center gap-2 text-sm font-medium"
                                  >
                                    <Eye className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                                    Incluir detecção visual
                                  </Label>
                                  <p className="text-muted-foreground text-xs">
                                    Usa visão computacional para detectar regiões de imagem
                                    adicionais além de imagens incorporadas
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                        </CardContent>
                      </Card>

                      <Button
                        onClick={async () => {
                          if (!jsonPasteFile || !onPdfProcess) return;
                          console.log('Starting PDF processing with:', {
                            file: jsonPasteFile.name,
                            extractImages,
                            imageFolder,
                            includeVisualDetection,
                          });
                          try {
                            await onPdfProcess(
                              jsonPasteFile,
                              'json_with_images', // Special mode for JSON + PDF processing
                              undefined, // No template needed
                              'gemini-2.5-flash', // Default model
                              undefined, // No additional model params needed
                              undefined, // No keys file
                              {
                                extractImages,
                                imageFolder,
                                includeVisualDetection,
                              }
                            );
                            console.log('PDF processing completed successfully');
                          } catch (error) {
                            console.error('PDF processing error:', error);
                            setError((error as Error).message);
                          }
                        }}
                        disabled={extractingText}
                        className="w-full"
                      >
                        {extractingText ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Processando Imagens...
                          </>
                        ) : (
                          'Processar Imagens do PDF'
                        )}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          )}

          {validating && (
            <Alert>
              <Loader2 className="h-4 w-4 animate-spin" />
              <AlertDescription>Validando estrutura JSON...</AlertDescription>
            </Alert>
          )}

          {validationResult?.valid && (
            <Alert className="border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                <div className="mb-2 font-medium">JSON validado com sucesso!</div>
                <div className="space-y-1 text-sm">
                  <p>Instituição: {validationResult.metadata.institution}</p>
                  <p>Banca Examinadora: {validationResult.metadata.exam_board}</p>
                  <p>Cargo: {validationResult.metadata.position}</p>
                  <p>Ano: {validationResult.metadata.year}</p>
                  <p>Total de Questões: {validationResult.totalQuestions}</p>
                  {validationResult.cancelledQuestionsCount > 0 && (
                    <p>Questões Canceladas: {validationResult.cancelledQuestionsCount}</p>
                  )}
                  {jsonPdfMode === 'paste-with-pdf' ? (
                    <p className="mt-2 font-medium text-blue-800">
                      💡 Now upload a PDF and click &quot;Process Images from PDF&quot; to extract
                      and assign images
                    </p>
                  ) : (
                    <p className="mt-2 text-xs text-gray-600">
                      ✓ Parsed successfully (supports both raw JSON and markdown code blocks)
                    </p>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Manual Image Assignment for JSON + PDF Mode */}
          {jsonPdfMode === 'paste-with-pdf' &&
            ocrResult &&
            ocrResult.extractedImages &&
            ocrResult.extractedImages.length > 0 &&
            ocrResult.parsedData && (
              <Card>
                <CardHeader>
                  <CardTitle>Atribuição Manual de Imagens</CardTitle>
                  <CardDescription>
                    {ocrResult.extractedImages.length} images extracted from PDF. Assign them to
                    questions manually.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ManualImageSelector
                    extractedImages={ocrResult.extractedImages}
                    parsedData={updatedParsedData || ocrResult.parsedData}
                    onUpdate={setUpdatedParsedData}
                  />
                </CardContent>
              </Card>
            )}

          {/* Use This Data Button for JSON + PDF Mode */}
          {jsonPdfMode === 'paste-with-pdf' &&
            ocrResult &&
            ocrResult.extractedImages &&
            ocrResult.extractedImages.length > 0 &&
            ocrResult.parsedData && (
              <div className="flex justify-center">
                <Button
                  onClick={() => {
                    const dataToUpload = updatedParsedData || ocrResult.parsedData;
                    if (dataToUpload) {
                      onFileUploaded(dataToUpload);
                    }
                  }}
                  className="w-full"
                  size="lg"
                >
                  Usar Estes Dados com Imagens
                </Button>
              </div>
            )}
        </TabsContent>

        <TabsContent value="pdf" className="space-y-4">
          <Alert>
            <InfoIcon className="h-4 w-4" />
            <AlertDescription>
              Faça upload de arquivos PDF para extrair questões e gabaritos usando IA. A IA
              analisará os PDFs e gerará o formato JSON necessário com textos associados indexados.
            </AlertDescription>
          </Alert>

          {/* PDF Type Selection */}
          <Card>
            <CardHeader>
              <CardTitle>Selecionar Tipo de PDF</CardTitle>
              <CardDescription>
                Escolha se você está fazendo upload de uma prova completa com respostas ou apenas
                gabaritos
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Tipo de PDF</Label>
                <Select
                  value={promptType}
                  onValueChange={(v) => setPromptType(v as 'questions_and_keys' | 'keys_only')}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="questions_and_keys">
                      Prova com Questões e Gabaritos
                    </SelectItem>
                    <SelectItem value="keys_only">Apenas Gabaritos</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Model Selection */}
              <div className="space-y-2">
                <Label>Modelo de IA</Label>
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gemini-2.5-flash">Gemini 2.5 Flash (Recomendado)</SelectItem>
                    <SelectItem value="gemini-2.5-flash-lite">
                      Gemini 2.5 Flash Lite (Mais Rápido)
                    </SelectItem>
                    <SelectItem value="gemini-2.5-pro">Gemini 2.5 Pro (Maior Qualidade)</SelectItem>
                    <SelectItem value="gemini-2.0-flash-001">Gemini 2.0 Flash (Legado)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-muted-foreground text-xs">
                  {getModelByName(selectedModel)?.description}
                </p>
              </div>

              {/* Template Selection */}
              {!templatesLoading && templates.length > 0 && (
                <div className="space-y-2">
                  <Label>Template OCR</Label>
                  <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um template" />
                    </SelectTrigger>
                    <SelectContent>
                      {templates
                        .filter((t) => {
                          // Map our prompt types to template types
                          if (promptType === 'questions_and_keys') {
                            return t.promptType === 'prova' || t.promptType === 'prova_gabarito';
                          } else if (promptType === 'keys_only') {
                            return t.promptType === 'gabarito';
                          }
                          return false;
                        })
                        .map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Custom Prompt */}
              <div className="space-y-2">
                <Label>Instruções Personalizadas (Opcional)</Label>
                <Textarea
                  placeholder="Adicione instruções específicas para a IA seguir ao processar o PDF..."
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  className="min-h-[100px]"
                />
                <p className="text-muted-foreground text-xs">
                  Essas instruções serão enviadas junto com o PDF para orientar o processo de
                  extração da IA.
                </p>
              </div>

              {/* Advanced Settings */}
              <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
                <CollapsibleTrigger asChild>
                  <Button variant="outline" size="sm" className="w-full">
                    <Settings className="mr-2 h-4 w-4" />
                    {showAdvanced ? 'Ocultar' : 'Mostrar'} Configurações Avançadas
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-4 space-y-4">
                  {/* Temperature */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Temperature: {temperature.toFixed(2)}</Label>
                      <Input
                        type="number"
                        value={temperature}
                        onChange={(e) => setTemperature(parseFloat(e.target.value))}
                        min={0}
                        max={2}
                        step={0.1}
                        className="w-20"
                      />
                    </div>
                    <Slider
                      value={[temperature]}
                      onValueChange={(value) => setTemperature(value[0])}
                      min={0}
                      max={2}
                      step={0.1}
                      className="w-full"
                    />
                    <p className="text-muted-foreground text-xs">
                      Controla aleatoriedade: 0 = determinístico, 2 = muito aleatório
                    </p>
                  </div>

                  {/* Top P */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Top P: {topP.toFixed(2)}</Label>
                      <Input
                        type="number"
                        value={topP}
                        onChange={(e) => setTopP(parseFloat(e.target.value))}
                        min={0}
                        max={1}
                        step={0.05}
                        className="w-20"
                      />
                    </div>
                    <Slider
                      value={[topP]}
                      onValueChange={(value) => setTopP(value[0])}
                      min={0}
                      max={1}
                      step={0.05}
                      className="w-full"
                    />
                    <p className="text-muted-foreground text-xs">
                      Amostragem de núcleo: considera tokens com massa de probabilidade top_p
                    </p>
                  </div>

                  {/* Top K */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Top K: {topK}</Label>
                      <Input
                        type="number"
                        value={topK}
                        onChange={(e) => setTopK(parseInt(e.target.value))}
                        min={1}
                        max={100}
                        step={1}
                        className="w-20"
                      />
                    </div>
                    <Slider
                      value={[topK]}
                      onValueChange={(value) => setTopK(value[0])}
                      min={1}
                      max={100}
                      step={1}
                      className="w-full"
                    />
                    <p className="text-muted-foreground text-xs">
                      Considerar apenas os top K tokens
                    </p>
                  </div>

                  {/* Thinking Budget */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Thinking Budget: {thinkingBudget.toLocaleString()}</Label>
                      <Input
                        type="number"
                        value={thinkingBudget}
                        onChange={(e) => setThinkingBudget(parseInt(e.target.value))}
                        min={0}
                        max={getModelThinkingBudgetRange(selectedModel)?.[1] || 65536}
                        step={1024}
                        className="w-24"
                      />
                    </div>
                    <Slider
                      value={[thinkingBudget]}
                      onValueChange={(value) => setThinkingBudget(value[0])}
                      min={0}
                      max={getModelThinkingBudgetRange(selectedModel)?.[1] || 65536}
                      step={1024}
                      className="w-full"
                    />
                    <p className="text-muted-foreground text-xs">
                      {getModelThinkingBudgetRange(selectedModel)
                        ? `Tokens de raciocínio para ${selectedModel}. Valores maiores permitem raciocínio mais complexo (0 = desabilitado, max: ${getModelThinkingBudgetRange(selectedModel)?.[1].toLocaleString()})`
                        : 'Este modelo não suporta tokens de raciocínio'}
                    </p>
                  </div>

                  {/* Image Extraction */}
                  <div className="space-y-4 border-t pt-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <ImageIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        <Label className="font-medium">Extração de Imagens</Label>
                      </div>

                      <div className="space-y-4 rounded-lg border border-blue-200 bg-blue-50/30 p-4 dark:border-blue-800 dark:bg-blue-950/10">
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            id="extractImages"
                            checked={extractImages}
                            onCheckedChange={(checked) => setExtractImages(checked as boolean)}
                          />
                          <Label htmlFor="extractImages" className="font-medium">
                            Extract images from PDF
                          </Label>
                        </div>
                        <p className="text-muted-foreground text-xs">
                          Quando habilitado, o sistema extrairá automaticamente imagens onde tags
                          &lt;img&gt; aparecem na saída do OCR
                        </p>

                        {extractImages && (
                          <div className="ml-7 space-y-4 border-l-2 border-blue-200 pl-4 dark:border-blue-800">
                            <div className="space-y-2">
                              <Label className="flex items-center gap-2 text-sm font-medium">
                                <FolderOpen className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                                Cloudinary Folder
                              </Label>
                              <Input
                                value={imageFolder}
                                onChange={(e) => setImageFolder(e.target.value)}
                                placeholder="memo-extracted-images"
                                className="w-full"
                              />
                              <p className="text-muted-foreground text-xs">
                                Folder name for organizing extracted images in Cloudinary
                              </p>
                            </div>

                            <div className="flex items-start space-x-3">
                              <Checkbox
                                id="includeVisualDetection"
                                checked={includeVisualDetection}
                                onCheckedChange={(checked) =>
                                  setIncludeVisualDetection(checked as boolean)
                                }
                                className="mt-0.5"
                              />
                              <div className="space-y-1">
                                <Label
                                  htmlFor="includeVisualDetection"
                                  className="flex items-center gap-2 text-sm font-medium"
                                >
                                  <Eye className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                                  Incluir detecção visual (gráficos, diagramas)
                                </Label>
                                <p className="text-muted-foreground text-xs">
                                  Usa visão computacional para detectar regiões de imagem adicionais
                                  além de imagens incorporadas
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </CardContent>
          </Card>

          {/* PDF Upload Areas */}
          <div className="grid gap-4 md:grid-cols-2">
            {/* Exam PDF */}
            <Card>
              <CardHeader>
                <CardTitle>
                  {promptType === 'questions_and_keys' ? 'PDF da Prova' : 'PDF do Gabarito'}
                </CardTitle>
                <CardDescription>
                  {promptType === 'questions_and_keys'
                    ? 'Faça upload da prova contendo as questões'
                    : 'Faça upload do PDF contendo o gabarito'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  {...getExamRootProps()}
                  className={cn(
                    'cursor-pointer rounded-lg border-2 border-dashed p-6 text-center transition-colors',
                    'hover:border-primary hover:bg-primary/5',
                    pdfFiles.exam && 'border-primary bg-primary/5'
                  )}
                >
                  <input {...getExamInputProps()} />
                  {!pdfFiles.exam ? (
                    <>
                      <FileText className="text-muted-foreground mx-auto mb-2 h-10 w-10" />
                      <p className="text-muted-foreground text-sm">
                        Solte o PDF aqui ou clique para selecionar
                      </p>
                    </>
                  ) : (
                    <div className="space-y-2">
                      <FileText className="text-primary mx-auto h-10 w-10" />
                      <p className="text-sm font-medium">{pdfFiles.exam.name}</p>
                      <p className="text-muted-foreground text-xs">
                        {(pdfFiles.exam.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          clearPdfFile('exam');
                        }}
                      >
                        Remover
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Keys PDF */}
            <Card>
              <CardHeader>
                <CardTitle>PDF do Gabarito (Opcional)</CardTitle>
                <CardDescription>
                  Se as respostas estão em um PDF separado, faça upload aqui
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  {...getKeysRootProps()}
                  className={cn(
                    'cursor-pointer rounded-lg border-2 border-dashed p-6 text-center transition-colors',
                    'hover:border-primary hover:bg-primary/5',
                    pdfFiles.keys && 'border-primary bg-primary/5'
                  )}
                >
                  <input {...getKeysInputProps()} />
                  {!pdfFiles.keys ? (
                    <>
                      <FileText className="text-muted-foreground mx-auto mb-2 h-10 w-10" />
                      <p className="text-muted-foreground text-sm">Solte o PDF do gabarito aqui</p>
                    </>
                  ) : (
                    <div className="space-y-2">
                      <FileText className="text-primary mx-auto h-10 w-10" />
                      <p className="text-sm font-medium">{pdfFiles.keys.name}</p>
                      <p className="text-muted-foreground text-xs">
                        {(pdfFiles.keys.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          clearPdfFile('keys');
                        }}
                      >
                        Remover
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Process Button */}
          {pdfFiles.exam && (
            <div className="flex justify-center">
              <Button
                onClick={handleExtractText}
                disabled={extractingText || !onPdfProcess}
                size="lg"
              >
                {extractingText ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processando PDF{pdfFiles.keys ? 's' : ''}...
                  </>
                ) : (
                  `Extrair Questões do PDF${pdfFiles.keys ? 's' : ''}`
                )}
              </Button>
            </div>
          )}

          {/* OCR Results */}
          {ocrResult && (
            <Card>
              <CardHeader>
                <CardTitle>Resultados do OCR</CardTitle>
                <CardDescription>
                  {ocrResult.status === 'parsed'
                    ? 'Dados JSON extraídos e analisados com sucesso'
                    : 'Texto extraído mas não foi possível analisar como JSON'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {ocrResult.status === 'parsed' && ocrResult.parsedData && (
                  <>
                    <Alert className="border-green-200 bg-green-50">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <AlertDescription className="text-green-800">
                        <div className="mb-2 font-medium">JSON analisado com sucesso!</div>
                        <div className="space-y-1 text-sm">
                          <p>Total de Questões: {ocrResult.parsedData.questions.length}</p>
                          <p>Instituição: {ocrResult.parsedData.metadata.institution}</p>
                          <p>Banca Examinadora: {ocrResult.parsedData.metadata.exam_board}</p>
                        </div>
                      </AlertDescription>
                    </Alert>
                    <Button
                      onClick={() => {
                        const dataToUpload = updatedParsedData || ocrResult.parsedData;
                        if (dataToUpload) {
                          onFileUploaded(dataToUpload);
                        }
                      }}
                      className="w-full"
                    >
                      Usar Esses Dados
                    </Button>
                  </>
                )}

                {/* Manual Image Assignment */}
                {ocrResult.extractedImages &&
                  ocrResult.extractedImages.length > 0 &&
                  ocrResult.parsedData && (
                    <ManualImageSelector
                      extractedImages={ocrResult.extractedImages}
                      parsedData={updatedParsedData || ocrResult.parsedData}
                      onUpdate={setUpdatedParsedData}
                    />
                  )}

                <div className="space-y-2">
                  <Label>Texto Extraído Bruto</Label>
                  <Textarea
                    value={ocrResult.rawText}
                    readOnly
                    className="min-h-[300px] font-mono text-xs"
                  />
                  {ocrResult.status === 'unparsed' && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        parseFile(ocrResult.rawText);
                      }}
                    >
                      Tentar Analisar como JSON
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
