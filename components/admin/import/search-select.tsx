'use client';

import { useState, useEffect, useCallback } from 'react';
import { Check, ChevronsUpDown, Plus, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

interface SearchSelectProps {
  entityType: 'institution' | 'examBoard' | 'position' | 'specialization' | 'subject' | 'topic';
  value?: string;
  onValueChange: (value: string, isNew: boolean) => void;
  placeholder?: string;
  label?: string;
  parentId?: string; // For hierarchical entities like specialization or topic
}

interface Entity {
  id: string;
  name: string;
  code?: string;
  isNew?: boolean;
}

export function SearchSelect({
  entityType,
  value,
  onValueChange,
  placeholder = 'Select or create...',
  label,
  parentId,
}: SearchSelectProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [entities, setEntities] = useState<Entity[]>([]);
  const [loading, setLoading] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newEntityName, setNewEntityName] = useState('');
  const [newEntityCode, setNewEntityCode] = useState('');
  const [creating, setCreating] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const searchEntities = useCallback(
    async (searchQuery: string) => {
      setLoading(true);
      try {
        const params = new URLSearchParams({
          type: entityType,
          q: searchQuery,
          ...(parentId && { parentId }),
        });

        console.log(`Searching ${entityType} with query: "${searchQuery}"`);
        const response = await fetch(`/api/admin/import/search?${params}`);

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Search API error:', errorData);
          throw new Error(`Failed to search entities: ${errorData.error || response.statusText}`);
        }

        const data = await response.json();
        console.log(`Search results for ${entityType}:`, data.results);
        setEntities(data.results || []);
        setHasSearched(true);
      } catch (error) {
        console.error('Error searching entities:', error);
        setEntities([]);
      } finally {
        setLoading(false);
      }
    },
    [entityType, parentId]
  );

  // Handle dropdown open/close
  useEffect(() => {
    if (open) {
      // Reset search state when opening
      if (!hasSearched) {
        const initialSearch = placeholder || '';
        setSearchTerm(initialSearch);
        searchEntities(initialSearch);
      }
    } else {
      // Reset when closing
      setHasSearched(false);
    }
  }, [open]); // eslint-disable-line react-hooks/exhaustive-deps

  // Handle search term changes
  useEffect(() => {
    if (open && hasSearched) {
      const timeoutId = setTimeout(() => {
        searchEntities(searchTerm);
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [searchTerm, open, hasSearched]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleCreate = async () => {
    setCreating(true);
    try {
      const response = await fetch(`/api/admin/entities/${entityType}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newEntityName,
          code: newEntityCode,
          ...(parentId && { parentId }),
        }),
      });

      if (!response.ok) throw new Error('Failed to create entity');

      const newEntity = await response.json();
      setEntities([...entities, { ...newEntity, isNew: true }]);
      onValueChange(newEntity.id, true);
      setOpen(false);
      setCreateDialogOpen(false);
      setNewEntityName('');
      setNewEntityCode('');
      setSearchTerm('');
      setHasSearched(false);
    } catch (error) {
      console.error('Error creating entity:', error);
    } finally {
      setCreating(false);
    }
  };

  const selectedEntity =
    entities.find((e) => e.id === value) || (value ? { id: value, name: value } : null);
  const displayName = searchTerm || placeholder || '';

  return (
    <>
      <div className="space-y-2">
        {label && <Label>{label}</Label>}
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className={cn('w-full justify-between', !selectedEntity && 'text-muted-foreground')}
            >
              <span className="truncate">
                {selectedEntity ? (
                  <span className="flex items-center gap-2">
                    {selectedEntity.name}
                    {selectedEntity.isNew && (
                      <Badge variant="secondary" className="text-xs">
                        New
                      </Badge>
                    )}
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <span className="text-orange-600 dark:text-orange-400">⚠</span>
                    {placeholder}
                  </span>
                )}
              </span>
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <Command shouldFilter={false}>
              <CommandInput
                placeholder={`Search ${entityType}...`}
                value={searchTerm}
                onValueChange={(value) => {
                  setSearchTerm(value);
                }}
              />
              <CommandList>
                {loading ? (
                  <div className="flex items-center justify-center py-6">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <>
                    {entities.length > 0 ? (
                      <>
                        <CommandGroup heading="Select existing">
                          {entities.map((entity) => (
                            <CommandItem
                              key={entity.id}
                              value={entity.name}
                              onSelect={() => {
                                onValueChange(entity.id, false);
                                setOpen(false);
                              }}
                            >
                              <Check
                                className={cn(
                                  'mr-2 h-4 w-4',
                                  value === entity.id ? 'opacity-100' : 'opacity-0'
                                )}
                              />
                              <span className="flex-1">{entity.name}</span>
                              {entity.code && (
                                <span className="text-muted-foreground ml-2 text-xs">
                                  ({entity.code})
                                </span>
                              )}
                              {entity.isNew && (
                                <Badge variant="secondary" className="ml-2 text-xs">
                                  New
                                </Badge>
                              )}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                        {displayName && (
                          <>
                            <CommandSeparator />
                            <CommandGroup heading="Actions">
                              <CommandItem
                                value={`create-new-${displayName}`}
                                onSelect={() => {
                                  setCreateDialogOpen(true);
                                  setNewEntityName(displayName);
                                  setOpen(false);
                                }}
                              >
                                <Plus className="mr-2 h-4 w-4" />
                                Create New
                              </CommandItem>
                            </CommandGroup>
                          </>
                        )}
                      </>
                    ) : (
                      <>
                        <div className="py-6 text-center">
                          <p className="text-muted-foreground mb-2 text-sm">
                            No {entityType} found
                          </p>
                          {displayName && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setCreateDialogOpen(true);
                                setNewEntityName(displayName);
                                setOpen(false);
                              }}
                            >
                              <Plus className="mr-2 h-4 w-4" />
                              Create &ldquo;{displayName}&rdquo;
                            </Button>
                          )}
                        </div>
                      </>
                    )}
                  </>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>

      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New {entityType}</DialogTitle>
            <DialogDescription>Add a new {entityType} to the database</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={newEntityName}
                onChange={(e) => setNewEntityName(e.target.value)}
                placeholder={`Enter ${entityType} name`}
              />
            </div>
            {(entityType === 'institution' ||
              entityType === 'examBoard' ||
              entityType === 'position') && (
              <div className="grid gap-2">
                <Label htmlFor="code">Code (optional)</Label>
                <Input
                  id="code"
                  value={newEntityCode}
                  onChange={(e) => setNewEntityCode(e.target.value)}
                  placeholder={`Enter ${entityType} code`}
                />
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setCreateDialogOpen(false)}
              disabled={creating}
            >
              Cancel
            </Button>
            <Button onClick={handleCreate} disabled={creating || !newEntityName.trim()}>
              {creating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
