'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { SearchSelect } from './search-select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ArrowRight, ArrowLeft, Zap, Edit3, Search } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import type { ImportData, MappedData } from '@/types/import';

interface DataMapperProps {
  importData: ImportData;
  onDataMapped: (mappedData: MappedData) => void;
  onBack: () => void;
  onSelectExistingExam?: () => void;
}

export function DataMapper({
  importData,
  onDataMapped,
  onBack,
  onSelectExistingExam,
}: DataMapperProps) {
  const [autoFillMessage, setAutoFillMessage] = useState<{
    type: 'success' | 'error';
    text: string;
  } | null>(null);

  // Format the date properly for the date input
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return '';
    // If it's already in YYYY-MM-DD format, return as is
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) return dateString;
    // If it's in DD/MM/YYYY format, convert it
    const match = dateString.match(/^(\d{2})\/(\d{2})\/(\d{4})$/);
    if (match) {
      return `${match[3]}-${match[2]}-${match[1]}`;
    }
    return '';
  };

  const [mappedData, setMappedData] = useState<MappedData>({
    institutionId: '',
    institutionIsNew: false,
    examBoardId: '',
    examBoardIsNew: false,
    positionId: '',
    positionIsNew: false,
    specializationId: '',
    specializationIsNew: false,
    year: importData.metadata.year,
    testType: importData.metadata.test_type,
    booklet: importData.metadata.booklet || '',
    applicationDate: formatDate(importData.metadata.application_date),
    manualMode: false,
    manualInstitution: importData.metadata.institution,
    manualExamBoard: importData.metadata.exam_board,
    manualPosition: importData.metadata.position,
    manualSpecialization: importData.metadata.specialization || '',
    subjectMappings: {} as Record<string, { id: string; isNew: boolean }>,
    topicMappings: {} as Record<string, { id: string; isNew: boolean; subjectId: string }>,
  });

  const [currentStep, setCurrentStep] = useState(0);
  const [isAutoFilling, setIsAutoFilling] = useState(false);
  const [manualMode, setManualMode] = useState(false);
  const [examMode, setExamMode] = useState<'new' | 'existing'>('new');

  const uniqueSubjects = Array.from(
    new Set(importData.questions.map((q) => q.subject).filter(Boolean))
  );

  const uniqueTopics = Array.from(
    new Set(importData.questions.map((q) => q.topic).filter(Boolean))
  );

  const steps = [
    {
      title: 'Exam Information',
      description: 'Map the basic exam metadata',
    },
    {
      title: 'Subjects & Topics',
      description: 'Map question subjects and topics',
    },
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onDataMapped(mappedData);
    }
  };

  const canProceed = () => {
    if (currentStep === 0) {
      // If using existing exam mode, we don't need to validate metadata
      if (examMode === 'existing') {
        return true;
      }
      if (manualMode) {
        return (
          mappedData.manualInstitution &&
          mappedData.manualExamBoard &&
          mappedData.manualPosition &&
          mappedData.year &&
          mappedData.testType
        );
      }
      return (
        mappedData.institutionId &&
        mappedData.examBoardId &&
        mappedData.positionId &&
        mappedData.year &&
        mappedData.testType
      );
    }
    return true;
  };

  const autoFillFromSuggestions = async () => {
    setIsAutoFilling(true);

    try {
      // Helper function to search and create/select entity
      const findOrCreateEntity = async (
        type: string,
        name: string,
        parentId?: string
      ): Promise<{ id: string; isNew: boolean }> => {
        const params = new URLSearchParams({
          type,
          q: name,
          ...(parentId && { parentId }),
        });

        const searchResponse = await fetch(`/api/admin/import/search?${params}`);
        const searchData = await searchResponse.json();

        if (searchData.results && searchData.results.length > 0) {
          // Found existing entity
          return { id: searchData.results[0].id, isNew: false };
        } else {
          // Create new entity
          const createResponse = await fetch(`/api/admin/entities/${type}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name, parentId }),
          });

          if (!createResponse.ok) throw new Error(`Failed to create ${type}`);

          const newEntity = await createResponse.json();
          return { id: newEntity.id, isNew: true };
        }
      };

      if (currentStep === 0) {
        // Auto-fill exam metadata
        const [institution, examBoard, position] = await Promise.all([
          findOrCreateEntity('institution', importData.metadata.institution),
          findOrCreateEntity('examBoard', importData.metadata.exam_board),
          findOrCreateEntity('position', importData.metadata.position),
        ]);

        let specialization = { id: '', isNew: false };
        if (importData.metadata.specialization && position.id) {
          specialization = await findOrCreateEntity(
            'specialization',
            importData.metadata.specialization,
            position.id
          );
        }

        setMappedData({
          ...mappedData,
          institutionId: institution.id,
          institutionIsNew: institution.isNew,
          examBoardId: examBoard.id,
          examBoardIsNew: examBoard.isNew,
          positionId: position.id,
          positionIsNew: position.isNew,
          specializationId: specialization.id,
          specializationIsNew: specialization.isNew,
        });

        setAutoFillMessage({
          type: 'success',
          text: 'All exam metadata fields have been populated',
        });
        setTimeout(() => setAutoFillMessage(null), 5000);
      } else if (currentStep === 1) {
        // Auto-fill subjects and topics
        const subjectMappings: Record<string, { id: string; isNew: boolean }> = {};
        const topicMappings: Record<string, { id: string; isNew: boolean; subjectId: string }> = {};

        // Process subjects first
        for (const subject of uniqueSubjects as string[]) {
          const result = await findOrCreateEntity('subject', subject);
          subjectMappings[subject] = result;
        }

        // Then process topics with their associated subjects
        for (const topic of uniqueTopics as string[]) {
          const questionWithTopic = importData.questions.find((q) => q.topic === topic);
          const relatedSubject = questionWithTopic?.subject;
          const subjectId = relatedSubject ? subjectMappings[relatedSubject]?.id : '';

          const result = await findOrCreateEntity('topic', topic, subjectId);
          topicMappings[topic] = { ...result, subjectId };
        }

        setMappedData({
          ...mappedData,
          subjectMappings,
          topicMappings,
        });

        setAutoFillMessage({
          type: 'success',
          text: 'All subjects and topics have been mapped',
        });
        setTimeout(() => setAutoFillMessage(null), 5000);
      }
    } catch (error) {
      console.error('Auto-fill error:', error);
      setAutoFillMessage({
        type: 'error',
        text: 'There was an error auto-filling the fields. Please try again.',
      });
      setTimeout(() => setAutoFillMessage(null), 5000);
    } finally {
      setIsAutoFilling(false);
    }
  };

  return (
    <div className="space-y-6">
      {autoFillMessage && (
        <Alert variant={autoFillMessage.type === 'error' ? 'destructive' : 'default'}>
          <AlertDescription>{autoFillMessage.text}</AlertDescription>
        </Alert>
      )}

      <div className="mb-6 flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{steps[currentStep].title}</h2>
          <p className="text-muted-foreground">{steps[currentStep].description}</p>
        </div>
        <div className="flex items-center gap-2">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`h-2 w-16 rounded-full ${
                index <= currentStep ? 'bg-primary' : 'bg-muted'
              }`}
            />
          ))}
        </div>
      </div>

      {currentStep === 0 && (
        <>
          {/* Exam Mode Selection */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Select Import Method</CardTitle>
              <CardDescription>
                Choose whether to create a new exam or add questions to an existing exam
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={examMode}
                onValueChange={(value) => setExamMode(value as 'new' | 'existing')}
              >
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <RadioGroupItem value="new" id="new-exam" />
                    <div className="flex-1">
                      <Label htmlFor="new-exam" className="cursor-pointer">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">Create New Exam</span>
                          <Badge variant="secondary">Recommended for new exams</Badge>
                        </div>
                        <p className="text-muted-foreground mt-1 text-sm">
                          Create a new exam entry with the metadata from your file
                        </p>
                      </Label>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <RadioGroupItem value="existing" id="existing-exam" />
                    <div className="flex-1">
                      <Label htmlFor="existing-exam" className="cursor-pointer">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">Use Existing Exam</span>
                          <Badge variant="outline">For adding questions</Badge>
                        </div>
                        <p className="text-muted-foreground mt-1 text-sm">
                          Add questions to an exam that already exists in the database
                        </p>
                      </Label>
                    </div>
                  </div>
                </div>
              </RadioGroup>

              {examMode === 'existing' && onSelectExistingExam && (
                <div className="mt-4">
                  <Button onClick={onSelectExistingExam} className="w-full">
                    <Search className="mr-2 h-4 w-4" />
                    Search for Existing Exam
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {examMode === 'new' && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Exam Metadata</CardTitle>
                    <CardDescription>
                      {manualMode
                        ? 'Enter the exact values for the exam metadata manually.'
                        : 'Click on each dropdown to search for existing values or create new ones. The text shown is a suggestion based on your JSON data.'}
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setManualMode(!manualMode);
                        setMappedData({ ...mappedData, manualMode: !manualMode });
                      }}
                    >
                      {manualMode ? (
                        <>
                          <Search className="mr-2 h-4 w-4" />
                          Search Mode
                        </>
                      ) : (
                        <>
                          <Edit3 className="mr-2 h-4 w-4" />
                          Manual Mode
                        </>
                      )}
                    </Button>
                    {!manualMode && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={autoFillFromSuggestions}
                        disabled={isAutoFilling}
                      >
                        {isAutoFilling ? (
                          <>
                            <div className="border-primary mr-2 h-4 w-4 animate-spin rounded-full border-b-2"></div>
                            Auto-filling...
                          </>
                        ) : (
                          <>
                            <Zap className="mr-2 h-4 w-4" />
                            Auto-fill All
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {manualMode ? (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Institution *</Label>
                        <Input
                          value={mappedData.manualInstitution}
                          onChange={(e) =>
                            setMappedData({ ...mappedData, manualInstitution: e.target.value })
                          }
                          placeholder="Enter institution name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Exam Board *</Label>
                        <Input
                          value={mappedData.manualExamBoard}
                          onChange={(e) =>
                            setMappedData({ ...mappedData, manualExamBoard: e.target.value })
                          }
                          placeholder="Enter exam board name"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Position *</Label>
                        <Input
                          value={mappedData.manualPosition}
                          onChange={(e) =>
                            setMappedData({ ...mappedData, manualPosition: e.target.value })
                          }
                          placeholder="Enter position name"
                        />
                      </div>
                      {(importData.metadata.specialization || mappedData.manualSpecialization) && (
                        <div className="space-y-2">
                          <Label>Specialization</Label>
                          <Input
                            value={mappedData.manualSpecialization}
                            onChange={(e) =>
                              setMappedData({ ...mappedData, manualSpecialization: e.target.value })
                            }
                            placeholder="Enter specialization name"
                          />
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <SearchSelect
                        entityType="institution"
                        value={mappedData.institutionId}
                        onValueChange={(value, isNew) =>
                          setMappedData({
                            ...mappedData,
                            institutionId: value,
                            institutionIsNew: isNew,
                          })
                        }
                        label="Institution *"
                        placeholder={importData.metadata.institution}
                      />

                      <SearchSelect
                        entityType="examBoard"
                        value={mappedData.examBoardId}
                        onValueChange={(value, isNew) =>
                          setMappedData({
                            ...mappedData,
                            examBoardId: value,
                            examBoardIsNew: isNew,
                          })
                        }
                        label="Exam Board *"
                        placeholder={importData.metadata.exam_board}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <SearchSelect
                        entityType="position"
                        value={mappedData.positionId}
                        onValueChange={(value, isNew) =>
                          setMappedData({ ...mappedData, positionId: value, positionIsNew: isNew })
                        }
                        label="Position *"
                        placeholder={importData.metadata.position}
                      />

                      {importData.metadata.specialization && (
                        <SearchSelect
                          entityType="specialization"
                          value={mappedData.specializationId}
                          onValueChange={(value, isNew) =>
                            setMappedData({
                              ...mappedData,
                              specializationId: value,
                              specializationIsNew: isNew,
                            })
                          }
                          label="Specialization"
                          placeholder={importData.metadata.specialization}
                          parentId={mappedData.positionId}
                        />
                      )}
                    </div>
                  </>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Year *</Label>
                    <Input
                      type="number"
                      value={mappedData.year}
                      onChange={(e) =>
                        setMappedData({ ...mappedData, year: parseInt(e.target.value) })
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Test Type *</Label>
                    <Select
                      value={mappedData.testType}
                      onValueChange={(value) => setMappedData({ ...mappedData, testType: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="MULTIPLA_ESCOLHA">Multiple Choice</SelectItem>
                        <SelectItem value="CERTO_ERRADO">True/False</SelectItem>
                        <SelectItem value="DISCURSIVA">Essay</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {(importData.metadata.booklet || importData.metadata.application_date || false) && (
                  <div className="grid grid-cols-2 gap-4">
                    {importData.metadata.booklet && (
                      <div className="space-y-2">
                        <Label>Booklet</Label>
                        <Input
                          value={mappedData.booklet}
                          onChange={(e) =>
                            setMappedData({ ...mappedData, booklet: e.target.value })
                          }
                          placeholder={importData.metadata.booklet}
                        />
                      </div>
                    )}

                    {importData.metadata.application_date && (
                      <div className="space-y-2">
                        <Label>Application Date</Label>
                        <Input
                          type="date"
                          value={mappedData.applicationDate}
                          onChange={(e) =>
                            setMappedData({ ...mappedData, applicationDate: e.target.value })
                          }
                        />
                      </div>
                    )}

                    {false && (
                      <div className="space-y-2">
                        <Label>Publication Date</Label>
                        <Input
                          type="date"
                          value={''}
                          onChange={() => setMappedData({ ...mappedData })}
                        />
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}

      {currentStep === 1 && (
        <div className="space-y-6">
          <div className="mb-4 flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={autoFillFromSuggestions}
              disabled={isAutoFilling}
            >
              {isAutoFilling ? (
                <>
                  <div className="border-primary mr-2 h-4 w-4 animate-spin rounded-full border-b-2"></div>
                  Auto-filling...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  Auto-fill All Subjects & Topics
                </>
              )}
            </Button>
          </div>

          {uniqueSubjects.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Subjects</CardTitle>
                <CardDescription>Map subjects found in the questions</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {(uniqueSubjects as string[]).map((subject: string) => (
                  <div key={subject} className="flex items-center gap-4">
                    <Badge variant="secondary" className="min-w-[200px]">
                      {subject}
                    </Badge>
                    <ArrowRight className="text-muted-foreground h-4 w-4" />
                    <SearchSelect
                      entityType="subject"
                      value={mappedData.subjectMappings[subject]?.id}
                      onValueChange={(value, isNew) =>
                        setMappedData({
                          ...mappedData,
                          subjectMappings: {
                            ...mappedData.subjectMappings,
                            [subject]: { id: value, isNew },
                          },
                        })
                      }
                      placeholder={`Select or create "${subject}"`}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {uniqueTopics.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Topics</CardTitle>
                <CardDescription>Map topics found in the questions</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {(uniqueTopics as string[]).map((topic: string) => {
                  const questionWithTopic = importData.questions.find((q) => q.topic === topic);
                  const relatedSubject = questionWithTopic?.subject;
                  const selectedSubjectId = relatedSubject
                    ? mappedData.subjectMappings[relatedSubject]?.id
                    : '';

                  return (
                    <div key={topic} className="flex items-center gap-4">
                      <Badge variant="secondary" className="min-w-[200px]">
                        {topic}
                      </Badge>
                      <ArrowRight className="text-muted-foreground h-4 w-4" />
                      <SearchSelect
                        entityType="topic"
                        value={mappedData.topicMappings[topic]?.id}
                        onValueChange={(value, isNew) =>
                          setMappedData({
                            ...mappedData,
                            topicMappings: {
                              ...mappedData.topicMappings,
                              [topic]: { id: value, isNew, subjectId: selectedSubjectId },
                            },
                          })
                        }
                        placeholder={`Select or create "${topic}"`}
                        parentId={selectedSubjectId}
                      />
                    </div>
                  );
                })}
              </CardContent>
            </Card>
          )}
        </div>
      )}

      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="flex items-center gap-4">
          {currentStep === 0 && !canProceed() && (
            <p className="text-muted-foreground text-sm">
              Please select all required fields to continue
            </p>
          )}
          <Button onClick={handleNext} disabled={!canProceed()}>
            {currentStep < steps.length - 1 ? (
              <>
                Next
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            ) : (
              'Continue to Preview'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
