'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ArrowLeft, ArrowRight, CheckCircle, XCircle, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getRenderedContent } from '@/lib/render-content';
import type { ImportQuestion, ImportData, AssociatedTextBlock } from '@/types/import';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface QuestionPreviewProps {
  questions: ImportQuestion[];
  selectedQuestions: Set<number>;
  onSelectionChange: (selection: Set<number>) => void;
  onBack: () => void;
  onImport: () => void;
  importData?: ImportData;
}

export function QuestionPreview({
  questions,
  selectedQuestions,
  onSelectionChange,
  onBack,
  onImport,
  importData,
}: QuestionPreviewProps) {
  const [expandedQuestions, setExpandedQuestions] = useState<Set<number>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [keyFilter, setKeyFilter] = useState<'all' | 'with-answer' | 'cancelled' | 'no-answer'>(
    'all'
  );

  // Create a map of associated texts if available
  const textCatalog = new Map<string, string>();
  if (importData?.associated_texts_catalog) {
    importData.associated_texts_catalog.forEach((block: AssociatedTextBlock) => {
      textCatalog.set(block.id, block.content);
    });
  }

  const toggleQuestionExpanded = (questionNumber: number) => {
    const newExpanded = new Set(expandedQuestions);
    if (newExpanded.has(questionNumber)) {
      newExpanded.delete(questionNumber);
    } else {
      newExpanded.add(questionNumber);
    }
    setExpandedQuestions(newExpanded);
  };

  const toggleQuestionSelection = (questionNumber: number) => {
    const newSelection = new Set(selectedQuestions);
    if (newSelection.has(questionNumber)) {
      newSelection.delete(questionNumber);
    } else {
      newSelection.add(questionNumber);
    }
    onSelectionChange(newSelection);
  };

  const filteredQuestions = questions.filter((q) => {
    const matchesSearch =
      searchTerm === '' ||
      q.stem.toLowerCase().includes(searchTerm.toLowerCase()) ||
      q.number.toString().includes(searchTerm);

    // Apply key filter
    let matchesKeyFilter = true;

    if (keyFilter === 'with-answer') {
      matchesKeyFilter = !q.is_null && q.correct_answer_order !== null;
    } else if (keyFilter === 'cancelled') {
      matchesKeyFilter = q.is_null;
    } else if (keyFilter === 'no-answer') {
      matchesKeyFilter = !q.is_null && q.correct_answer_order === null;
    }

    return matchesSearch && matchesKeyFilter;
  });

  const selectAll = () => {
    onSelectionChange(new Set(filteredQuestions.map((q) => q.number)));
  };

  const deselectAll = () => {
    onSelectionChange(new Set());
  };

  // Count different types of questions
  const totalCount = questions.length;
  const withAnswerCount = questions.filter(
    (q) => !q.is_null && q.correct_answer_order !== null
  ).length;
  const cancelledCount = questions.filter((q) => q.is_null).length;
  const noAnswerCount = questions.filter(
    (q) => !q.is_null && q.correct_answer_order === null
  ).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle>Select Questions to Import</CardTitle>
          <CardDescription>
            Review and select which questions to import from the file
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Stats */}
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline" className="text-sm">
                Total: {totalCount}
              </Badge>
              <Badge variant="outline" className="text-sm text-green-600">
                <CheckCircle className="mr-1 h-3 w-3" />
                With Answer: {withAnswerCount}
              </Badge>
              <Badge variant="outline" className="text-sm text-red-600">
                <XCircle className="mr-1 h-3 w-3" />
                Cancelled: {cancelledCount}
              </Badge>
              <Badge variant="outline" className="text-sm text-yellow-600">
                <Info className="mr-1 h-3 w-3" />
                No Answer: {noAnswerCount}
              </Badge>
            </div>

            {/* Filters */}
            <div className="flex gap-4">
              <div className="flex-1">
                <Label htmlFor="search">Search</Label>
                <Input
                  id="search"
                  placeholder="Search by question text or number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="w-48">
                <Label htmlFor="filter">Filter by Answer Key</Label>
                <Select
                  value={keyFilter}
                  onValueChange={(v) =>
                    setKeyFilter(v as 'all' | 'with-answer' | 'cancelled' | 'no-answer')
                  }
                >
                  <SelectTrigger id="filter">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Questions</SelectItem>
                    <SelectItem value="with-answer">With Answer</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="no-answer">No Answer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Selection controls */}
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={selectAll}>
                Select All ({filteredQuestions.length})
              </Button>
              <Button variant="outline" size="sm" onClick={deselectAll}>
                Deselect All
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Questions list */}
      <ScrollArea className="h-[500px] rounded-md border p-4">
        <div className="space-y-4">
          {filteredQuestions.map((question) => {
            const isExpanded = expandedQuestions.has(question.number);
            const isSelected = selectedQuestions.has(question.number);
            const associatedText = question.associated_text_id
              ? textCatalog.get(question.associated_text_id) || null
              : null;

            return (
              <Card
                key={question.number}
                className={cn('transition-colors', isSelected && 'border-primary bg-primary/5')}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={() => toggleQuestionSelection(question.number)}
                      />
                      <span className="font-medium">Question {question.number}</span>
                      {question.subject && <Badge variant="secondary">{question.subject}</Badge>}
                      {question.topic && <Badge variant="outline">{question.topic}</Badge>}
                    </div>
                    <div className="flex items-center gap-2">
                      {question.is_null ? (
                        <Badge variant="destructive" className="text-xs">
                          <XCircle className="mr-1 h-3 w-3" />
                          Cancelled
                        </Badge>
                      ) : question.correct_answer_order !== null ? (
                        <Badge variant="outline" className="text-xs text-green-600">
                          <CheckCircle className="mr-1 h-3 w-3" />
                          Answer: {String.fromCharCode(64 + question.correct_answer_order)}
                        </Badge>
                      ) : (
                        <Badge variant="secondary" className="text-xs">
                          <Info className="mr-1 h-3 w-3" />
                          No Answer
                        </Badge>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleQuestionExpanded(question.number)}
                      >
                        {isExpanded ? 'Hide' : 'Show'}
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                {isExpanded && (
                  <CardContent className="pt-0">
                    <div className="space-y-4">
                      {associatedText && (
                        <div className="bg-muted rounded-md p-3">
                          <p className="text-muted-foreground mb-1 text-xs font-medium">
                            Associated Text:
                          </p>
                          <div
                            className="text-foreground text-sm"
                            {...getRenderedContent(associatedText)}
                          />
                        </div>
                      )}

                      <div>
                        <p className="mb-2 text-sm font-medium">Question:</p>
                        <div className="text-sm" {...getRenderedContent(question.stem)} />
                      </div>

                      <div>
                        <p className="mb-2 text-sm font-medium">Options:</p>
                        <div className="space-y-2">
                          {question.options.map((option) => (
                            <div
                              key={option.order}
                              className={cn(
                                'flex items-start gap-2 rounded p-2',
                                question.correct_answer_order === option.order &&
                                  'border border-green-200 bg-green-50'
                              )}
                            >
                              <span className="text-sm font-medium">
                                {String.fromCharCode(64 + option.order)}.
                              </span>
                              <div
                                className="flex-1 text-sm"
                                {...getRenderedContent(option.text)}
                              />
                              {question.correct_answer_order === option.order && (
                                <CheckCircle className="mt-0.5 h-4 w-4 text-green-600" />
                              )}
                            </div>
                          ))}
                        </div>
                      </div>

                      {question.change_reason && (
                        <div className="rounded-md border border-yellow-200 bg-yellow-50 p-3">
                          <p className="mb-1 text-xs font-medium text-yellow-800">Change Reason:</p>
                          <div
                            className="text-sm text-yellow-700"
                            {...getRenderedContent(question.change_reason)}
                          />
                        </div>
                      )}
                    </div>
                  </CardContent>
                )}
              </Card>
            );
          })}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Mapping
        </Button>
        <div className="flex gap-2">
          <div className="text-muted-foreground self-center text-sm">
            {selectedQuestions.size} questions selected
          </div>
          <Button onClick={onImport} disabled={selectedQuestions.size === 0}>
            Import Selected Questions
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
