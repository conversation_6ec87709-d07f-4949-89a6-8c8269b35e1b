'use client';

import * as React from 'react';
import { Moon, Sun } from 'lucide-react';
import { useTheme } from 'next-themes';

import { Button } from '@/components/ui/button';

interface ThemeToggleProps {
  iconOnly?: boolean;
}

export function ThemeToggle({ iconOnly = false }: ThemeToggleProps) {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <Button
      variant="ghost"
      size={iconOnly ? 'icon' : 'sm'}
      onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
      className={
        iconOnly
          ? 'h-8 w-8 p-0'
          : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground flex-1 group-data-[collapsible=icon]:h-8 group-data-[collapsible=icon]:w-8 group-data-[collapsible=icon]:flex-none group-data-[collapsible=icon]:p-0'
      }
    >
      <div className="relative flex items-center">
        <Sun className="h-4 w-4 scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" />
        <Moon className="absolute h-4 w-4 scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0" />
      </div>
      {!iconOnly && <span className="ml-2 group-data-[collapsible=icon]:hidden">Tema</span>}
      <span className="sr-only">Alternar tema</span>
    </Button>
  );
}
