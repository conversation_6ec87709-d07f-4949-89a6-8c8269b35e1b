'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useOpenRouter } from '@/hooks/use-openrouter';
import { OpenRouterModel, ChatMessage } from '@/lib/openrouter/client';
import { ModelConfig } from '@/lib/openrouter/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Send, Bot, User, Trash2 } from 'lucide-react';
import { SidebarTrigger } from '@/components/ui/sidebar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from '@/components/ui/select';
import { ChatMessageMetadata } from '@/components/chat-message-metadata';
import { ChatReasoningBox } from '@/components/chat-reasoning-box';
import { MarkdownRenderer } from '@/components/markdown-renderer';
import { ChatSettings, type ChatSettingsConfig } from '@/components/chat-settings';

interface ChatBotProps {
  conversationId?: string;
  initialMessages?: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    reasoning?: string;
    metadata?: ChatMessage['metadata'];
  }>;
  systemPrompt?: string;
  placeholder?: string;
  title?: string;
  streaming?: boolean;
  defaultModel?: OpenRouterModel;
  showModelSelector?: boolean;
  onMessageSent?: (message: string) => void;
  onMessageReceived?: (message: {
    role: 'user' | 'assistant';
    content: string;
    reasoning?: string;
    model?: string;
    metadata?: ChatMessage['metadata'];
  }) => void;
  onMessageDeleted?: (messageIndex: number) => void;
  onConversationTitleChange?: (title: string) => void;
  availableModels?: ModelConfig[];
  reasoning?: {
    enabled?: boolean;
    effort?: 'low' | 'medium' | 'high';
    maxTokens?: number;
    exclude?: boolean;
  };
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

export function ChatBot({
  initialMessages = [],
  systemPrompt,
  placeholder = 'Type your message...',
  title = 'AI Assistant',
  streaming = true,
  defaultModel = 'openai/gpt-4o-mini',
  showModelSelector = true,
  onMessageSent,
  onMessageReceived,
  onMessageDeleted,
  availableModels = [],
  reasoning,
  temperature,
  maxTokens,
  topP,
  frequencyPenalty,
  presencePenalty,
}: ChatBotProps) {
  const [input, setInput] = useState('');
  const [selectedModel, setSelectedModel] = useState<OpenRouterModel>(defaultModel);

  // Only show enabled models
  const enabledModels = availableModels.filter((m) => m.enabled);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const initialMessageCountRef = useRef<number>(0);
  const savedMessageTimestampsRef = useRef<Set<number>>(new Set());

  // Settings state
  const [settings, setSettings] = useState<ChatSettingsConfig>({
    systemPrompt: systemPrompt || '',
    temperature: temperature ?? 0.7,
    maxTokens: maxTokens ?? 1000,
    topP: topP ?? 1,
    frequencyPenalty: frequencyPenalty ?? 0,
    presencePenalty: presencePenalty ?? 0,
    reasoning: reasoning,
    availableModels: availableModels,
  });

  // Prepare initial messages with system prompt
  const preparedInitialMessages = React.useMemo(() => {
    const msgs = [...initialMessages];
    if (systemPrompt && !msgs.some((m) => m.role === 'system')) {
      msgs.unshift({ role: 'system', content: systemPrompt });
    }
    return msgs;
  }, [initialMessages, systemPrompt]);

  const { messages, sendMessage, streamMessage, loading, clearMessages, setMessages } =
    useOpenRouter({
      model: selectedModel,
      initialMessages: preparedInitialMessages,
      reasoning: settings.reasoning,
      temperature: settings.temperature,
      maxTokens: settings.maxTokens,
      topP: settings.topP,
      frequencyPenalty: settings.frequencyPenalty,
      presencePenalty: settings.presencePenalty,
    });

  // Store the initial message count to avoid saving existing messages
  useEffect(() => {
    initialMessageCountRef.current = preparedInitialMessages.length;
  }, [preparedInitialMessages.length]);

  useEffect(() => {
    // Scroll to bottom when messages change
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector(
        '[data-radix-scroll-area-viewport]'
      );
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [messages]);

  // Track and save new assistant messages only
  useEffect(() => {
    // Only process messages that were added after initialization
    if (messages.length > initialMessageCountRef.current) {
      const lastMessage = messages[messages.length - 1];

      if (lastMessage.role === 'assistant' && lastMessage.metadata?.timestamp) {
        const timestamp = lastMessage.metadata.timestamp;

        // Check if we've already saved this message
        if (!savedMessageTimestampsRef.current.has(timestamp)) {
          savedMessageTimestampsRef.current.add(timestamp);

          // This is a newly generated message, save it
          onMessageReceived?.({
            role: 'assistant',
            content: lastMessage.content,
            reasoning: lastMessage.reasoning,
            model: selectedModel,
            metadata: lastMessage.metadata,
          });
        }
      }
    }
  }, [messages, onMessageReceived, selectedModel]); // Include all dependencies

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || loading) return;

    const message = input.trim();
    setInput('');
    onMessageSent?.(message);

    // Save user message
    onMessageReceived?.({
      role: 'user',
      content: message,
    });

    if (streaming) {
      await streamMessage(message);
    } else {
      await sendMessage(message);
    }
  };

  const handleClear = () => {
    if (systemPrompt) {
      setMessages([{ role: 'system', content: systemPrompt }]);
    } else {
      clearMessages();
    }
  };

  const handleDeleteMessage = (index: number) => {
    if (!confirm('Are you sure you want to delete this message?')) return;

    // Get the actual message index in the full messages array (including system message)
    const actualIndex = messages.findIndex((_, i) => {
      const displayIndex = messages.slice(0, i + 1).filter((m) => m.role !== 'system').length - 1;
      return displayIndex === index;
    });

    if (actualIndex !== -1) {
      // Remove the message from local state
      const newMessages = messages.filter((_, i) => i !== actualIndex);
      setMessages(newMessages);

      // Notify parent component
      onMessageDeleted?.(index);
    }
  };

  // Filter out system messages from display
  const displayMessages = messages.filter((m) => m.role !== 'system');

  // Group enabled models by category
  const modelsByCategory = enabledModels.reduce(
    (acc, model) => {
      const category = model.category || 'Other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(model);
      return acc;
    },
    {} as Record<string, ModelConfig[]>
  );

  return (
    <div className="flex h-full w-full flex-col">
      <div className="bg-background sticky top-0 z-10 flex-shrink-0 border-b">
        <div className="flex items-center gap-4 px-6 py-3">
          <SidebarTrigger />
          <div className="flex flex-1 items-center justify-between">
            <h1 className="text-lg font-semibold">{title}</h1>
            <div className="flex items-center gap-2">
              {showModelSelector && (
                <>
                  <Select
                    value={selectedModel}
                    onValueChange={(value) => setSelectedModel(value as OpenRouterModel)}
                  >
                    <SelectTrigger className="w-[250px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(modelsByCategory).map(([category, models]) => (
                        <SelectGroup key={category}>
                          <SelectLabel>{category}</SelectLabel>
                          {models.map((model) => (
                            <SelectItem key={model.value} value={model.value}>
                              {model.label}
                            </SelectItem>
                          ))}
                        </SelectGroup>
                      ))}
                    </SelectContent>
                  </Select>
                </>
              )}
              <ChatSettings settings={settings} onSettingsChange={setSettings} />
              <Button
                variant="ghost"
                size="icon"
                onClick={handleClear}
                disabled={displayMessages.length === 0}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-1 flex-col overflow-hidden">
        <ScrollArea ref={scrollAreaRef} className="flex-1 px-4">
          <div className="space-y-4 py-4">
            {displayMessages.length === 0 && (
              <div className="text-muted-foreground py-8 text-center">
                Start a conversation by typing a message below
              </div>
            )}
            {displayMessages.map((message, index) => (
              <div
                key={index}
                className={`group flex gap-3 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`flex max-w-[80%] gap-3 ${
                    message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                  }`}
                >
                  <div
                    className={`flex h-8 w-8 shrink-0 items-center justify-center rounded-full ${
                      message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'
                    }`}
                  >
                    {message.role === 'user' ? (
                      <User className="h-4 w-4" />
                    ) : (
                      <Bot className="h-4 w-4" />
                    )}
                  </div>
                  <div
                    className={`rounded-lg px-3 py-2 ${
                      message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'
                    }`}
                  >
                    {message.role === 'assistant' && message.reasoning && (
                      <ChatReasoningBox reasoning={message.reasoning} />
                    )}
                    <MarkdownRenderer
                      content={message.content}
                      className={`text-sm ${
                        message.role === 'user' ? 'prose-invert' : 'dark:prose-invert'
                      } prose-p:my-2 prose-pre:my-2 prose-ul:my-2 prose-ol:my-2 prose-headings:mt-3 prose-headings:mb-2`}
                    />
                    {message.role === 'assistant' && (
                      <ChatMessageMetadata metadata={message.metadata} />
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={`h-8 w-8 opacity-0 transition-opacity group-hover:opacity-100 ${
                      message.role === 'user' ? 'order-first' : ''
                    }`}
                    onClick={() => handleDeleteMessage(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
            {loading && (
              <div className="flex justify-start">
                <div className="flex max-w-[80%] gap-3">
                  <div className="bg-muted flex h-8 w-8 shrink-0 items-center justify-center rounded-full">
                    <Bot className="h-4 w-4" />
                  </div>
                  <div className="bg-muted rounded-lg px-3 py-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
        <form
          onSubmit={handleSubmit}
          className="bg-background sticky bottom-0 flex-shrink-0 border-t p-4"
        >
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={placeholder}
              disabled={loading}
              className="flex-1"
            />
            <Button type="submit" disabled={loading || !input.trim()}>
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
