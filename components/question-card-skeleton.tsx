import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface QuestionCardSkeletonProps {
  showNavigation?: boolean;
}

export function QuestionCardSkeleton({ showNavigation = false }: QuestionCardSkeletonProps = {}) {
  const cardContent = (
    <Card className="overflow-hidden">
      <CardHeader className="border-b px-6 py-3">
        <div className="flex items-center justify-between">
          <Skeleton className="h-12 w-3/4" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 w-10 rounded-lg" />
            <Skeleton className="h-10 w-10 rounded-lg" />
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-6">
        <div className="space-y-6">
          {/* Question text area - single block */}
          <Skeleton className="h-24 w-full" />

          {/* Options - simple blocks */}
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={i} className="h-12 w-full" />
            ))}
          </div>

          {/* Button */}
          <Skeleton className="h-10 w-full" />
        </div>
      </CardContent>
    </Card>
  );

  if (showNavigation) {
    return (
      <>
        {/* Navigation header */}
        <div className="mb-4 flex items-center justify-between">
          <Skeleton className="h-9 w-24" />
          <Skeleton className="h-4 w-12" />
          <Skeleton className="h-9 w-24" />
        </div>
        {cardContent}
      </>
    );
  }

  return cardContent;
}

// For list views where multiple cards are shown
export function QuestionListSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="space-y-6">
      {Array.from({ length: count }).map((_, i) => (
        <QuestionCardSkeleton key={i} />
      ))}
    </div>
  );
}
