'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  MessageSquare,
  ChevronLeft,
  ChevronRight,
  BarChart3,
  ArrowUp,
  ChevronDown,
  Settings,
  Copy,
  Edit,
  Trash2,
  GraduationCap,
  Briefcase,
  BookOpen,
  Sparkles,
  FileText,
} from 'lucide-react';
import { CommentSection, type Comment } from './comment-section';
import { getRenderedContent } from '@/lib/render-content';
import { useCommentCounts } from '@/contexts/comment-count-context';
import { AnimatedBadge } from './animated-badge';
import { getRenderedContentWithReferences } from '@/lib/render-content';
import { QuestionAIAssistantSimple } from './question-ai-assistant-simple';
import { questionToMarkdown, copyToClipboard } from '@/lib/utils/question-to-markdown';
import { toast } from 'sonner';
import { ModelConfig } from '@/lib/openrouter/client';
import { useSidebarAwarePositioning } from '@/hooks/use-sidebar-aware-positioning';

interface QuestionCardProps {
  questionNumber: number;
  totalQuestions: number;
  // Exam information
  examBoard?: string;
  examBoardId?: string;
  institution?: string;
  institutionId?: string;
  year?: number;
  position?: string;
  positionId?: string;
  specialization?: string;
  specializationId?: string;
  // Subject and topic
  subject: string;
  subjectId?: string;
  topic: string;
  topicId?: string;
  questionText: string;
  associatedText?: string | null;
  associatedTextReferences?: Array<{ snippet: string; location_pointer: string }> | null;
  options: {
    letter: string;
    text: string;
    commentary?: string;
  }[];
  images?: {
    url: string;
    altText: string;
    order: number;
  }[];
  correctAnswer?: string;
  onAnswer?: (answer: string) => void;
  onReview?: (rating: number) => void;
  onPrevious?: () => void;
  onNext?: () => void;
  onBeforeSubmit?: (selectedAnswer: string) => Promise<void>;
  comments?: Comment[];
  commentCount?: number;
  onCommentVote?: (commentId: string, voteType: 'up' | 'down') => void;
  onCommentReply?: (parentId: string, content: string) => void;
  onNewComment?: (content: string) => void;
  onCommentDelete?: (commentId: string) => void;
  onCommentEdit?: (commentId: string, content: string) => void;
  currentUserId?: string;
  onCommentsToggle?: (show: boolean) => void;
  fsrsIntervals?: {
    rating: number;
    label: string;
    interval: string;
    emoji: string;
  }[];
  hideNavigation?: boolean; // Hide navigation buttons for list view
  // Admin props
  userRole?: string;
  questionId?: string;
  onAdminCopyJSON?: (questionId: string) => void;
  onAdminCopyMarkdown?: (questionId: string) => void;
  onAdminEdit?: (questionId: string) => void;
  onAdminDelete?: (questionId: string) => void;
  // AI Assistant props
  availableModels?: ModelConfig[];
  showAIAssistant?: boolean;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  reasoning?: {
    enabled?: boolean;
    effort?: 'low' | 'medium' | 'high';
    maxTokens?: number;
    exclude?: boolean;
  };
}

export function QuestionCard({
  questionNumber,
  totalQuestions,
  examBoard,
  examBoardId,
  institution,
  institutionId,
  year,
  position,
  positionId,
  specialization,
  specializationId,
  subject,
  subjectId,
  topic,
  topicId,
  questionText,
  associatedText,
  associatedTextReferences,
  options,
  correctAnswer,
  onAnswer,
  onReview,
  onPrevious,
  onNext,
  onBeforeSubmit,
  comments = [],
  commentCount,
  onCommentVote,
  onCommentReply,
  onNewComment,
  onCommentDelete,
  onCommentEdit,
  currentUserId,
  onCommentsToggle,
  fsrsIntervals,
  hideNavigation = false,
  // Admin props
  userRole,
  questionId,
  onAdminCopyJSON,
  onAdminCopyMarkdown,
  onAdminEdit,
  onAdminDelete,
  // AI Assistant props
  availableModels = [],
  showAIAssistant = false,
  maxTokens,
  temperature,
  topP,
  frequencyPenalty,
  presencePenalty,
  reasoning,
}: QuestionCardProps) {
  const router = useRouter();
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [eliminatedOptions, setEliminatedOptions] = useState<Set<string>>(new Set());
  const [hasAnswered, setHasAnswered] = useState(false);
  const [expandedOptions, setExpandedOptions] = useState<Set<string>>(new Set());
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [showComments, setShowComments] = useState(false);
  const [showStatistics, setShowStatistics] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [showAssociatedText, setShowAssociatedText] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAdminMenu, setShowAdminMenu] = useState(false);
  const [showAI, setShowAI] = useState(false);

  // Get sidebar-aware positioning
  const { sidebarOffset, contentWidth } = useSidebarAwarePositioning();

  // Get real-time comment count from context
  const { getCommentCount } = useCommentCounts();

  // Use commentCount prop as the base value, with fallback to comments length
  const baseCount = commentCount ?? comments?.length ?? 0;

  // For real-time updates, check context first, then fall back to base count
  // This ensures real-time updates work while showing the correct initial count
  const displayCommentCount = questionId ? getCommentCount(questionId, baseCount) : baseCount;

  // Reset state when question changes
  useEffect(() => {
    setSelectedOption(null);
    setEliminatedOptions(new Set());
    setHasAnswered(false);
    setExpandedOptions(new Set());
    setSelectedRating(null);
    setShowComments(false);
    setShowStatistics(false);
    setShowAssociatedText(true);
    setShowAI(false);
  }, [questionNumber]);

  // Auto-expand associated text if it contains images
  useEffect(() => {
    if (associatedText && associatedText.includes('<img')) {
      setShowAssociatedText(true);
    }
  }, [associatedText]);

  // Handle scroll to show/hide scroll to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // FSRS intervals - only show if provided via props
  const intervals = fsrsIntervals;

  const handleOptionClick = (letter: string) => {
    if (hasAnswered) {
      // After answering, clicking toggles the commentary expansion
      setExpandedOptions((prev) => {
        const newSet = new Set(prev);
        if (newSet.has(letter)) {
          newSet.delete(letter);
        } else {
          newSet.add(letter);
        }
        return newSet;
      });
      return;
    }
    if (selectedOption === letter) {
      // If clicking the selected option, toggle elimination
      setEliminatedOptions((prev) => {
        const newSet = new Set(prev);
        if (newSet.has(letter)) {
          newSet.delete(letter);
        } else {
          newSet.add(letter);
        }
        return newSet;
      });
    } else {
      // If clicking a different option, select it and remove from eliminated
      setSelectedOption(letter);
      setEliminatedOptions((prev) => {
        const newSet = new Set(prev);
        newSet.delete(letter);
        return newSet;
      });
    }
  };

  const handleSubmit = async () => {
    if (selectedOption && !isSubmitting) {
      setIsSubmitting(true);
      try {
        // Call onBeforeSubmit if provided (e.g., to fetch intervals)
        if (onBeforeSubmit) {
          await onBeforeSubmit(selectedOption);
        }

        setHasAnswered(true);
        if (onAnswer) {
          onAnswer(selectedOption);
        }
        // Add a small delay before expanding to allow the color transition to happen first
        setTimeout(() => {
          const allLetters = options.map((opt) => opt.letter);
          setExpandedOptions(new Set(allLetters));
        }, 100);
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleReview = (rating: number) => {
    setSelectedRating(rating);
    if (onReview) {
      onReview(rating);
    }
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleAdminAction = (action: 'copy' | 'copy-markdown' | 'edit' | 'delete') => {
    setShowAdminMenu(false);

    switch (action) {
      case 'copy':
        if (questionId) {
          onAdminCopyJSON?.(questionId);
        }
        break;
      case 'copy-markdown':
        if (questionId && onAdminCopyMarkdown) {
          onAdminCopyMarkdown(questionId);
        } else {
          // Fallback to local implementation
          const markdownData = questionToMarkdown({
            examBoard,
            institution,
            year,
            position,
            specialization,
            subject,
            topic: topic || undefined,
            associatedText: associatedText || undefined,
            associatedTextReferences,
            questionText,
            options: options.map((opt) => ({
              letter: opt.letter,
              text: opt.text,
              commentary: opt.commentary,
            })),
            correctAnswer,
          });
          copyToClipboard(markdownData).then((success) => {
            if (success) {
              toast.success('Question copied as Markdown!');
            } else {
              toast.error('Failed to copy to clipboard');
            }
          });
        }
        break;
      case 'edit':
        if (questionId) {
          onAdminEdit?.(questionId);
        }
        break;
      case 'delete':
        if (questionId) {
          onAdminDelete?.(questionId);
        }
        break;
    }
  };

  // Close admin menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showAdminMenu) {
        setShowAdminMenu(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [showAdminMenu]);

  // Keyboard navigation support
  useEffect(() => {
    if (hideNavigation) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle arrow keys if no input/textarea is focused
      const activeElement = document.activeElement;
      if (
        activeElement &&
        (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')
      ) {
        return;
      }

      if (e.key === 'ArrowLeft' && onPrevious && questionNumber > 1) {
        e.preventDefault();
        onPrevious();
      } else if (e.key === 'ArrowRight' && onNext && questionNumber < totalQuestions) {
        e.preventDefault();
        onNext();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [hideNavigation, onPrevious, onNext, questionNumber, totalQuestions]);

  return (
    <div className={hideNavigation ? '' : 'mx-auto max-w-5xl p-6'}>
      {/* Main Container with integrated design */}
      <div className="bg-background border-border overflow-hidden rounded-lg border shadow-lg">
        {/* Progress Bar at the top - only show when not in compact mode */}
        {!hideNavigation && (
          <div className="bg-muted h-1">
            <div
              className="h-full bg-black/70 transition-all duration-300 ease-out dark:bg-white/70"
              style={{ width: `${(questionNumber / totalQuestions) * 100}%` }}
            />
          </div>
        )}

        {/* Header - Compact Design from Option 1 */}
        <div className="border-border border-b">
          <div className="flex items-center justify-between p-2.5 sm:p-3">
            <div className="flex-1 space-y-1">
              {/* All information in compact rows with clickable elements */}
              <div className="flex items-center gap-1.5 text-[11px] sm:text-xs">
                <GraduationCap className="text-primary h-3 w-3" />
                <span className="text-foreground/85">
                  {examBoardId ? (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/exam-boards/${examBoardId}`);
                      }}
                      className="font-medium hover:underline"
                    >
                      {examBoard}
                    </button>
                  ) : (
                    <span className="font-medium">{examBoard}</span>
                  )}
                  {' • '}
                  {institutionId ? (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/institutions/${institutionId}`);
                      }}
                      className="hover:underline"
                    >
                      {institution}
                    </button>
                  ) : (
                    <span>{institution}</span>
                  )}
                  {' • '}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      router.push(`/years/${year}`);
                    }}
                    className="hover:underline"
                  >
                    {year}
                  </button>
                </span>
              </div>

              {(position || specialization) && (
                <div className="flex items-center gap-1.5 text-[11px] sm:text-xs">
                  <Briefcase className="text-secondary-foreground h-3 w-3" />
                  <span className="text-foreground/85">
                    {positionId ? (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/positions/${positionId}`);
                        }}
                        className="hover:underline"
                      >
                        {position}
                      </button>
                    ) : (
                      <span>{position}</span>
                    )}
                    {specialization && specialization !== position && (
                      <>
                        {' • '}
                        {specializationId ? (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(`/specializations/${specializationId}`);
                            }}
                            className="hover:underline"
                          >
                            {specialization}
                          </button>
                        ) : (
                          <span>{specialization}</span>
                        )}
                      </>
                    )}
                  </span>
                </div>
              )}

              <div className="flex items-center gap-1.5 text-[11px] sm:text-xs">
                <BookOpen className="h-3 w-3 text-green-600 dark:text-green-500" />
                <span className="text-foreground/85">
                  {subjectId ? (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/subjects/${subjectId}`);
                      }}
                      className="font-medium hover:underline"
                    >
                      {subject}
                    </button>
                  ) : (
                    <span className="font-medium">{subject}</span>
                  )}
                  {' • '}
                  {topicId ? (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/topics/${topicId}`);
                      }}
                      className="text-muted-foreground hover:underline"
                    >
                      {topic || 'Sem Classificação'}
                    </button>
                  ) : (
                    <span className="text-muted-foreground">{topic || 'Sem Classificação'}</span>
                  )}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <button
                onClick={() => {
                  const newState = !showStatistics;

                  if (newState) {
                    // Close others and open statistics simultaneously
                    setShowComments(false);
                    setShowAI(false);
                    setShowStatistics(true);

                    // Smooth scroll after a brief delay
                    setTimeout(() => {
                      const element = document.getElementById('statistics-section');
                      if (element) {
                        const yOffset = -20;
                        const y =
                          element.getBoundingClientRect().top + window.pageYOffset + yOffset;
                        window.scrollTo({ top: y, behavior: 'smooth' });
                      }
                    }, 100);
                  } else {
                    setShowStatistics(false);
                  }
                }}
                className={`hover:bg-accent rounded-md p-1.5 transition-all duration-200 sm:p-2 ${
                  showStatistics ? 'bg-accent' : ''
                }`}
                title="Estatísticas"
              >
                <BarChart3
                  className={`h-4 w-4 transition-all duration-200 sm:h-5 sm:w-5 ${
                    showStatistics ? 'text-foreground scale-110' : 'text-muted-foreground scale-100'
                  }`}
                />
              </button>
              <button
                onClick={() => {
                  const newState = !showComments;

                  if (newState) {
                    // Close others and open comments simultaneously
                    setShowStatistics(false);
                    setShowAI(false);
                    setShowComments(true);

                    // Notify parent component about comments toggle
                    if (onCommentsToggle) {
                      onCommentsToggle(true);
                    }

                    // Smooth scroll after a brief delay
                    setTimeout(() => {
                      const element = document.getElementById('comments-section');
                      if (element) {
                        const yOffset = -20;
                        const y =
                          element.getBoundingClientRect().top + window.pageYOffset + yOffset;
                        window.scrollTo({ top: y, behavior: 'smooth' });
                      }
                    }, 100);
                  } else {
                    setShowComments(false);

                    // Notify parent component about comments toggle
                    if (onCommentsToggle) {
                      onCommentsToggle(false);
                    }
                  }
                }}
                className={`hover:bg-accent relative rounded-md p-1.5 transition-all duration-200 sm:p-2 ${
                  showComments ? 'bg-accent' : ''
                }`}
                title="Discussão"
              >
                <MessageSquare
                  className={`h-4 w-4 transition-all duration-200 sm:h-5 sm:w-5 ${
                    showComments ? 'text-foreground scale-110' : 'text-muted-foreground scale-100'
                  }`}
                />
                {/* Notification Badge */}
                {displayCommentCount > 0 && <AnimatedBadge value={displayCommentCount} />}
              </button>

              {/* AI Assistant Button */}
              {showAIAssistant && availableModels.length > 0 && (
                <button
                  onClick={() => {
                    const newState = !showAI;

                    if (newState) {
                      // Close others and open AI simultaneously
                      setShowStatistics(false);
                      setShowComments(false);
                      setShowAI(true);

                      // Smooth scroll after a brief delay
                      setTimeout(() => {
                        const element = document.getElementById('ai-assistant-section');
                        if (element) {
                          const yOffset = -20;
                          const y =
                            element.getBoundingClientRect().top + window.pageYOffset + yOffset;
                          window.scrollTo({ top: y, behavior: 'smooth' });
                        }
                      }, 100);
                    } else {
                      setShowAI(false);
                    }
                  }}
                  className={`hover:bg-accent relative rounded-md p-1.5 transition-all duration-200 sm:p-2 ${
                    showAI ? 'bg-accent' : ''
                  }`}
                  title="AI Study Assistant"
                >
                  <Sparkles
                    className={`h-4 w-4 transition-all duration-200 sm:h-5 sm:w-5 ${
                      showAI ? 'text-foreground scale-110' : 'text-muted-foreground scale-100'
                    }`}
                  />
                </button>
              )}

              {/* Admin Button */}
              {userRole === 'admin' && questionId && (
                <div className="relative">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowAdminMenu(!showAdminMenu);
                    }}
                    className={`hover:bg-accent rounded-md p-1.5 transition-all duration-200 sm:p-2 ${
                      showAdminMenu ? 'bg-accent' : ''
                    }`}
                    title="Administrar Questão"
                  >
                    <Settings
                      className={`h-4 w-4 transition-all duration-200 sm:h-5 sm:w-5 ${
                        showAdminMenu
                          ? 'text-foreground scale-110'
                          : 'text-muted-foreground scale-100'
                      }`}
                    />
                  </button>

                  {/* Admin Dropdown Menu */}
                  {showAdminMenu && (
                    <div className="border-border bg-popover absolute top-full right-0 z-50 mt-2 w-48 rounded-lg border shadow-lg">
                      <div className="py-1">
                        <button
                          onClick={() => handleAdminAction('copy')}
                          className="text-popover-foreground hover:bg-accent hover:text-accent-foreground flex w-full items-center gap-2 px-4 py-2 text-left text-sm"
                        >
                          <Copy className="h-4 w-4" />
                          Copiar JSON
                        </button>
                        <button
                          onClick={() => handleAdminAction('copy-markdown')}
                          className="text-popover-foreground hover:bg-accent hover:text-accent-foreground flex w-full items-center gap-2 px-4 py-2 text-left text-sm"
                        >
                          <FileText className="h-4 w-4" />
                          Copiar Markdown
                        </button>
                        <button
                          onClick={() => handleAdminAction('edit')}
                          className="text-popover-foreground hover:bg-accent hover:text-accent-foreground flex w-full items-center gap-2 px-4 py-2 text-left text-sm"
                        >
                          <Edit className="h-4 w-4" />
                          Editar Questão
                        </button>
                        <button
                          onClick={() => handleAdminAction('delete')}
                          className="text-destructive hover:bg-destructive/10 hover:text-destructive flex w-full items-center gap-2 px-4 py-2 text-left text-sm"
                        >
                          <Trash2 className="h-4 w-4" />
                          Deletar Questão
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Components Container with relative positioning for overlay effect */}
        <div className="relative">
          {/* Integrated Statistics Section with smooth animation */}
          <div
            id="statistics-section"
            className={`transition-all duration-300 ease-out ${
              showStatistics
                ? 'relative translate-y-0 opacity-100'
                : 'pointer-events-none absolute inset-x-0 top-0 -translate-y-8 opacity-0'
            }`}
          >
            <div className="border-border border-t p-4">
              <h3 className="text-foreground mb-3 text-sm font-semibold">
                Estatísticas da Questão
              </h3>

              {/* Mock statistics - replace with real data */}
              <div className="mb-4 grid grid-cols-2 gap-4 md:grid-cols-4">
                <div className="text-center">
                  <div className="text-foreground text-2xl font-bold">68%</div>
                  <div className="text-muted-foreground text-xs">Taxa de Acerto</div>
                </div>
                <div className="text-center">
                  <div className="text-foreground text-2xl font-bold">3,542</div>
                  <div className="text-muted-foreground text-xs">Respondidas</div>
                </div>
                <div className="text-center">
                  <div className="text-foreground text-2xl font-bold">2.8</div>
                  <div className="text-muted-foreground text-xs">Dificuldade Média</div>
                </div>
                <div className="text-center">
                  <div className="text-foreground text-2xl font-bold">45s</div>
                  <div className="text-muted-foreground text-xs">Tempo Médio</div>
                </div>
              </div>

              {/* Answer distribution */}
              <div className="space-y-2">
                <h4 className="text-muted-foreground mb-2 text-xs font-medium">
                  Distribuição de Respostas
                </h4>
                {options.map((option, index) => {
                  // Mock percentages - replace with real data
                  const percentages = [15, 8, 5, 4, 68];
                  const percentage = percentages[index] || 0;
                  const isCorrectOption = option.letter === correctAnswer;

                  return (
                    <div key={option.letter} className="flex items-center gap-2">
                      <span
                        className={`w-4 text-xs font-medium ${
                          isCorrectOption
                            ? 'text-green-600 dark:text-green-400'
                            : 'text-muted-foreground'
                        }`}
                      >
                        {option.letter}
                      </span>
                      <div className="bg-muted h-4 flex-1 overflow-hidden rounded-full">
                        <div
                          className={`h-full transition-all duration-500 ease-out ${
                            isCorrectOption
                              ? 'bg-green-500 dark:bg-green-600'
                              : 'bg-muted-foreground/30'
                          }`}
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="text-muted-foreground w-10 text-right text-xs">
                        {percentage}%
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Integrated Comment Section with smooth animation */}
          <div
            id="comments-section"
            className={`transition-all duration-300 ease-out ${
              showComments
                ? 'relative translate-y-0 opacity-100'
                : 'pointer-events-none absolute inset-x-0 top-0 -translate-y-8 opacity-0'
            }`}
          >
            <div className="border-border border-t">
              <CommentSection
                comments={comments}
                onVote={onCommentVote}
                onReply={onCommentReply}
                onNewComment={onNewComment}
                onDelete={onCommentDelete}
                onEdit={onCommentEdit}
                currentUserId={currentUserId}
                layout="top"
                className="rounded-none border-0 shadow-none"
              />
            </div>
          </div>

          {/* Integrated AI Assistant Section with smooth animation */}
          <div
            id="ai-assistant-section"
            className={`transition-all duration-300 ease-out ${
              showAI
                ? 'relative translate-y-0 opacity-100'
                : 'pointer-events-none absolute inset-x-0 top-0 -translate-y-8 opacity-0'
            }`}
          >
            {showAI && (
              <div className="border-t">
                <QuestionAIAssistantSimple
                  questionData={{
                    examBoard,
                    institution,
                    year,
                    position,
                    specialization,
                    subject,
                    topic: topic || undefined,
                    associatedText: associatedText || undefined,
                    associatedTextReferences,
                    questionText,
                    options: options.map((opt) => ({
                      letter: opt.letter,
                      text: opt.text,
                      commentary: opt.commentary,
                    })),
                    correctAnswer,
                  }}
                  availableModels={availableModels}
                  onClose={() => setShowAI(false)}
                  maxTokens={maxTokens}
                  temperature={temperature}
                  topP={topP}
                  frequencyPenalty={frequencyPenalty}
                  presencePenalty={presencePenalty}
                  reasoning={reasoning}
                />
              </div>
            )}
          </div>
        </div>

        {/* Question Content */}
        <div
          className={`p-4 ${showStatistics || showComments || showAI ? 'border-border border-t' : ''}`}
        >
          <div className="space-y-4">
            {associatedText && (
              <div className="bg-muted overflow-hidden rounded-md">
                <button
                  onClick={() => setShowAssociatedText(!showAssociatedText)}
                  className="hover:bg-muted/80 flex w-full items-center justify-between p-3 transition-colors duration-200"
                >
                  <span className="text-xs font-medium">Texto de Apoio</span>
                  <ChevronDown
                    className={`h-4 w-4 transition-transform duration-200 ${
                      showAssociatedText ? 'rotate-180' : ''
                    }`}
                  />
                </button>
                <div
                  className={`overflow-hidden transition-[max-height] ${
                    showAssociatedText ? 'max-h-[2000px]' : 'max-h-0'
                  }`}
                  style={{
                    transitionDuration: '400ms',
                    transitionTimingFunction: 'ease-in-out',
                  }}
                >
                  <div className="px-3 pb-3">
                    <p
                      className="text-foreground/90 text-justify text-base leading-relaxed"
                      {...getRenderedContentWithReferences(
                        associatedText,
                        associatedTextReferences
                      )}
                    />
                  </div>
                </div>
              </div>
            )}
            <p
              className="text-foreground/90 text-justify leading-relaxed"
              {...getRenderedContent(questionText)}
            />

            {/* Options */}
            <div className="space-y-1">
              {options.map((option) => {
                const isCorrect = hasAnswered && correctAnswer === option.letter;
                const isWrong =
                  hasAnswered &&
                  selectedOption === option.letter &&
                  correctAnswer !== option.letter;

                return (
                  <button
                    key={option.letter}
                    onClick={() => handleOptionClick(option.letter)}
                    className={`w-full overflow-hidden rounded-lg text-left transition-all ${
                      hasAnswered
                        ? isCorrect
                          ? 'dark:bg-background dark:hover:bg-accent cursor-pointer bg-green-100 hover:bg-green-200 dark:bg-green-900/20 dark:hover:bg-green-900/30'
                          : isWrong
                            ? 'dark:bg-background dark:hover:bg-accent cursor-pointer bg-red-100 hover:bg-red-200 dark:bg-red-900/20 dark:hover:bg-red-900/30'
                            : 'bg-background dark:bg-background hover:bg-accent cursor-pointer'
                        : 'bg-background dark:bg-background hover:bg-accent cursor-pointer'
                    }`}
                  >
                    <div className="p-2">
                      <div className="flex items-center gap-3">
                        <div
                          className={`flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full font-medium transition-colors ${
                            hasAnswered
                              ? isCorrect
                                ? 'bg-green-200 text-green-700 dark:bg-green-800 dark:text-green-300'
                                : isWrong
                                  ? 'bg-red-200 text-red-700 dark:bg-red-800 dark:text-red-300'
                                  : 'bg-muted-foreground/10 text-muted-foreground'
                              : eliminatedOptions.has(option.letter)
                                ? 'bg-muted-foreground/10 text-muted-foreground opacity-50'
                                : selectedOption === option.letter
                                  ? 'bg-black/70 text-white dark:bg-white/70 dark:text-black'
                                  : 'bg-muted-foreground/10 text-muted-foreground'
                          }`}
                        >
                          {option.letter}
                        </div>
                        <span
                          className={`text-foreground/90 flex-1 text-justify transition-all ${
                            eliminatedOptions.has(option.letter) && !hasAnswered
                              ? 'line-through opacity-50'
                              : ''
                          }`}
                          {...getRenderedContent(option.text)}
                        />
                      </div>

                      {/* Commentary Box inside the option */}
                      {hasAnswered && option.commentary && (
                        <div
                          className={`overflow-hidden transition-all duration-400 ${
                            expandedOptions.has(option.letter) ? 'max-h-32' : 'max-h-0'
                          }`}
                        >
                          <div className="border-border/50 mt-1.5 border-t pt-1.5">
                            <p
                              className="text-foreground/70 text-justify text-sm leading-relaxed"
                              {...getRenderedContent(option.commentary || '')}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-4 pb-4">
          {!hasAnswered ? (
            <button
              onClick={handleSubmit}
              disabled={!selectedOption || isSubmitting}
              className={`w-full rounded-lg px-6 py-2.5 font-medium transition-all ${
                selectedOption && !isSubmitting
                  ? 'bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 dark:hover:bg-green-900/30'
                  : 'bg-muted text-muted-foreground cursor-not-allowed'
              }`}
            >
              {isSubmitting ? 'Carregando...' : 'RESOLVER QUESTÃO'}
            </button>
          ) : (
            <div className="space-y-2">
              {intervals && intervals.length > 0 && (
                <div className="grid grid-cols-4 gap-2">
                  {intervals.map((interval) => (
                    <button
                      key={interval.rating}
                      onClick={() => handleReview(interval.rating)}
                      disabled={selectedRating !== null}
                      className={`flex flex-col items-center rounded-lg px-2 py-2 font-medium transition-all ${
                        selectedRating === interval.rating
                          ? interval.rating === 1
                            ? 'bg-red-200 text-red-800 dark:bg-red-800 dark:text-red-200'
                            : interval.rating === 2
                              ? 'bg-orange-200 text-orange-800 dark:bg-orange-800 dark:text-orange-200'
                              : interval.rating === 3
                                ? 'bg-blue-200 text-blue-800 dark:bg-blue-800 dark:text-blue-200'
                                : 'bg-green-200 text-green-800 dark:bg-green-600 dark:text-green-100'
                          : selectedRating !== null
                            ? 'bg-background dark:bg-background cursor-not-allowed text-gray-400 opacity-50 dark:text-gray-500'
                            : interval.rating === 1
                              ? 'bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/30'
                              : interval.rating === 2
                                ? 'bg-orange-100 text-orange-700 hover:bg-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:hover:bg-orange-900/30'
                                : interval.rating === 3
                                  ? 'bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30'
                                  : 'bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 dark:hover:bg-green-900/30'
                      }`}
                    >
                      <span className="text-sm opacity-80">{interval.interval}</span>
                      <span className="text-2xl">{interval.emoji}</span>
                      <span className="text-sm font-bold">{interval.label}</span>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Floating Scroll to Top Button */}
      <button
        onClick={scrollToTop}
        className={`bg-background border-border hover:bg-accent fixed right-6 bottom-6 rounded-full border p-3 shadow-lg transition-all duration-300 ${
          showScrollTop
            ? 'translate-y-0 opacity-100'
            : 'pointer-events-none translate-y-10 opacity-0'
        }`}
        aria-label="Voltar ao topo"
      >
        <ArrowUp className="text-foreground h-5 w-5" />
      </button>

      {/* Floating Navigation Buttons - only show if not hidden */}
      {!hideNavigation && (
        <>
          {/* Floating Navigation Container - positioned relative to content area */}
          <div 
            className="pointer-events-none fixed bottom-6 z-20"
            style={{
              left: `${sidebarOffset}px`,
              width: contentWidth ? `${contentWidth}px` : '100%',
              transition: 'left 0.3s ease-out, width 0.3s ease-out'
            }}
          >
            <div className="mx-auto max-w-5xl px-6">
              <div className="flex items-center justify-between">
                {/* Previous Button */}
                <button
                  onClick={onPrevious}
                  disabled={questionNumber === 1 || !onPrevious}
                  className={`bg-background border-border pointer-events-auto rounded-full border p-3 shadow-lg transition-all duration-300 ${
                    questionNumber === 1 || !onPrevious
                      ? 'cursor-not-allowed opacity-50'
                      : 'hover:bg-accent hover:scale-110'
                  }`}
                  aria-label="Questão anterior"
                >
                  <ChevronLeft className="text-foreground h-5 w-5" />
                </button>

                {/* Progress Indicator */}
                <div className="bg-background/90 border-border text-muted-foreground pointer-events-auto rounded-full border px-4 py-2 text-sm shadow-lg backdrop-blur-sm">
                  {questionNumber} de {totalQuestions}
                </div>

                {/* Next Button */}
                <button
                  onClick={onNext}
                  disabled={questionNumber === totalQuestions || !onNext}
                  className={`bg-background border-border pointer-events-auto rounded-full border p-3 shadow-lg transition-all duration-300 ${
                    questionNumber === totalQuestions || !onNext
                      ? 'cursor-not-allowed opacity-50'
                      : 'hover:bg-accent hover:scale-110'
                  }`}
                  aria-label="Próxima questão"
                >
                  <ChevronRight className="text-foreground h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
