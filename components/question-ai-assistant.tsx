'use client';

import React, { useState, useEffect } from 'react';
import { ChatBot } from '@/components/chat-bot';
import { Button } from '@/components/ui/button';
import { questionToMarkdown } from '@/lib/utils/question-to-markdown';
import { Brain, Lightbulb, Map, HelpCircle, BookOpen, Globe, Sparkles, X } from 'lucide-react';
import { ModelConfig } from '@/lib/openrouter/client';

interface QuestionAIAssistantProps {
  questionData: {
    examBoard?: string;
    institution?: string;
    year?: number;
    position?: string;
    specialization?: string;
    subject: string;
    topic?: string;
    associatedText?: string;
    associatedTextReferences?: Array<{ snippet: string; location_pointer: string }> | null;
    questionText: string;
    options: Array<{ letter: string; text: string; commentary?: string }>;
    correctAnswer?: string;
  };
  availableModels?: ModelConfig[];
  onClose?: () => void;
}

interface PromptSuggestion {
  id: string;
  icon: React.ElementType;
  label: string;
  prompt: string;
}

const PROMPT_SUGGESTIONS: PromptSuggestion[] = [
  {
    id: 'explain',
    icon: Lightbulb,
    label: 'Explain this question',
    prompt:
      'Please explain this question in detail, breaking down the key concepts and why the correct answer is right.',
  },
  {
    id: 'mnemonics',
    icon: Brain,
    label: 'Create mnemonics',
    prompt:
      'Create memorable mnemonics or memory aids to help me remember the key concepts and correct answer for this question.',
  },
  {
    id: 'mindmap',
    icon: Map,
    label: 'Generate mind map',
    prompt:
      'Create a text-based mind map showing the relationships between the concepts in this question. Use indentation and symbols to show hierarchy.',
  },
  {
    id: 'why-wrong',
    icon: HelpCircle,
    label: 'Why others are wrong',
    prompt:
      'Explain in detail why each of the incorrect options is wrong. What makes them plausible but ultimately incorrect?',
  },
  {
    id: 'similar',
    icon: BookOpen,
    label: 'Similar questions',
    prompt:
      'Generate 3 similar practice questions on the same topic with the same difficulty level. Include the correct answers.',
  },
  {
    id: 'real-world',
    icon: Globe,
    label: 'Real-world applications',
    prompt:
      'How do the concepts in this question apply to real-world situations? Give practical examples and applications.',
  },
];

export function QuestionAIAssistant({
  questionData,
  availableModels = [],
  onClose,
}: QuestionAIAssistantProps) {
  const [selectedPrompt, setSelectedPrompt] = useState<string | null>(null);
  const [questionContext, setQuestionContext] = useState<string>('');

  useEffect(() => {
    // Generate markdown context when component mounts
    const markdown = questionToMarkdown(questionData);
    setQuestionContext(markdown);
  }, [questionData]);

  const handlePromptSelect = (suggestion: PromptSuggestion) => {
    setSelectedPrompt(suggestion.prompt);
  };

  const handleMessageSent = () => {
    // If this is the first message and no prompt was selected, clear the selected prompt
    if (selectedPrompt) {
      setSelectedPrompt(null);
    }
  };

  // System prompt for educational assistance
  const systemPrompt = `You are an expert educational AI assistant helping students understand exam questions. 

Here is the question context:

${questionContext}

Please provide helpful, accurate, and educational responses. When explaining concepts:
- Be clear and concise
- Use examples when helpful
- Break down complex ideas into simpler parts
- Encourage critical thinking
- Focus on understanding rather than memorization

Always maintain a supportive and encouraging tone.`;

  return (
    <div className="border-border border-t">
      <div className="border-border flex items-center justify-between border-b px-4 py-2">
        <div className="flex items-center gap-2">
          <Sparkles className="text-primary h-4 w-4" />
          <h3 className="text-sm font-semibold">AI Study Assistant</h3>
        </div>
        {onClose && (
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Prompt Suggestions */}
      <div className="border-border border-b p-4">
        <p className="text-muted-foreground mb-3 text-sm">
          Choose a study aid or ask your own question:
        </p>
        <div className="grid grid-cols-2 gap-2 sm:grid-cols-3">
          {PROMPT_SUGGESTIONS.map((suggestion) => {
            const Icon = suggestion.icon;
            return (
              <Button
                key={suggestion.id}
                variant="outline"
                size="sm"
                className="justify-start gap-2 text-xs"
                onClick={() => handlePromptSelect(suggestion)}
              >
                <Icon className="h-3.5 w-3.5" />
                <span className="truncate">{suggestion.label}</span>
              </Button>
            );
          })}
        </div>
      </div>

      {/* Chat Interface */}
      <div className="h-[400px]">
        <ChatBot
          systemPrompt={systemPrompt}
          placeholder="Ask me anything about this question..."
          title=""
          showModelSelector={availableModels.length > 1}
          availableModels={availableModels}
          onMessageSent={handleMessageSent}
          initialMessages={
            selectedPrompt
              ? [
                  { role: 'system', content: systemPrompt },
                  { role: 'user', content: selectedPrompt },
                ]
              : []
          }
          defaultModel={availableModels.find((m) => m.enabled)?.value || 'openai/gpt-4o-mini'}
        />
      </div>
    </div>
  );
}
