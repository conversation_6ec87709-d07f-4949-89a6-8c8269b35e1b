'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { questionToMarkdown } from '@/lib/utils/question-to-markdown';
import {
  Brain,
  Lightbulb,
  Map,
  HelpCircle,
  BookOpen,
  Globe,
  Sparkles,
  Send,
  Loader2,
  Bot,
  User,
  ChevronLeft,
  ChevronRight,
  Cpu,
} from 'lucide-react';
import { ModelConfig } from '@/lib/openrouter/client';
import { useOpenRouter } from '@/hooks/use-openrouter';
import { MarkdownRenderer } from './markdown-renderer';
import { ChatMessageMetadata } from './chat-message-metadata';
import { ChatReasoningBox } from './chat-reasoning-box';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectGroup,
  SelectLabel,
} from '@/components/ui/select';

interface PromptSuggestion {
  id: string;
  icon: string;
  label: string;
  prompt: string;
}

// Icon mapping
const ICON_MAP: Record<string, React.ElementType> = {
  Brain,
  Lightbulb,
  Map,
  HelpCircle,
  BookOpen,
  Globe,
  Sparkles,
};

interface QuestionAIAssistantSimpleProps {
  questionData: {
    examBoard?: string;
    institution?: string;
    year?: number;
    position?: string;
    specialization?: string;
    subject: string;
    topic?: string;
    associatedText?: string;
    associatedTextReferences?: Array<{ snippet: string; location_pointer: string }> | null;
    questionText: string;
    options: Array<{ letter: string; text: string; commentary?: string }>;
    correctAnswer?: string;
  };
  availableModels?: ModelConfig[];
  defaultModel?: string;
  onClose?: () => void;
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  reasoning?: {
    enabled?: boolean;
    effort?: 'low' | 'medium' | 'high';
    maxTokens?: number;
    exclude?: boolean;
  };
}

export function QuestionAIAssistantSimple({
  questionData,
  availableModels = [],
  defaultModel = 'google/gemini-2.5-flash-lite',
  maxTokens,
  temperature,
  topP,
  frequencyPenalty,
  presencePenalty,
  reasoning,
}: QuestionAIAssistantSimpleProps) {
  const [input, setInput] = useState('');
  const [selectedModel, setSelectedModel] = useState(defaultModel);
  const [selectedPrompt, setSelectedPrompt] = useState<string | null>(null);
  const [prompts, setPrompts] = useState<PromptSuggestion[]>([]);
  const [loadingPrompts, setLoadingPrompts] = useState(true);
  const [showLeftScroll, setShowLeftScroll] = useState(false);
  const [showRightScroll, setShowRightScroll] = useState(false);
  const [showModelDropdown, setShowModelDropdown] = useState(false);
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);

  // Only show enabled models
  const enabledModels = availableModels.filter((m) => m.enabled);

  // Check if scroll buttons should be shown
  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setShowLeftScroll(scrollLeft > 0);
      setShowRightScroll(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  // Load prompts from database
  useEffect(() => {
    const loadPrompts = async () => {
      try {
        const response = await fetch('/api/prompts/ai-study-assistant');
        if (response.ok) {
          const data = await response.json();
          if (data.prompts && data.prompts.length > 0) {
            setPrompts(data.prompts);
          }
        }
      } catch (error) {
        console.error('Failed to load prompts:', error);
      } finally {
        setLoadingPrompts(false);
      }
    };

    loadPrompts();
  }, []);

  // Check scroll buttons when prompts change or window resizes
  useEffect(() => {
    checkScrollButtons();
    window.addEventListener('resize', checkScrollButtons);
    return () => window.removeEventListener('resize', checkScrollButtons);
  }, [prompts]);

  // System prompt for educational assistance
  const systemPrompt = `You are an expert educational AI assistant helping Brazilian students understand exam questions. 
The student is studying for ${questionData.examBoard || 'an exam'} - ${questionData.position || 'position'} (${questionData.year || 'year'}).

Question Context:
${questionToMarkdown(questionData)}

IMPORTANT: Always respond in Brazilian Portuguese (pt-BR).

Instructions:
- SEMPRE responda em português brasileiro
- Be supportive and encouraging (seja solidário e encorajador)
- Use clear, simple language (use linguagem clara e simples)
- Provide step-by-step explanations when needed (forneça explicações passo a passo quando necessário)
- Focus on helping the student learn and understand (foque em ajudar o aluno a aprender e entender)
- Use examples and analogies when helpful (use exemplos e analogias quando útil)
- If discussing incorrect options, explain why they're wrong (ao discutir opções incorretas, explique por que estão erradas)
- Help build conceptual understanding, not just memorization (ajude a construir compreensão conceitual, não apenas memorização)
- Use Brazilian Portuguese terminology and expressions
- Adapt explanations to Brazilian educational context when relevant`;

  const { messages, streamMessage, loading } = useOpenRouter({
    model: selectedModel,
    initialMessages: [{ role: 'system', content: systemPrompt }],
    maxTokens: maxTokens,
    temperature: temperature,
    topP: topP,
    frequencyPenalty: frequencyPenalty,
    presencePenalty: presencePenalty,
    reasoning: reasoning,
  });

  // Auto-send selected prompt
  useEffect(() => {
    if (selectedPrompt) {
      streamMessage(selectedPrompt);
      setSelectedPrompt(null);
    }
  }, [selectedPrompt, streamMessage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || loading) return;

    const message = input.trim();
    setInput('');
    await streamMessage(message);
  };

  const handlePromptSelect = (prompt: string) => {
    setSelectedPrompt(prompt);
  };

  const scrollPrompts = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const scrollAmount = 200;
      scrollContainerRef.current.scrollBy({
        left: direction === 'left' ? -scrollAmount : scrollAmount,
        behavior: 'smooth',
      });
      // Check scroll buttons after scrolling
      setTimeout(checkScrollButtons, 300);
    }
  };

  // Filter out system messages from display
  const displayMessages = messages.filter((m) => m.role !== 'system');

  // Group enabled models by category
  const modelsByCategory = enabledModels.reduce(
    (acc, model) => {
      const category = model.category || 'Other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(model);
      return acc;
    },
    {} as Record<string, ModelConfig[]>
  );

  return (
    <div className="bg-background flex h-[800px] max-h-[90vh] flex-col">
      {/* Prompt Suggestions - Horizontal Scrollable */}
      <div className="border-border flex-shrink-0 border-b px-4 py-1.5">
        {/* <p className="text-muted-foreground mb-2 text-xs">
          Escolha uma ajuda de estudo ou faça sua própria pergunta:
        </p> */}
        <div className="relative flex items-center">
          {/* Left scroll button */}
          {showLeftScroll && (
            <Button
              variant="ghost"
              size="icon"
              className="bg-muted/30 hover:bg-muted/50 dark:bg-muted/20 dark:hover:bg-muted/40 mr-1 h-8 w-8 flex-shrink-0 rounded-md backdrop-blur-sm"
              onClick={() => scrollPrompts('left')}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          )}

          <div
            ref={scrollContainerRef}
            className="no-scrollbar flex flex-1 gap-2 overflow-x-auto"
            onScroll={checkScrollButtons}
            style={
              {
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
              } as React.CSSProperties
            }
          >
            {loadingPrompts ? (
              <div className="text-muted-foreground flex items-center gap-2 px-3 py-1 text-xs">
                <Loader2 className="h-3.5 w-3.5 animate-spin" />
                Carregando sugestões...
              </div>
            ) : prompts.length === 0 ? (
              <div className="text-muted-foreground flex items-center gap-2 px-3 py-1 text-xs">
                Nenhuma sugestão de prompt disponível
              </div>
            ) : (
              prompts.map((suggestion) => {
                const Icon = ICON_MAP[suggestion.icon] || Sparkles;
                return (
                  <Button
                    key={suggestion.id}
                    variant="ghost"
                    size="sm"
                    className="hover:bg-muted/50 dark:hover:bg-muted/30 h-8 flex-shrink-0 gap-1.5 rounded-md px-3 py-1 whitespace-nowrap"
                    onClick={() => handlePromptSelect(suggestion.prompt)}
                    disabled={loading}
                  >
                    <Icon className="h-3.5 w-3.5 shrink-0" />
                    <span className="text-xs">{suggestion.label}</span>
                  </Button>
                );
              })
            )}
          </div>

          {/* Right scroll button */}
          {showRightScroll && (
            <Button
              variant="ghost"
              size="icon"
              className="bg-muted/30 hover:bg-muted/50 dark:bg-muted/20 dark:hover:bg-muted/40 ml-1 h-8 w-8 flex-shrink-0 rounded-md backdrop-blur-sm"
              onClick={() => scrollPrompts('right')}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Chat Messages */}
      <ScrollArea className="flex-1 overflow-hidden">
        <div className="space-y-3 p-3">
          {displayMessages.length === 0 ? (
            <div className="flex h-full flex-col items-center justify-center py-8 text-center">
              <Bot className="text-muted-foreground/50 mb-3 h-10 w-10" />
              <p className="text-muted-foreground text-sm">
                Pergunte-me qualquer coisa sobre esta questão!
              </p>
              <p className="text-muted-foreground mt-1 text-xs">
                Selecione um prompt acima ou digite sua própria pergunta
              </p>
            </div>
          ) : (
            displayMessages.map((message, index) => (
              <div
                key={index}
                className={`flex gap-3 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`flex max-w-[85%] gap-3 ${
                    message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                  }`}
                >
                  <div
                    className={`flex h-8 w-8 shrink-0 items-center justify-center rounded-full ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted/50 dark:bg-muted/30'
                    }`}
                  >
                    {message.role === 'user' ? (
                      <User className="h-4 w-4" />
                    ) : (
                      <Bot className="h-4 w-4" />
                    )}
                  </div>
                  <div
                    className={`rounded-lg px-3 py-2 ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted/50 dark:bg-muted/30'
                    }`}
                  >
                    {message.role === 'assistant' && message.reasoning && (
                      <ChatReasoningBox reasoning={message.reasoning} />
                    )}
                    <MarkdownRenderer
                      content={message.content}
                      className={`text-sm ${message.role === 'user' ? 'prose-invert' : ''}`}
                    />
                    {message.role === 'assistant' && (
                      <ChatMessageMetadata
                        metadata={{
                          ...message.metadata,
                          model: selectedModel,
                        }}
                        className="mt-1"
                      />
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
          {loading && (
            <div className="flex justify-start">
              <div className="flex max-w-[85%] gap-3">
                <div className="bg-muted/50 dark:bg-muted/30 flex h-8 w-8 shrink-0 items-center justify-center rounded-full">
                  <Bot className="h-4 w-4" />
                </div>
                <div className="bg-muted/50 dark:bg-muted/30 rounded-lg px-3 py-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input Form */}
      <form onSubmit={handleSubmit} className="border-border flex-shrink-0 border-t p-3">
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Pergunte qualquer coisa sobre esta questão..."
              disabled={loading}
              className="pr-10"
            />

            {/* Model Selector Button inside input */}
            <div className="absolute top-1/2 right-2 -translate-y-1/2">
              <button
                type="button"
                onClick={() => setShowModelDropdown(!showModelDropdown)}
                className="text-muted-foreground hover:text-foreground p-1 transition-colors"
                aria-label="Select model"
              >
                <Cpu className="h-4 w-4" />
              </button>
              <Select
                value={selectedModel}
                onValueChange={(value) => {
                  setSelectedModel(value);
                  setShowModelDropdown(false);
                }}
                open={showModelDropdown}
                onOpenChange={setShowModelDropdown}
              >
                <SelectTrigger className="pointer-events-none absolute inset-0 opacity-0" />
                <SelectContent>
                  {Object.entries(modelsByCategory).map(([category, models]) => (
                    <SelectGroup key={category}>
                      <SelectLabel>{category}</SelectLabel>
                      {models.map((model) => (
                        <SelectItem key={model.value} value={model.value}>
                          {model.label}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button type="submit" size="icon" disabled={loading || !input.trim()}>
            {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
          </Button>
        </div>
      </form>
    </div>
  );
}
