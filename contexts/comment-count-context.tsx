'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface CommentCountContextType {
  commentCounts: Map<string, number>;
  updateCommentCount: (questionId: string, count: number) => void;
  incrementCommentCount: (questionId: string) => void;
  decrementCommentCount: (questionId: string) => void;
  getCommentCount: (questionId: string, defaultCount: number) => number;
}

const CommentCountContext = createContext<CommentCountContextType | undefined>(undefined);

export function CommentCountProvider({ children }: { children: ReactNode }) {
  const [commentCounts, setCommentCounts] = useState<Map<string, number>>(new Map());

  const updateCommentCount = useCallback((questionId: string, count: number) => {
    setCommentCounts((prev) => {
      const newMap = new Map(prev);
      console.log(`[CommentCount] Setting ${questionId} to ${count}`);
      newMap.set(questionId, count);
      return newMap;
    });
  }, []);

  const incrementCommentCount = useCallback((questionId: string) => {
    setCommentCounts((prev) => {
      const newMap = new Map(prev);
      const currentCount = newMap.get(questionId) ?? 0;
      const newCount = currentCount + 1;
      console.log(`[CommentCount] Incrementing ${questionId}: ${currentCount} -> ${newCount}`);
      newMap.set(questionId, newCount);
      return newMap;
    });
  }, []);

  const decrementCommentCount = useCallback((questionId: string) => {
    setCommentCounts((prev) => {
      const newMap = new Map(prev);
      const currentCount = newMap.get(questionId) ?? 0;
      const newCount = Math.max(0, currentCount - 1);
      console.log(`[CommentCount] Decrementing ${questionId}: ${currentCount} -> ${newCount}`);
      newMap.set(questionId, newCount);
      return newMap;
    });
  }, []);

  const getCommentCount = useCallback(
    (questionId: string, defaultCount: number): number => {
      return commentCounts.get(questionId) ?? defaultCount;
    },
    [commentCounts]
  );

  return (
    <CommentCountContext.Provider
      value={{
        commentCounts,
        updateCommentCount,
        incrementCommentCount,
        decrementCommentCount,
        getCommentCount,
      }}
    >
      {children}
    </CommentCountContext.Provider>
  );
}

export function useCommentCounts() {
  const context = useContext(CommentCountContext);
  if (!context) {
    // Return a no-op implementation if used outside provider
    return {
      commentCounts: new Map<string, number>(),
      updateCommentCount: () => {},
      incrementCommentCount: () => {},
      decrementCommentCount: () => {},
      getCommentCount: (_questionId: string, defaultCount: number) => defaultCount,
    };
  }
  return context;
}
