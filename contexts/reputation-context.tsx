'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

interface ReputationUpdate {
  userId: string;
  reputation: number;
}

interface ReputationContextType {
  reputationUpdates: Map<string, number>;
  updateUserReputation: (userId: string, reputation: number) => void;
  getUserReputation: (userId: string, defaultRep: number) => number;
}

const ReputationContext = createContext<ReputationContextType | undefined>(undefined);

export function ReputationProvider({ children }: { children: ReactNode }) {
  const [reputationUpdates, setReputationUpdates] = useState<Map<string, number>>(new Map());

  const updateUserReputation = useCallback((userId: string, reputation: number) => {
    setReputationUpdates((prev) => {
      const newMap = new Map(prev);
      newMap.set(userId, reputation);
      return newMap;
    });
  }, []);

  const getUserReputation = useCallback(
    (userId: string, defaultRep: number): number => {
      return reputationUpdates.get(userId) ?? defaultRep;
    },
    [reputationUpdates]
  );

  return (
    <ReputationContext.Provider
      value={{ reputationUpdates, updateUserReputation, getUserReputation }}
    >
      {children}
    </ReputationContext.Provider>
  );
}

export function useReputation() {
  const context = useContext(ReputationContext);
  if (!context) {
    // Return a no-op implementation if used outside provider
    return {
      reputationUpdates: new Map<string, number>(),
      updateUserReputation: () => {},
      getUserReputation: (_userId: string, defaultRep: number) => defaultRep,
    };
  }
  return context;
}
