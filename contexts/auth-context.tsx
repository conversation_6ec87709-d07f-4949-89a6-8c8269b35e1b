'use client';

import React, { createContext, useContext } from 'react';
import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import type { Role, Feature, SubscriptionTier, SubscriptionStatus } from '@/lib/auth/features';

interface AuthUser {
  id: string;
  email: string;
  name: string | null;
  image: string | null;
  avatarUrl?: string | null;
  role?: Role;
  tier?: SubscriptionTier;
  features?: Feature[];
  subscription?: {
    status: SubscriptionStatus;
    expiresAt?: string;
  };
}

interface AuthContextType {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  hasRole: (role: Role) => boolean;
  hasFeature: (feature: Feature) => boolean;
  login: (email: string, password: string) => Promise<void>;
  loginWithGoogle: (callbackUrl?: string) => Promise<void>;
  logout: () => Promise<void>;
  register?: (credentials: any) => Promise<void>;
  setUser?: (user: AuthUser | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const router = useRouter();

  const user: AuthUser | null = session?.user
    ? {
        id: session.user.id!,
        email: session.user.email!,
        name: session.user.name || null,
        image: session.user.image || null,
        avatarUrl: session.user.image || null,
        role: session.user.role,
        tier: session.user.tier,
        features: session.user.features,
        subscription: session.user.subscription,
      }
    : null;

  const isLoading = status === 'loading';
  const isAuthenticated = !!session?.user;

  const hasRole = (role: Role): boolean => {
    if (!user?.role) return false;
    return user.role === role || user.role === 'admin'; // Admin has all roles
  };

  const hasFeature = (feature: Feature): boolean => {
    if (!user?.features) return false;
    return user.features.includes(feature);
  };

  const login = async (email: string, password: string) => {
    const result = await signIn('credentials', {
      email,
      password,
      redirect: false,
    });

    if (result?.error) {
      throw new Error(result.error);
    }

    // Refresh the router to ensure session is updated
    router.refresh();
  };

  const loginWithGoogle = async (callbackUrl?: string) => {
    await signIn('google', {
      callbackUrl: callbackUrl || '/dashboard',
    });
  };

  const logout = async () => {
    await signOut({ redirect: false });
  };

  // Registration function
  const register = async (credentials: { name: string; email: string; password: string }) => {
    const response = await fetch('/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Registration failed');
    }

    // After successful registration, sign in automatically
    await login(credentials.email, credentials.password);
  };

  const setUser = (user: AuthUser | null) => {
    // No-op for now as session is managed by Auth.js
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated,
        hasRole,
        hasFeature,
        login,
        loginWithGoogle,
        logout,
        register,
        setUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
