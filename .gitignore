# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# drizzle
/drizzle

/docs/*
# Temporary files
repomix-output.txt
database-performance-analysis.md

# Repomix directory
.repomix/

# Temporary instruction files
newinstruct.md
newprompt.json

# Temporary test files
corrected-question.json

# Temporary Python image extraction files
/tmp/memo_images_*
**/memo_images_*

# Test and debug folders
test-debug/
test-debug-old/
