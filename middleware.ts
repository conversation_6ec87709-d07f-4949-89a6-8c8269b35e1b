import NextAuth from 'next-auth';
import { authConfig } from './auth.config';

// Use the Edge-compatible auth configuration
export default NextAuth(authConfig).auth;

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api/auth (handled by NextAuth)
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};
