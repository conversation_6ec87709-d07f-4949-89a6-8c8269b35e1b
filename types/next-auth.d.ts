import { DefaultSession, DefaultUser } from 'next-auth';
import { JWT, DefaultJWT } from 'next-auth/jwt';
import type { Role, Feature, SubscriptionTier, SubscriptionStatus } from '@/lib/auth/features';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      role?: Role;
      tier?: SubscriptionTier;
      features?: Feature[];
      subscription?: {
        status: SubscriptionStatus;
        expiresAt?: string;
      };
    } & DefaultSession['user'];
  }

  interface User extends DefaultUser {
    role?: Role;
    tier?: SubscriptionTier;
    features?: Feature[];
    subscription?: {
      status: SubscriptionStatus;
      expiresAt?: string;
    };
  }
}

declare module 'next-auth/jwt' {
  interface JWT extends DefaultJWT {
    userId?: string;
    role?: Role;
    tier?: SubscriptionTier;
    features?: Feature[];
    subscription?: {
      status: SubscriptionStatus;
      expiresAt?: string;
    };
  }
}
