export interface ImportMetadata {
  institution: string;
  exam_board: string;
  position: string;
  specialization?: string | null;
  year: number;
  test_type: string;
  booklet?: string | null;
  application_date?: string | null;
  total_questions: number;
}

export interface QuestionOption {
  order: number;
  text: string;
}

export interface QuestionImage {
  url: string;
  alt_text: string;
  page: number;
  bbox: number[]; // [x1, y1, x2, y2]
  type:
    | 'chart'
    | 'cartoon'
    | 'diagram'
    | 'table_image'
    | 'graph'
    | 'map'
    | 'photo'
    | 'illustration';
  text_content?: string | null;
  position_in_question:
    | 'before_stem'
    | 'in_stem'
    | 'after_stem'
    | 'in_option_A'
    | 'in_option_B'
    | 'in_option_C'
    | 'in_option_D'
    | 'in_option_E';
}

export interface AssociatedTextBlock {
  id: string;
  content: string;
}

export interface TextReference {
  snippet: string;
  location_pointer: string;
}

export interface ImportQuestion {
  number: number;
  subject?: string | null;
  topic?: string | null;
  associated_text_id: string | null;
  stem: string;
  associated_text_references?: TextReference[] | null;
  options: QuestionOption[];
  correct_answer_order: number | null;
  is_null: boolean;
  change_reason: string;
  // Manual image assignment fields
  image_url?: string;
  image_public_id?: string;
  // Support for multiple manual images
  manual_images?: Array<{
    url: string;
    public_id: string;
  }>;
}

export interface ImportData {
  metadata: ImportMetadata;
  associated_texts_catalog: AssociatedTextBlock[];
  questions: ImportQuestion[];
}

export interface MappedData {
  institutionId: string;
  institutionIsNew: boolean;
  examBoardId: string;
  examBoardIsNew: boolean;
  positionId: string;
  positionIsNew: boolean;
  specializationId: string;
  specializationIsNew: boolean;
  year: number;
  testType: string;
  booklet: string;
  applicationDate: string;
  // Manual mode fields for direct metadata input
  manualMode?: boolean;
  manualInstitution?: string;
  manualExamBoard?: string;
  manualPosition?: string;
  manualSpecialization?: string;
  // Matched exam/test IDs from exam matcher
  matchedExamId?: string;
  matchedTestId?: string;
  subjectMappings: Record<string, { id: string; isNew: boolean }>;
  topicMappings: Record<string, { id: string; isNew: boolean; subjectId: string }>;
}
