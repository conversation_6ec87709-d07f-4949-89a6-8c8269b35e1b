export interface QuestionOption {
  id: string;
  optionLetter: string;
  text: string;
  isCorrect: boolean;
}

export interface Question {
  id: string;
  number: number;
  stem: string;
  associatedText: string | null;
  associatedTextId?: string | null;
  associatedTextReferences?: Array<{ snippet: string; location_pointer: string }> | null;
  commentCount?: number;
  options: QuestionOption[];
  exam: {
    id: string;
    name: string;
    examBoard: string | null;
    examBoardId?: string;
    year: number | null;
  };
  institution?: {
    id: string;
    name: string;
    code?: string;
  };
  position: {
    id: string;
    name: string;
  } | null;
  specialization: {
    id: string;
    name: string;
  } | null;
  subject: {
    id: string;
    name: string;
  } | null;
  topic: {
    id: string;
    name: string;
  } | null;
}

export interface BasicQuestion {
  id: string;
  stem: string;
  number: number;
  exam: {
    year: number | null;
    name: string;
  };
  subject: {
    name: string;
  } | null;
  topic: {
    name: string;
  } | null;
}
