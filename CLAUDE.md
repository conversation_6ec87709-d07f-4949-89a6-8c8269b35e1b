# Memo App - Development Notes

## FSRS (Free Spaced Repetition Scheduler) Implementation

### Overview

The app uses ts-fsrs (version 5.2.0) for spaced repetition scheduling, implementing the FSRS6 algorithm.

### Key Features

- **Learning Steps**: 1 minute and 10 minutes for new cards
- **Fuzz Factor**: Enabled to prevent card clustering on the same day
- **Short-term Scheduler**: Enabled for proper learning/relearning phases
- **Maximum Interval**: 36,500 days (100 years)
- **Default Retention**: 90%
- **FSRS6 Parameters**: Uses 21 weights (w0-w20) with proper FSRS6 defaults

### User Parameters

- FSRS parameters are automatically created for users when they review their first card
- Default FSRS6 weights are used initially
- Parameters can be optimized later based on user review history (not yet implemented)

### Database Schema

The app uses "decks" terminology (not "question banks") to align with standard spaced repetition systems:

- `decks` - Collections of cards
- `deckCards` - Links questions to decks
- `fsrsCards` - Stores card state and scheduling data
- `fsrsReviewLogs` - Tracks all reviews for optimization
- `fsrsParameters` - User-specific FSRS parameters

### API Endpoints

- `POST /api/fsrs/schedule` - Get scheduling options for a card
- `POST /api/fsrs/review` - Review a card with a rating
- `GET /api/study/due-cards` - Get cards due for review
- `POST /api/fsrs/optimize` - Optimize parameters (not yet implemented)

### Testing

Run tests with:

```bash
npm run typecheck  # TypeScript compilation
npm run lint       # ESLint checks
```

### Important Commands

When making changes, always run:

- `npm run typecheck` - Ensure TypeScript compiles
- `npm run lint` - Check code style
- `npm run dev` - Start development server

### Authentication

All FSRS endpoints require authentication. The auth() function must receive the request object:

```typescript
const session = await auth(request);
```

## Google Generative AI (Gemini) Integration

### Overview

The app includes integration with Google's Gemini AI models for text generation and embeddings.

### Setup

1. Add your Gemini API key to `.env.local`:

   ```
   GOOGLE_GENAI_API_KEY="your-api-key-here"
   ```

2. The integration uses the `@google/genai` SDK (v1.9.0)

### Available Models

The integration now includes comprehensive model configurations with rate limits for all tiers:

#### Language Models

- **Gemini 2.5 Series** (Latest Generation)
  - `gemini-2.5-pro` - State-of-the-art thinking model
  - `gemini-2.5-flash` - Best price-performance ratio (default)
  - `gemini-2.5-flash-lite` - Optimized for cost and latency
- **Gemini 2.0 Series**
  - `gemini-2.0-flash-001` - Multimodal with native image generation
  - `gemini-2.0-flash-exp` - Experimental features

- **Specialized Models**
  - `gemini-2.5-flash-native-audio` - Native audio understanding
  - `gemini-2.5-flash-preview-tts` - Text-to-speech capabilities
  - `gemini-2.5-pro-preview-tts` - High-quality TTS

#### Embedding Models

- `text-embedding-preview-0815` - Latest embedding model (default)
- `text-embedding-004` - Stable production model

#### Model Aliases

Use these shortcuts for convenience:

- `flash` → `gemini-2.0-flash-001`
- `pro` → `gemini-2.5-pro`
- `flash-latest` → `gemini-2.5-flash`
- `flash-lite` → `gemini-2.5-flash-lite`
- `embedding` → `text-embedding-preview-0815`

### Rate Limits

Each model has different rate limits based on user tier:

- **Free Tier**: Limited RPM (5-20), up to 1M TPM
- **Tier 1**: 100-2000 RPM, up to 4M TPM
- **Tier 2**: 800-4000 RPM, up to 8M TPM
- **Tier 3**: 1600-20000 RPM, up to 16M TPM

Example rate limits for `gemini-2.5-flash`:

- Free: 10 RPM, 250K TPM, 250 RPD
- Tier 1: 1000 RPM, 1M TPM, 10K RPD
- Tier 2: 2000 RPM, 3M TPM, 100K RPD
- Tier 3: 10000 RPM, 8M TPM

### API Endpoints

- `POST /api/gemini/generate` - Generate text content
- `POST /api/gemini/embed` - Generate text embeddings

Both endpoints require authentication and track usage for rate limiting.

### React Hooks

Use the provided hooks for easy integration:

```typescript
import { useGemini, useGeminiChat } from '@/hooks/use-gemini';

// For single generation
const { generateContent, loading, error } = useGemini();

// For chat interface
const { messages, sendMessage, clearMessages } = useGeminiChat();
```

### Utility Functions

```typescript
import {
  getModelByName,
  getModelsByCapability,
  getRateLimitsForTier,
  getModelsByBestUse,
  isModelAvailableForTier,
} from '@/lib/gemini/service';

// Get model by name or alias
const model = getModelByName('flash-latest');

// Find models with specific capabilities
const visionModels = getModelsByCapability('supports_vision');

// Check rate limits for user tier
const limits = getRateLimitsForTier('gemini-2.5-flash', 'tier1');

// Find models for specific use cases
const audioModels = getModelsByBestUse('audio');

// Check tier availability
const available = isModelAvailableForTier('gemini-2.5-pro', 'free');
```

### Usage Tracking

AI usage is tracked with these limits:

- `aiGenerationsPerDay` - Text generation daily limit
- `aiEmbeddingsPerDay` - Embeddings daily limit

Premium users have higher or unlimited access.

## Development Best Practices

- **IMPORTANT: USE PNPM**
