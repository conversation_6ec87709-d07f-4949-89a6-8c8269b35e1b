'use client';

import { useEffect, useState } from 'react';

export function useSidebarAwarePositioning() {
  const [sidebarOffset, setSidebarOffset] = useState(0);
  const [contentWidth, setContentWidth] = useState(0);

  useEffect(() => {
    const updateDimensions = () => {
      // Get the sidebar inset element (the main content area)
      const sidebarInset = document.querySelector('[data-slot="sidebar-inset"]');
      
      if (sidebarInset) {
        // Get the actual position and width of the content area
        const insetRect = sidebarInset.getBoundingClientRect();
        
        // Calculate the offset from the viewport edge
        setSidebarOffset(insetRect.left);
        setContentWidth(insetRect.width);
      }
    };

    // Initial calculation with a small delay to ensure DOM is ready
    const timeoutId = setTimeout(updateDimensions, 100);

    // Update on resize
    window.addEventListener('resize', updateDimensions);
    
    // Watch for sidebar state changes using MutationObserver
    const observer = new MutationObserver(() => {
      // Delay to ensure transition is complete
      setTimeout(updateDimensions, 250);
    });
    
    // Observe the sidebar wrapper for state changes
    const sidebarWrapper = document.querySelector('[data-slot="sidebar-wrapper"]');
    if (sidebarWrapper) {
      observer.observe(sidebarWrapper, {
        attributes: true,
        attributeFilter: ['data-state'],
        subtree: true
      });
    }

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', updateDimensions);
      observer.disconnect();
    };
  }, []);

  return { sidebarOffset, contentWidth };
}