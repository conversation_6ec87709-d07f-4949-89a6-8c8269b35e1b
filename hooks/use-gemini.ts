import { useState, useCallback } from 'react';
import type {
  GeminiGenerateRequest,
  GeminiGenerateResponse,
  GeminiEmbedRequest,
  GeminiEmbedResponse,
} from '@/lib/gemini/types';

export function useGemini() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateContent = useCallback(
    async (request: GeminiGenerateRequest): Promise<GeminiGenerateResponse | null> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/gemini/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(request),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to generate content');
        }

        const data = await response.json();
        return data.data;
      } catch (err) {
        const message = err instanceof Error ? err.message : 'An error occurred';
        setError(message);
        return null;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  const generateEmbedding = useCallback(
    async (request: GeminiEmbedRequest): Promise<GeminiEmbedResponse | null> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/gemini/embed', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(request),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to generate embedding');
        }

        const data = await response.json();
        return data.data;
      } catch (err) {
        const message = err instanceof Error ? err.message : 'An error occurred';
        setError(message);
        return null;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  return {
    generateContent,
    generateEmbedding,
    loading,
    error,
  };
}

export function useGeminiChat() {
  const [messages, setMessages] = useState<Array<{ role: 'user' | 'assistant'; content: string }>>(
    []
  );
  const { generateContent, loading, error } = useGemini();

  const sendMessage = useCallback(
    async (message: string, systemInstruction?: string) => {
      const newUserMessage = { role: 'user' as const, content: message };
      setMessages((prev) => [...prev, newUserMessage]);

      const prompt =
        messages.length > 0
          ? messages.map((m) => `${m.role}: ${m.content}`).join('\n') +
            `\nuser: ${message}\nassistant:`
          : message;

      const response = await generateContent({
        prompt,
        systemInstruction,
        temperature: 0.7,
      });

      if (response) {
        const assistantMessage = { role: 'assistant' as const, content: response.text };
        setMessages((prev) => [...prev, assistantMessage]);
        return response.text;
      }

      return null;
    },
    [messages, generateContent]
  );

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  return {
    messages,
    sendMessage,
    clearMessages,
    loading,
    error,
  };
}
