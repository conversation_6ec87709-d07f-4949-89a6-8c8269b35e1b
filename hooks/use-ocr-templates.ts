import { useState, useEffect } from 'react';

interface OcrTemplate {
  id: string;
  name: string;
  description: string | null;
  feature: string;
  promptType: string;
  promptContent: string;
  modelName: string;
  isDefault: boolean;
  isSystem: boolean;
}

export function useOcrTemplates() {
  const [templates, setTemplates] = useState<OcrTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/import/templates');

      if (!response.ok) {
        throw new Error('Failed to fetch templates');
      }

      const data = await response.json();
      setTemplates(data.templates || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  const getTemplateByType = (type: string, isDefault = true) => {
    return templates.find((t) => t.promptType === type && (!isDefault || t.isDefault));
  };

  const getTemplatesForType = (type: string) => {
    return templates.filter((t) => t.promptType === type);
  };

  return {
    templates,
    loading,
    error,
    fetchTemplates,
    getTemplateByType,
    getTemplatesForType,
  };
}
