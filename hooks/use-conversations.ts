import { useState, useEffect, useCallback } from 'react';
import type { Conversation, Message } from '@/db/schema';

export interface ConversationWithMessages extends Omit<Conversation, 'metadata'> {
  messages: Message[];
  metadata?: any;
}

export function useConversations() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<ConversationWithMessages | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all conversations
  const fetchConversations = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/conversations');
      if (!response.ok) throw new Error('Failed to fetch conversations');
      const data = await response.json();
      setConversations(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch a specific conversation with messages
  const fetchConversation = useCallback(async (conversationId: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/conversations/${conversationId}`);
      if (!response.ok) throw new Error('Failed to fetch conversation');
      const data = await response.json();
      setCurrentConversation(data);
      return data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Create a new conversation
  const createConversation = useCallback(
    async (data: { title?: string; model?: string; systemPrompt?: string; metadata?: any }) => {
      try {
        const response = await fetch('/api/conversations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data),
        });
        if (!response.ok) throw new Error('Failed to create conversation');
        const newConversation = await response.json();
        // Add the new conversation to the list immediately
        setConversations((prev) => [newConversation, ...prev]);
        return newConversation;
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        return null;
      }
    },
    []
  );

  // Add a message to the current conversation
  const addMessage = useCallback(
    async (message: {
      role: 'user' | 'assistant' | 'system';
      content: string;
      reasoning?: string;
      model?: string;
      metadata?: any;
    }) => {
      if (!currentConversation) return null;

      try {
        const response = await fetch(`/api/conversations/${currentConversation.id}/messages`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(message),
        });
        if (!response.ok) throw new Error('Failed to add message');
        const newMessage = await response.json();

        // Update current conversation with new message
        setCurrentConversation((prev) => {
          if (!prev) return null;
          return {
            ...prev,
            messages: [...prev.messages, newMessage],
          };
        });

        // No need to fetch conversations here, the polling will handle it

        return newMessage;
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        return null;
      }
    },
    [currentConversation]
  );

  // Update conversation metadata
  const updateConversation = useCallback(
    async (
      conversationId: string,
      updates: {
        title?: string;
        systemPrompt?: string;
        metadata?: any;
        archived?: boolean;
      }
    ) => {
      try {
        const response = await fetch(`/api/conversations/${conversationId}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updates),
        });
        if (!response.ok) throw new Error('Failed to update conversation');
        const updated = await response.json();
        // Update the conversation in the list immediately
        setConversations((prev) =>
          prev.map((conv) => (conv.id === conversationId ? { ...conv, ...updated } : conv))
        );
        if (currentConversation?.id === conversationId) {
          setCurrentConversation((prev) => (prev ? { ...prev, ...updated } : null));
        }
        return updated;
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        return null;
      }
    },
    [currentConversation]
  );

  // Delete a conversation
  const deleteConversation = useCallback(
    async (conversationId: string) => {
      try {
        const response = await fetch(`/api/conversations/${conversationId}`, {
          method: 'DELETE',
        });
        if (!response.ok) throw new Error('Failed to delete conversation');
        // Remove the conversation from the list immediately
        setConversations((prev) => prev.filter((conv) => conv.id !== conversationId));
        if (currentConversation?.id === conversationId) {
          setCurrentConversation(null);
        }
        return true;
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        return false;
      }
    },
    [currentConversation]
  );

  // Load conversations on mount
  useEffect(() => {
    fetchConversations();
  }, [fetchConversations]);

  return {
    conversations,
    currentConversation,
    loading,
    error,
    fetchConversations,
    fetchConversation,
    createConversation,
    addMessage,
    updateConversation,
    deleteConversation,
    setCurrentConversation,
  };
}
