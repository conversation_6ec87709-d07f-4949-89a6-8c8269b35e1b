import { useState, useCallback } from 'react';
import { ChatMessage, OpenRouterModel, DEFAULT_MODEL } from '@/lib/openrouter/client';

export interface UseOpenRouterOptions {
  model?: OpenRouterModel;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  onError?: (error: Error) => void;
  initialMessages?: ChatMessage[];
  reasoning?: {
    enabled?: boolean;
    effort?: 'low' | 'medium' | 'high';
    maxTokens?: number;
    exclude?: boolean;
  };
}

export interface UseOpenRouterReturn {
  messages: ChatMessage[];
  sendMessage: (content: string) => Promise<void>;
  streamMessage: (content: string) => Promise<void>;
  loading: boolean;
  error: Error | null;
  clearMessages: () => void;
  setMessages: (messages: ChatMessage[]) => void;
}

export function useOpenRouter(options: UseOpenRouterOptions = {}): UseOpenRouterReturn {
  const [messages, setMessages] = useState<ChatMessage[]>(options.initialMessages || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const sendMessage = useCallback(
    async (content: string) => {
      try {
        setLoading(true);
        setError(null);

        const userMessage: ChatMessage = { role: 'user', content };
        const updatedMessages = [...messages, userMessage];
        setMessages(updatedMessages);

        const response = await fetch('/api/openrouter/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: options.model || DEFAULT_MODEL,
            messages: updatedMessages,
            temperature: options.temperature,
            maxTokens: options.maxTokens,
            stream: false,
            reasoning: options.reasoning,
            topP: options.topP,
            frequencyPenalty: options.frequencyPenalty,
            presencePenalty: options.presencePenalty,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to get response');
        }

        const data = await response.json();
        const assistantMessage: ChatMessage = {
          role: 'assistant',
          content: data.content,
          reasoning: data.reasoning,
          metadata: data.metadata ? { ...data.metadata, timestamp: Date.now() } : undefined,
        };

        setMessages([...updatedMessages, assistantMessage]);
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown error');
        setError(error);
        options.onError?.(error);
      } finally {
        setLoading(false);
      }
    },
    [messages, options]
  );

  const streamMessage = useCallback(
    async (content: string) => {
      try {
        setLoading(true);
        setError(null);

        const userMessage: ChatMessage = { role: 'user', content };
        const updatedMessages = [...messages, userMessage];
        setMessages(updatedMessages);

        const response = await fetch('/api/openrouter/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: options.model || DEFAULT_MODEL,
            messages: updatedMessages,
            temperature: options.temperature,
            maxTokens: options.maxTokens,
            stream: true,
            reasoning: options.reasoning,
            topP: options.topP,
            frequencyPenalty: options.frequencyPenalty,
            presencePenalty: options.presencePenalty,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to get response');
        }

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();
        let assistantContent = '';
        let assistantReasoning = '';
        let metadata: ChatMessage['metadata'] = {};

        if (reader) {
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  break;
                }
                try {
                  const parsed = JSON.parse(data);
                  if (parsed.content || parsed.reasoning) {
                    if (parsed.content) {
                      assistantContent += parsed.content;
                    }
                    if (parsed.reasoning) {
                      assistantReasoning += parsed.reasoning;
                    }
                    setMessages([
                      ...updatedMessages,
                      {
                        role: 'assistant',
                        content: assistantContent,
                        reasoning: assistantReasoning || undefined,
                        metadata,
                      },
                    ]);
                  } else if (parsed.metadata) {
                    metadata = { ...parsed.metadata, timestamp: Date.now() };
                    // Update the last message with metadata
                    setMessages([
                      ...updatedMessages,
                      {
                        role: 'assistant',
                        content: assistantContent,
                        reasoning: assistantReasoning || undefined,
                        metadata,
                      },
                    ]);
                  }
                } catch {
                  // Ignore parsing errors
                }
              }
            }
          }
        }
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown error');
        setError(error);
        options.onError?.(error);
      } finally {
        setLoading(false);
      }
    },
    [messages, options]
  );

  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
  }, []);

  return {
    messages,
    sendMessage,
    streamMessage,
    loading,
    error,
    clearMessages,
    setMessages,
  };
}
