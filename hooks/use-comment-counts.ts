import { useState, useEffect, useCallback, useRef } from 'react';
import { useCommentCounts as useCommentCountContext } from '@/contexts/comment-count-context';

interface UseCommentCountsProps {
  questionIds: string[];
}

export function useCommentCounts({ questionIds }: UseCommentCountsProps) {
  const [counts, setCounts] = useState<Record<string, number>>({});
  const [loading, setLoading] = useState(false);
  const previousIdsRef = useRef<string>('');
  const { updateCommentCount } = useCommentCountContext();

  const fetchCounts = useCallback(
    async (ids: string[]) => {
      if (ids.length === 0) return;

      const idsString = ids.join(',');

      // Skip if we're already fetching the same IDs
      if (previousIdsRef.current === idsString) {
        return;
      }

      previousIdsRef.current = idsString;
      setLoading(true);

      try {
        const response = await fetch(`/api/questions/comment-counts?ids=${idsString}`, {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error('Failed to fetch comment counts');
        }

        const data = await response.json();
        setCounts(data.counts);

        // Update the global context with fetched counts
        Object.entries(data.counts).forEach(([questionId, count]) => {
          updateCommentCount(questionId, count as number);
        });
      } catch (error) {
        console.error('Error fetching comment counts:', error);
      } finally {
        setLoading(false);
      }
    },
    [updateCommentCount]
  );

  useEffect(() => {
    fetchCounts(questionIds);
  }, [questionIds, fetchCounts]);

  // Function to update a single count (when a comment is added/removed)
  const updateCount = useCallback((questionId: string, delta: number) => {
    setCounts((prev) => ({
      ...prev,
      [questionId]: Math.max(0, (prev[questionId] || 0) + delta),
    }));
  }, []);

  return {
    counts,
    loading,
    updateCount,
    refetch: fetchCounts,
  };
}
