import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { Comment } from '@/components/comment-section';
import { useReputation } from '@/contexts/reputation-context';
import { useCommentCounts } from '@/contexts/comment-count-context';

interface UseCommentsProps {
  questionId: string | null;
  currentUserId?: string;
}

export function useComments({ questionId, currentUserId }: UseCommentsProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(false);
  const { updateUserReputation } = useReputation();
  const { incrementCommentCount, decrementCommentCount } = useCommentCounts();

  // Fetch comments for a question
  const fetchComments = useCallback(async () => {
    if (!questionId) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/questions/${questionId}/comments`, {
        credentials: 'include',
      });

      if (!response.ok) {
        const data = await response.json();
        console.error('Failed to fetch comments:', response.status, data);
        throw new Error(data.error || 'Failed to fetch comments');
      }

      const data = await response.json();

      // Convert timestamp strings to Date objects
      const convertTimestamps = (comments: any[]): Comment[] => {
        return comments.map((comment) => ({
          ...comment,
          timestamp: new Date(comment.timestamp),
          replies: comment.replies ? convertTimestamps(comment.replies) : [],
        }));
      };

      setComments(convertTimestamps(data.comments || []));
    } catch (error) {
      console.error('Error fetching comments:', error);
      toast.error('Failed to load comments');
    } finally {
      setLoading(false);
    }
  }, [questionId]);

  // Load comments when questionId changes
  useEffect(() => {
    fetchComments();
  }, [fetchComments]);

  // Vote on a comment
  const handleVote = useCallback(
    async (commentId: string, voteType: 'up' | 'down') => {
      if (!currentUserId) {
        toast.error('Please sign in to vote');
        return;
      }

      try {
        const response = await fetch(`/api/comments/${commentId}/vote`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ voteType }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to vote');
        }

        // Update local state with votes and reputation
        const updateCommentsRecursively = (comments: Comment[]): Comment[] => {
          return comments.map((comment) => {
            let updatedComment = { ...comment };

            // Update the voted comment
            if (comment.id === commentId) {
              updatedComment.votes = data.votes;
              updatedComment.userVote = data.userVote;
            }

            // Update reputation for all comments by the same user
            if (comment.userId === data.authorUserId) {
              updatedComment.userReputation = data.authorReputation;
            }

            // Recursively update replies
            if (comment.replies) {
              updatedComment.replies = updateCommentsRecursively(comment.replies);
            }

            return updatedComment;
          });
        };

        setComments(updateCommentsRecursively(comments));

        // Update global reputation state
        if (data.authorUserId && data.authorReputation !== undefined) {
          updateUserReputation(data.authorUserId, data.authorReputation);
        }
      } catch (error) {
        console.error('Error voting:', error);
        toast.error('Failed to vote on comment');
      }
    },
    [currentUserId, comments]
  );

  // Reply to a comment
  const handleReply = useCallback(
    async (parentId: string, content: string) => {
      if (!currentUserId) {
        toast.error('Please sign in to reply');
        return;
      }

      if (!questionId) return;

      try {
        const response = await fetch(`/api/questions/${questionId}/comments`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ content, parentId }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to create reply');
        }

        // Update local state
        const updateCommentsRecursively = (comments: Comment[]): Comment[] => {
          return comments.map((comment) => {
            if (comment.id === parentId) {
              return {
                ...comment,
                replies: [
                  ...(comment.replies || []),
                  {
                    ...data.comment,
                    timestamp: new Date(data.comment.timestamp),
                  },
                ],
              };
            }
            if (comment.replies) {
              return {
                ...comment,
                replies: updateCommentsRecursively(comment.replies),
              };
            }
            return comment;
          });
        };

        setComments(updateCommentsRecursively(comments));

        // Update comment count in real-time
        incrementCommentCount(questionId);

        toast.success('Reply added successfully');
      } catch (error) {
        console.error('Error replying:', error);
        toast.error('Failed to add reply');
      }
    },
    [currentUserId, questionId, comments, incrementCommentCount]
  );

  // Create a new top-level comment
  const handleNewComment = useCallback(
    async (content: string) => {
      if (!currentUserId) {
        toast.error('Please sign in to comment');
        return;
      }

      if (!questionId) return;

      console.log('[useComments] handleNewComment called for question:', questionId);

      try {
        const response = await fetch(`/api/questions/${questionId}/comments`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ content }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to create comment');
        }

        setComments([
          {
            ...data.comment,
            timestamp: new Date(data.comment.timestamp),
          },
          ...comments,
        ]);

        // Update comment count in real-time
        console.log('[useComments] Calling incrementCommentCount for question:', questionId);
        incrementCommentCount(questionId);

        toast.success('Comment added successfully');
      } catch (error) {
        console.error('Error creating comment:', error);
        toast.error('Failed to add comment');
      }
    },
    [currentUserId, questionId, comments, incrementCommentCount]
  );

  // Delete a comment
  const handleDelete = useCallback(
    async (commentId: string) => {
      if (!currentUserId) {
        toast.error('Please sign in to delete');
        return;
      }

      try {
        const response = await fetch(`/api/comments/${commentId}`, {
          method: 'DELETE',
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to delete comment');
        }

        // Count how many comments will be deleted (including replies)
        const countCommentsToDelete = (comments: Comment[], targetId: string): number => {
          let count = 0;
          for (const comment of comments) {
            if (comment.id === targetId) {
              // Count this comment plus all its nested replies
              count = 1;
              if (comment.replies && comment.replies.length > 0) {
                const countAllReplies = (replies: Comment[]): number => {
                  let replyCount = replies.length;
                  for (const reply of replies) {
                    if (reply.replies && reply.replies.length > 0) {
                      replyCount += countAllReplies(reply.replies);
                    }
                  }
                  return replyCount;
                };
                count += countAllReplies(comment.replies);
              }
              break;
            } else if (comment.replies) {
              count += countCommentsToDelete(comment.replies, targetId);
            }
          }
          return count;
        };

        const deletedCount = countCommentsToDelete(comments, commentId);

        // Update local state
        const removeCommentRecursively = (comments: Comment[]): Comment[] => {
          return comments
            .filter((comment) => comment.id !== commentId)
            .map((comment) => {
              if (comment.replies) {
                return {
                  ...comment,
                  replies: removeCommentRecursively(comment.replies),
                };
              }
              return comment;
            });
        };

        setComments(removeCommentRecursively(comments));

        // Update comment count in real-time - decrement by the total number deleted
        if (questionId && deletedCount > 0) {
          for (let i = 0; i < deletedCount; i++) {
            decrementCommentCount(questionId);
          }
        }

        toast.success('Comment deleted successfully');
      } catch (error) {
        console.error('Error deleting comment:', error);
        toast.error('Failed to delete comment');
      }
    },
    [currentUserId, comments, questionId, decrementCommentCount]
  );

  // Edit a comment
  const handleEdit = useCallback(
    async (commentId: string, content: string) => {
      if (!currentUserId) {
        toast.error('Please sign in to edit');
        return;
      }

      try {
        const response = await fetch(`/api/comments/${commentId}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ content }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to edit comment');
        }

        // Update local state
        const updateCommentsRecursively = (comments: Comment[]): Comment[] => {
          return comments.map((comment) => {
            if (comment.id === commentId) {
              return {
                ...comment,
                content: data.comment.content,
              };
            }
            if (comment.replies) {
              return {
                ...comment,
                replies: updateCommentsRecursively(comment.replies),
              };
            }
            return comment;
          });
        };

        setComments(updateCommentsRecursively(comments));
        toast.success('Comment updated successfully');
      } catch (error) {
        console.error('Error editing comment:', error);
        toast.error('Failed to edit comment');
      }
    },
    [currentUserId, comments]
  );

  return {
    comments,
    loading,
    handleVote,
    handleReply,
    handleNewComment,
    handleDelete,
    handleEdit,
    refetch: fetchComments,
  };
}
