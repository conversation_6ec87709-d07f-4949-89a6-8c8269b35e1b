import { useState, useCallback } from 'react';
import { Rating } from '@/lib/fsrs/types';

interface UseFSRSOptions {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

export function useFSRS(options: UseFSRSOptions = {}) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const reviewCard = useCallback(
    async (cardId: string, rating: Rating) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/fsrs/review', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ cardId, rating }),
        });

        if (!response.ok) {
          throw new Error('Failed to review card');
        }

        const data = await response.json();
        options.onSuccess?.();
        return data;
      } catch (err) {
        const error = err as Error;
        setError(error);
        options.onError?.(error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [options]
  );

  const getDueCards = useCallback(
    async (limit: number = 20) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/fsrs/due-cards?limit=${limit}`);

        if (!response.ok) {
          throw new Error('Failed to get due cards');
        }

        const data = await response.json();
        return data.cards;
      } catch (err) {
        const error = err as Error;
        setError(error);
        options.onError?.(error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [options]
  );

  const optimizeParameters = useCallback(
    async (minReviews: number = 20) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch('/api/fsrs/optimize', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ minReviews }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to optimize parameters');
        }

        const data = await response.json();
        return data;
      } catch (err) {
        const error = err as Error;
        setError(error);
        options.onError?.(error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [options]
  );

  const getSchedulingInfo = useCallback(
    async (cardId: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/fsrs/card/${cardId}/schedule`);

        if (!response.ok) {
          throw new Error('Failed to get scheduling info');
        }

        const data = await response.json();
        return data;
      } catch (err) {
        const error = err as Error;
        setError(error);
        options.onError?.(error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [options]
  );

  return {
    reviewCard,
    getDueCards,
    optimizeParameters,
    getSchedulingInfo,
    isLoading,
    error,
  };
}
