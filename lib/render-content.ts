import DOMPurify from 'dompurify';
import katex from 'katex';

// CSS for content styling
const CONTENT_STYLES = `
  <style>
    [data-rendered-content] img {
      display: inline-block;
      margin: 0 0.25rem;
      max-width: 100%;
      height: auto;
      vertical-align: middle;
    }
    [data-rendered-content] img.block-image {
      display: block;
      margin: 1rem auto;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    [data-rendered-content] p.context {
      font-size: 0.875em;
      color: var(--muted-foreground, #6b7280);
      line-height: 1.5;
      margin-bottom: 0.75rem;
    }
    [data-rendered-content] p.stem {
      font-size: 1em;
      line-height: 1.6;
    }
    [data-rendered-content] p.source {
      font-size: 0.8125em;
      color: var(--muted-foreground, #6b7280);
      font-style: italic;
      line-height: 1.4;
      margin-top: 1.5rem;
      padding-top: 0.75rem;
      border-top: 1px solid var(--border, #e5e7eb);
      opacity: 0.9;
    }
    [data-rendered-content] p.source strong {
      font-style: normal;
      font-weight: 600;
    }
    [data-rendered-content] .text-reference-highlight {
      font-weight: 600;
      background-color: rgba(250, 204, 21, 0.4);
      color: inherit;
      padding: 0.125rem 0.25rem;
      border-radius: 2px;
      box-decoration-break: clone;
      -webkit-box-decoration-break: clone;
    }
    @media (prefers-color-scheme: dark) {
      [data-rendered-content] .text-reference-highlight {
        background-color: rgba(250, 204, 21, 0.3);
      }
    }
    .dark [data-rendered-content] .text-reference-highlight {
      background-color: rgba(250, 204, 21, 0.3);
    }
  </style>
`;

/**
 * Renders LaTeX expressions in text content
 * Supports multiple delimiter styles:
 * - Inline: $...$ or \(...\)
 * - Display: $$...$$ or \[...\]
 */
function renderLatex(text: string): string {
  let processed = text;

  // Store currency values to protect them from LaTeX processing
  const currencyPlaceholders: { [key: string]: string } = {};
  let placeholderIndex = 0;

  // Common currency patterns to preserve
  // Matches: $100, $1,000, $1,000.00, R$ 100, R$ 1.000,00, etc.
  const currencyPatterns = [
    // US Dollar format: $1,234.56
    /\$\s*\d{1,3}(?:,\d{3})*(?:\.\d{2})?(?!\w)/g,
    // Brazilian Real format: R$ 1.234,56 or R$1.234,56
    /R\$\s*\d{1,3}(?:\.\d{3})*(?:,\d{2})?(?!\w)/g,
    // Euro format: €1.234,56 or € 1.234,56
    /€\s*\d{1,3}(?:\.\d{3})*(?:,\d{2})?(?!\w)/g,
    // Generic currency symbol followed by number
    /[¥£₹₽¢]\s*\d{1,3}(?:[,.\s]\d{3})*(?:[,.]\d{2})?(?!\w)/g,
  ];

  // Replace currency values with placeholders
  currencyPatterns.forEach((pattern) => {
    processed = processed.replace(pattern, (match) => {
      const placeholder = `__CURRENCY_${placeholderIndex}__`;
      currencyPlaceholders[placeholder] = match;
      placeholderIndex++;
      return placeholder;
    });
  });

  // Process display math first
  // Handle \[...\] delimiters
  processed = processed.replace(/\\\[([\s\S]*?)\\\]/g, (match, latex) => {
    try {
      return katex.renderToString(latex, {
        throwOnError: false,
        displayMode: true,
      });
    } catch (e) {
      console.error('KaTeX error:', e);
      return match; // Return original if rendering fails
    }
  });

  // Handle $$...$$ delimiters
  processed = processed.replace(/\$\$([\s\S]*?)\$\$/g, (match, latex) => {
    try {
      return katex.renderToString(latex, {
        throwOnError: false,
        displayMode: true,
      });
    } catch (e) {
      console.error('KaTeX error:', e);
      return match; // Return original if rendering fails
    }
  });

  // Process inline math
  // Handle \(...\) delimiters
  processed = processed.replace(/\\\(([\s\S]*?)\\\)/g, (match, latex) => {
    try {
      return katex.renderToString(latex, {
        throwOnError: false,
        displayMode: false,
      });
    } catch (e) {
      console.error('KaTeX error:', e);
      return match; // Return original if rendering fails
    }
  });

  // Handle $...$ delimiters with improved pattern
  // More flexible pattern that works at string boundaries and after whitespace/punctuation
  processed = processed.replace(
    /(?:^|(?<=\s|[^\w$]))\$([^\$\n]+?)\$(?=\s|[^\w$]|$)/g,
    (match, latex) => {
      try {
        return katex.renderToString(latex, {
          throwOnError: false,
          displayMode: false,
        });
      } catch (e) {
        console.error('KaTeX error:', e);
        return match; // Return original if rendering fails
      }
    }
  );

  // Restore currency values
  Object.entries(currencyPlaceholders).forEach(([placeholder, value]) => {
    processed = processed.replace(placeholder, value);
  });

  return processed;
}

/**
 * Safely renders content with HTML and LaTeX support
 * 1. First processes LaTeX expressions
 * 2. Then sanitizes HTML to prevent XSS attacks
 */
export function renderContent(content: string): string {
  if (!content) return '';

  // First render LaTeX
  const latexProcessed = renderLatex(content);

  // Configure DOMPurify to allow KaTeX elements
  const clean = DOMPurify.sanitize(latexProcessed, {
    ADD_TAGS: [
      'span',
      'math',
      'mrow',
      'mi',
      'mo',
      'mn',
      'msup',
      'msub',
      'mfrac',
      'mroot',
      'msqrt',
      'mtext',
      'mspace',
      'semantics',
      'annotation',
      'annotation-xml',
    ],
    ADD_ATTR: ['class', 'style', 'mathvariant', 'width', 'height', 'valign', 'rowspan', 'colspan'],
    ALLOWED_TAGS: [
      // Default safe tags
      'a',
      'b',
      'i',
      'em',
      'strong',
      'p',
      'br',
      'ul',
      'ol',
      'li',
      'blockquote',
      'code',
      'pre',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'hr',
      'div',
      'span',
      'sub',
      'sup',
      'small',
      // Table tags
      'table',
      'thead',
      'tbody',
      'tr',
      'th',
      'td',
      // Image tags
      'img',
      // Style tags for image styling
      'style',
      // KaTeX generated tags
      'math',
      'mrow',
      'mi',
      'mo',
      'mn',
      'msup',
      'msub',
      'mfrac',
      'mroot',
      'msqrt',
      'mtext',
      'mspace',
      'semantics',
      'annotation',
      'annotation-xml',
    ],
    ALLOWED_ATTR: [
      'href',
      'target',
      'rel',
      'class',
      'style',
      'mathvariant',
      'width',
      'height',
      'valign',
      'rowspan',
      'colspan',
      // Image attributes
      'src',
      'alt',
      'data-page',
      'loading',
      'crossorigin',
      'decoding',
      'sizes',
      'srcset',
    ],
  });

  return CONTENT_STYLES + clean;
}

/**
 * Returns an object that can be spread into dangerouslySetInnerHTML
 * This is not a hook, so it can be used anywhere
 */
export function getRenderedContent(content: string): {
  dangerouslySetInnerHTML: { __html: string };
  'data-rendered-content': boolean;
} {
  return {
    dangerouslySetInnerHTML: {
      __html: renderContent(content),
    },
    'data-rendered-content': true,
  };
}

/**
 * Highlights text references in associated text content
 * @param content The HTML content to process
 * @param textReferences Array of text references to highlight
 * @returns HTML content with highlighted references
 */
export function highlightTextReferences(
  content: string,
  textReferences?: Array<{ snippet: string; location_pointer: string }> | null
): string {
  if (!content || !textReferences || textReferences.length === 0) {
    return content;
  }

  let processedContent = content;

  // Sort references by snippet length (longest first) to avoid partial replacements
  const sortedReferences = [...textReferences].sort((a, b) => b.snippet.length - a.snippet.length);

  for (const ref of sortedReferences) {
    // Escape special regex characters in the snippet
    const escapedSnippet = ref.snippet.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // Create a regex that matches the snippet
    // Use word boundaries to avoid partial word matches
    const regex = new RegExp(`\\b${escapedSnippet}\\b`, 'g');

    // Replace with highlighted version
    processedContent = processedContent.replace(
      regex,
      `<span class="text-reference-highlight" title="Linha ${ref.location_pointer}">${ref.snippet}</span>`
    );
  }

  return processedContent;
}

/**
 * Renders content with text references highlighted
 * Combines renderContent and highlightTextReferences
 */
export function renderContentWithReferences(
  content: string,
  textReferences?: Array<{ snippet: string; location_pointer: string }> | null
): string {
  const renderedContent = renderContent(content);
  return highlightTextReferences(renderedContent, textReferences);
}

/**
 * Returns an object that can be spread into dangerouslySetInnerHTML with text references
 */
export function getRenderedContentWithReferences(
  content: string,
  textReferences?: Array<{ snippet: string; location_pointer: string }> | null
): {
  dangerouslySetInnerHTML: { __html: string };
  'data-rendered-content': boolean;
} {
  return {
    dangerouslySetInnerHTML: {
      __html: renderContentWithReferences(content, textReferences),
    },
    'data-rendered-content': true,
  };
}
