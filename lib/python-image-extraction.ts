/**
 * Python Image Extraction Service
 * Integrates advanced Python image extractor with Node.js application
 */

import { spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import { tmpdir } from 'os';
import { randomUUID } from 'crypto';

export interface BoundingBox {
  x0?: number;
  y0?: number;
  x1?: number;
  y1?: number;
  // Alternative formats
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  // Bbox array format
  bbox?: number[];
}

export interface CoordinateData {
  pageNumber?: number;
  page?: number;
  boundingBox?: BoundingBox;
  bbox?: number[] | BoundingBox;
  text?: string;
}

export interface ImageExtractionResult {
  success: boolean;
  page_number?: number;
  bounding_box?: {
    x0: number;
    y0: number;
    x1: number;
    y1: number;
  };
  method?: string;
  confidence?: number;
  image_size?: {
    width: number;
    height: number;
  };
  hash?: string;
  cloudinary_url?: string;
  public_id?: string;
  uploaded?: boolean;
  upload_error?: string;
  error?: string;
  local_path?: string;
  local_filename?: string;
}

export interface ExtractionOptions {
  uploadToCloudinary?: boolean;
  folder?: string;
  includeVisualDetection?: boolean;
  skipCoverPage?: boolean;
}

export class PythonImageExtractionService {
  private pythonPath: string;
  private scriptPath: string;
  private cloudinaryCloudName?: string;
  private cloudinaryApiKey?: string;
  private cloudinaryApiSecret?: string;

  constructor(
    options: {
      pythonPath?: string;
      cloudinaryCloudName?: string;
      cloudinaryApiKey?: string;
      cloudinaryApiSecret?: string;
    } = {}
  ) {
    // Use explicit virtual environment path to ensure dependencies are available
    this.pythonPath = options.pythonPath || '/home/<USER>/AI/core/.venv/bin/python3';
    this.scriptPath = path.join(process.cwd(), 'scripts', 'advanced_image_extractor.py');
    this.cloudinaryCloudName = options.cloudinaryCloudName || process.env.CLOUDINARY_CLOUD_NAME;
    this.cloudinaryApiKey = options.cloudinaryApiKey || process.env.CLOUDINARY_API_KEY;
    this.cloudinaryApiSecret = options.cloudinaryApiSecret || process.env.CLOUDINARY_API_SECRET;
  }

  /**
   * Extract images from PDF using Python script
   */
  async extractImages(
    pdfBuffer: Buffer,
    coordinates?: CoordinateData[],
    options: ExtractionOptions = {}
  ): Promise<{
    success: boolean;
    extracted_images: ImageExtractionResult[];
    total_processed: number | string;
    successful_extractions: number;
    error?: string;
  }> {
    const tempPdfPath = await this.saveTempFile(pdfBuffer, '.pdf');

    try {
      // Normalize coordinates to expected format
      const normalizedCoordinates = coordinates
        ? this.normalizeCoordinates(coordinates)
        : undefined;

      // Build command arguments
      const args = [this.scriptPath, '--pdf-path', tempPdfPath];

      // Add coordinates if provided
      if (normalizedCoordinates) {
        args.push('--coordinates', JSON.stringify(normalizedCoordinates));
      }

      // Add Cloudinary configuration if available and upload requested
      if (options.uploadToCloudinary !== false && this.isCloudinaryConfigured()) {
        args.push(
          '--cloudinary-cloud-name',
          this.cloudinaryCloudName!,
          '--cloudinary-api-key',
          this.cloudinaryApiKey!,
          '--cloudinary-api-secret',
          this.cloudinaryApiSecret!,
          '--upload'
        );
      }

      // Add optional parameters
      if (options.folder) {
        args.push('--folder', options.folder);
      }

      if (options.includeVisualDetection) {
        args.push('--visual-detection');
      }

      if (options.skipCoverPage !== false) {
        args.push('--skip-cover');
      }

      // Execute Python script
      console.log(`Using Python path: ${this.pythonPath}`);
      console.log(`Python args:`, args);
      console.log(`Cloudinary configured:`, this.isCloudinaryConfigured());
      const result = await this.executePythonScript(args);
      console.log(`Python extraction result:`, JSON.stringify(result, null, 2));

      return result;
    } catch (error) {
      console.error('Python image extraction failed:', error);
      return {
        success: false,
        extracted_images: [],
        total_processed: 0,
        successful_extractions: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    } finally {
      // Clean up temporary file
      try {
        await fs.unlink(tempPdfPath);
      } catch (error) {
        console.warn('Failed to clean up temporary PDF file:', error);
      }
    }
  }

  /**
   * Extract images from specific coordinates
   */
  async extractFromCoordinates(
    pdfBuffer: Buffer,
    coordinates: CoordinateData[],
    options: ExtractionOptions = {}
  ): Promise<ImageExtractionResult[]> {
    const result = await this.extractImages(pdfBuffer, coordinates, options);
    return result.extracted_images;
  }

  /**
   * Extract all embedded images automatically
   */
  async extractAllImages(
    pdfBuffer: Buffer,
    options: ExtractionOptions = {}
  ): Promise<ImageExtractionResult[]> {
    const result = await this.extractImages(pdfBuffer, undefined, options);
    return result.extracted_images;
  }

  private async saveTempFile(buffer: Buffer, extension: string): Promise<string> {
    const tempDir = tmpdir();
    const fileName = `${randomUUID()}${extension}`;
    const filePath = path.join(tempDir, fileName);

    await fs.writeFile(filePath, buffer);
    return filePath;
  }

  private normalizeCoordinates(coordinates: CoordinateData[]): CoordinateData[] {
    return coordinates.map((coord) => {
      // Handle different coordinate formats and normalize them
      const normalized: CoordinateData = {
        pageNumber: coord.pageNumber || coord.page || 1,
        text: coord.text || 'extracted_image',
      };

      // Handle bbox array format [x0, y0, x1, y1]
      if (coord.bbox && Array.isArray(coord.bbox) && coord.bbox.length === 4) {
        normalized.boundingBox = {
          x0: coord.bbox[0],
          y0: coord.bbox[1],
          x1: coord.bbox[2],
          y1: coord.bbox[3],
        };
      }
      // Handle bbox object format
      else if (coord.bbox && typeof coord.bbox === 'object') {
        const bbox = coord.bbox as BoundingBox;
        if (
          bbox.x0 !== undefined &&
          bbox.y0 !== undefined &&
          bbox.x1 !== undefined &&
          bbox.y1 !== undefined
        ) {
          normalized.boundingBox = {
            x0: bbox.x0,
            y0: bbox.y0,
            x1: bbox.x1,
            y1: bbox.y1,
          };
        } else if (
          bbox.x !== undefined &&
          bbox.y !== undefined &&
          bbox.width !== undefined &&
          bbox.height !== undefined
        ) {
          // Convert x,y,width,height to x0,y0,x1,y1
          normalized.boundingBox = {
            x0: bbox.x,
            y0: bbox.y,
            x1: bbox.x + bbox.width,
            y1: bbox.y + bbox.height,
          };
        }
      }
      // Handle boundingBox object
      else if (coord.boundingBox) {
        const bbox = coord.boundingBox;
        if (
          bbox.x0 !== undefined &&
          bbox.y0 !== undefined &&
          bbox.x1 !== undefined &&
          bbox.y1 !== undefined
        ) {
          normalized.boundingBox = bbox;
        } else if (
          bbox.x !== undefined &&
          bbox.y !== undefined &&
          bbox.width !== undefined &&
          bbox.height !== undefined
        ) {
          normalized.boundingBox = {
            x0: bbox.x,
            y0: bbox.y,
            x1: bbox.x + bbox.width,
            y1: bbox.y + bbox.height,
          };
        }
      }

      return normalized;
    });
  }

  private async executePythonScript(args: string[]): Promise<{
    success: boolean;
    extracted_images: ImageExtractionResult[];
    total_processed: number | string;
    successful_extractions: number;
    error?: string;
  }> {
    return new Promise((resolve, reject) => {
      const process = spawn(this.pythonPath, args);

      let stdout = '';
      let stderr = '';

      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        if (code !== 0) {
          console.error(`Python execution failed:`, {
            code,
            pythonPath: this.pythonPath,
            args: args.slice(0, 3), // First few args for debugging
            stdout: stdout.substring(0, 200),
            stderr: stderr.substring(0, 500),
          });
          reject(new Error(`Python script failed with code ${code}: ${stderr}`));
          return;
        }

        try {
          const result = JSON.parse(stdout);
          resolve(result);
        } catch (error) {
          reject(new Error(`Failed to parse Python script output: ${error}\nOutput: ${stdout}`));
        }
      });

      process.on('error', (err) => {
        reject(new Error(`Failed to start Python script: ${err.message}`));
      });

      // Set timeout (5 minutes)
      const timeout = setTimeout(
        () => {
          process.kill();
          reject(new Error('Python script execution timed out'));
        },
        5 * 60 * 1000
      );

      process.on('close', () => {
        clearTimeout(timeout);
      });
    });
  }

  private isCloudinaryConfigured(): boolean {
    return !!(this.cloudinaryCloudName && this.cloudinaryApiKey && this.cloudinaryApiSecret);
  }

  /**
   * Check if Python dependencies are installed
   */
  async checkDependencies(): Promise<{ available: boolean; missing: string[] }> {
    try {
      const args = ['-c', 'import fitz, cv2, cloudinary; print("OK")'];
      const process = spawn(this.pythonPath, args);

      return new Promise((resolve) => {
        let stdout = '';
        let stderr = '';

        process.stdout.on('data', (data) => {
          stdout += data.toString();
        });

        process.stderr.on('data', (data) => {
          stderr += data.toString();
        });

        process.on('close', (code) => {
          if (code === 0 && stdout.includes('OK')) {
            resolve({ available: true, missing: [] });
          } else {
            const missing = [];
            if (stderr.includes('fitz') || stderr.includes('PyMuPDF')) {
              missing.push('PyMuPDF');
            }
            if (stderr.includes('cv2') || stderr.includes('opencv')) {
              missing.push('opencv-python');
            }
            if (stderr.includes('cloudinary')) {
              missing.push('cloudinary');
            }
            resolve({ available: false, missing });
          }
        });
      });
    } catch {
      return { available: false, missing: ['python3', 'PyMuPDF', 'opencv-python', 'cloudinary'] };
    }
  }
}

// Export singleton instance
export const pythonImageExtractor = new PythonImageExtractionService();
