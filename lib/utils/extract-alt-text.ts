import { J<PERSON><PERSON> } from 'jsdom';

export interface ImageAltText {
  url: string;
  altText: string;
}

/**
 * Extracts alt text from img tags in HTML content
 * @param html - HTML content to parse
 * @returns Array of objects containing image URLs and their alt text
 */
export function extractAltTextFromHtml(html: string): ImageAltText[] {
  try {
    // Parse the HTML
    const dom = new JSDOM(html);
    const document = dom.window.document;

    // Find all img tags
    const images = document.querySelectorAll('img');

    // Extract URL and alt text from each image
    const imageAltTexts: ImageAltText[] = [];

    images.forEach((img) => {
      const src = img.getAttribute('src');
      const alt = img.getAttribute('alt');

      if (src) {
        imageAltTexts.push({
          url: src,
          altText: alt || '', // Use empty string if no alt attribute
        });
      }
    });

    return imageAltTexts;
  } catch (error) {
    console.error('Error parsing HTML for alt text extraction:', error);
    return [];
  }
}

/**
 * Extracts alt text from question content (stem, options, associated_text)
 * @param stem - Question stem HTML
 * @param options - Array of option objects with text property
 * @param associatedText - Associated text HTML (optional)
 * @returns Map of image URLs to their alt text
 */
export function extractAltTextFromQuestion(
  stem: string,
  options: Array<{ text: string }> = [],
  associatedText?: string
): Map<string, string> {
  const altTextMap = new Map<string, string>();

  // Extract from stem
  const stemImages = extractAltTextFromHtml(stem);
  stemImages.forEach(({ url, altText }) => {
    if (altText) {
      altTextMap.set(url, altText);
    }
  });

  // Extract from options
  options.forEach((option) => {
    const optionImages = extractAltTextFromHtml(option.text);
    optionImages.forEach(({ url, altText }) => {
      if (altText && !altTextMap.has(url)) {
        altTextMap.set(url, altText);
      }
    });
  });

  // Extract from associated text
  if (associatedText) {
    const associatedImages = extractAltTextFromHtml(associatedText);
    associatedImages.forEach(({ url, altText }) => {
      if (altText && !altTextMap.has(url)) {
        altTextMap.set(url, altText);
      }
    });
  }

  return altTextMap;
}
