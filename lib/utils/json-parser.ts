/**
 * Strips markdown code block formatting from JSON strings
 * Handles Gemini's tendency to wrap JSON output in ```json ... ``` blocks
 */
export function stripMarkdownCodeBlock(text: string): string {
  // Trim whitespace
  let cleanText = text.trim();

  // Check if the text starts with ```json or just ```
  if (cleanText.startsWith('```json')) {
    // Remove the opening ```json
    cleanText = cleanText.slice(7);
  } else if (cleanText.startsWith('```')) {
    // Remove the opening ```
    cleanText = cleanText.slice(3);
  }

  // Check if the text ends with ```
  if (cleanText.endsWith('```')) {
    // Remove the closing ```
    cleanText = cleanText.slice(0, -3);
  }

  // Trim any remaining whitespace
  return cleanText.trim();
}

/**
 * Safely parses JSON that might be wrapped in markdown code blocks
 * @param text The text that might contain JSO<PERSON> with markdown formatting
 * @returns Parsed JSON object
 * @throws Error if the JSON is invalid after cleaning
 */
export function parseMarkdownJSON<T = unknown>(text: string): T {
  const cleanedText = stripMarkdownCodeBlock(text);
  return JSON.parse(cleanedText);
}

/**
 * Parses raw OCR output from the database
 * Handles the extractedData structure from ocr_processing_history table
 * @param extractedData The extracted data from the database (could be string or object)
 * @returns Parsed JSON object
 *
 * @example
 * // When retrieving from the database:
 * const history = await db.select().from(ocrProcessingHistory).where(eq(ocrProcessingHistory.id, historyId));
 * const parsedData = parseOCRExtractedData<ImportData>(history[0].extractedData);
 *
 * @example
 * // The function handles multiple formats:
 * // 1. Database format: { rawText: "```json\n{...}\n```" }
 * // 2. Raw string: "```json\n{...}\n```"
 * // 3. Already parsed object: { metadata: {...}, questions: [...] }
 */
export function parseOCRExtractedData<T = unknown>(extractedData: unknown): T {
  // If extractedData is already an object with rawText property
  if (typeof extractedData === 'object' && extractedData !== null && 'rawText' in extractedData) {
    const rawText = (extractedData as { rawText: string }).rawText;
    return parseMarkdownJSON<T>(rawText);
  }

  // If extractedData is a string, parse it directly
  if (typeof extractedData === 'string') {
    return parseMarkdownJSON<T>(extractedData);
  }

  // If extractedData is already the parsed object
  if (typeof extractedData === 'object' && extractedData !== null) {
    return extractedData as T;
  }

  throw new Error('Invalid extractedData format');
}
