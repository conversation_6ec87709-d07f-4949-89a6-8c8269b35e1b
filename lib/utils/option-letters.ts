/**
 * Converts a numeric order (1-based) to a letter (A, B, C, etc.)
 * @param order - The 1-based order number
 * @returns The corresponding letter
 */
export function getOptionLetter(order: number): string {
  if (order < 1 || order > 26) {
    throw new Error('Order must be between 1 and 26');
  }
  return String.fromCharCode(64 + order); // 65 is 'A', so 64 + 1 = 'A'
}

/**
 * Adds option letters to an array of options based on their order
 * @param options - Array of options with an order field
 * @returns Array of options with added optionLetter field
 */
export function addOptionLetters<T extends { order: number }>(
  options: T[]
): (T & { optionLetter: string })[] {
  return options
    .sort((a, b) => a.order - b.order)
    .map((option) => ({
      ...option,
      optionLetter: getOptionLetter(option.order),
    }));
}

/**
 * Adds option letters and isCorrect flag to an array of options
 * @param options - Array of options with order and id fields
 * @param correctAnswerId - The ID of the correct option
 * @returns Array of options with added optionLetter and isCorrect fields
 */
export function addOptionLettersWithCorrect<T extends { order: number; id: string }>(
  options: T[],
  correctAnswerId: string | null | undefined
): (T & { optionLetter: string; isCorrect: boolean })[] {
  return options
    .sort((a, b) => a.order - b.order)
    .map((option) => ({
      ...option,
      optionLetter: getOptionLetter(option.order),
      isCorrect: option.id === correctAnswerId,
    }));
}
