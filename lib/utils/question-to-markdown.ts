/**
 * Convert question data to markdown format for AI context
 */
export function questionToMarkdown(data: {
  examBoard?: string;
  institution?: string;
  year?: number;
  position?: string;
  specialization?: string;
  subject: string;
  topic?: string;
  associatedText?: string;
  associatedTextReferences?: Array<{ snippet: string; location_pointer: string }> | null;
  questionText: string;
  options: Array<{ letter: string; text: string; commentary?: string }>;
  correctAnswer?: string;
}): string {
  const lines: string[] = [];

  // Header with metadata
  lines.push('# Question Details\n');

  // Metadata line
  const metadata: string[] = [];
  if (data.examBoard) metadata.push(`**Exam Board**: ${data.examBoard}`);
  if (data.institution) metadata.push(`**Institution**: ${data.institution}`);
  if (data.year) metadata.push(`**Year**: ${data.year}`);
  if (metadata.length > 0) {
    lines.push(metadata.join(' | '));
  }

  if (data.position || data.specialization) {
    const positionData: string[] = [];
    if (data.position) positionData.push(`**Position**: ${data.position}`);
    if (data.specialization) positionData.push(`**Specialization**: ${data.specialization}`);
    lines.push(positionData.join(' | '));
  }

  lines.push(`**Subject**: ${data.subject}${data.topic ? ` | **Topic**: ${data.topic}` : ''}\n`);

  // Associated text (context)
  if (data.associatedText) {
    lines.push('## Context\n');
    // Remove HTML tags for cleaner markdown
    const cleanText = data.associatedText
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");
    lines.push(cleanText);

    // Add text references if available
    if (data.associatedTextReferences && data.associatedTextReferences.length > 0) {
      lines.push('\n### Text References');
      data.associatedTextReferences.forEach((ref, index) => {
        lines.push(`${index + 1}. "${ref.snippet}" (Line ${ref.location_pointer})`);
      });
    }
    lines.push('');
  }

  // Question stem
  lines.push('## Question\n');
  const cleanQuestion = data.questionText
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'");
  lines.push(cleanQuestion);
  lines.push('');

  // Options
  lines.push('### Options\n');
  data.options.forEach((option) => {
    const cleanOption = option.text
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");

    const isCorrect = data.correctAnswer === option.letter;
    const marker = isCorrect ? ' ✓' : '';
    lines.push(`**${option.letter})** ${cleanOption}${marker}`);

    // Add commentary if available and this is the correct answer
    if (isCorrect && option.commentary) {
      const cleanCommentary = option.commentary
        .replace(/<[^>]*>/g, '')
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");
      lines.push(`   *Commentary: ${cleanCommentary}*`);
    }
  });

  if (data.correctAnswer) {
    lines.push(`\n**Correct Answer**: ${data.correctAnswer}`);
  }

  return lines.join('\n');
}

/**
 * Copy text to clipboard and return success status
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    console.error('Failed to copy to clipboard:', err);
    return false;
  }
}
