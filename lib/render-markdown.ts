import { marked } from 'marked';
import { renderContent } from './render-content';

// Configure marked options
marked.setOptions({
  breaks: true, // Convert \n to <br>
  gfm: true, // GitHub Flavored Markdown
});

/**
 * Renders markdown content with support for:
 * - Basic markdown syntax (bold, italic, links, lists, etc.)
 * - LaTeX expressions (via renderContent)
 * - HTML sanitization
 */
export function renderMarkdown(content: string): string {
  if (!content) return '';

  try {
    // First, convert markdown to HTML synchronously
    const markdownHtml = marked.parse(content, { async: false }) as string;

    // Then process with renderContent to handle LaTeX and sanitize
    return renderContent(markdownHtml);
  } catch (error) {
    console.error('Error rendering markdown:', error);
    // Fall back to just rendering content without markdown
    return renderContent(content);
  }
}

/**
 * Returns an object that can be spread into dangerouslySetInnerHTML
 * for markdown content
 */
export function getRenderedMarkdown(content: string): {
  dangerouslySetInnerHTML: { __html: string };
  'data-rendered-content': boolean;
} {
  return {
    dangerouslySetInnerHTML: {
      __html: renderMarkdown(content),
    },
    'data-rendered-content': true,
  };
}
