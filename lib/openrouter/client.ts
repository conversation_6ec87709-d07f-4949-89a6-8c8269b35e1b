import OpenAI from 'openai';

export const createOpenRouterClient = (apiKey: string) => {
  return new OpenAI({
    baseURL: 'https://openrouter.ai/api/v1',
    apiKey,
    defaultHeaders: {
      'HTTP-Referer':
        typeof window !== 'undefined' ? window.location.origin : 'https://memo-app.com',
      'X-Title': 'Memo Learning App',
    },
  });
};

// Allow any string as model, but provide common ones for autocomplete
export type OpenRouterModel = string;

export const DEFAULT_MODEL: OpenRouterModel = 'openai/gpt-4o-mini';

export interface ModelConfig {
  value: string;
  label: string;
  category?: string;
  enabled?: boolean;
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  reasoning?: string; // Reasoning/thinking content from the model
  metadata?: {
    model?: string;
    usage?: {
      promptTokens?: number;
      completionTokens?: number;
      totalTokens?: number;
      cost?: number;
      reasoningTokens?: number;
      cachedTokens?: number;
    };
    latency?: {
      timeToFirstToken?: number;
      totalTime?: number;
    };
    performance?: {
      tokensPerSecond?: number;
    };
    generationId?: string;
    timestamp?: number;
  };
}

export interface ChatCompletionOptions {
  model?: OpenRouterModel;
  messages: ChatMessage[];
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  reasoning?: {
    enabled?: boolean;
    effort?: 'low' | 'medium' | 'high';
    maxTokens?: number;
    exclude?: boolean;
  };
}
