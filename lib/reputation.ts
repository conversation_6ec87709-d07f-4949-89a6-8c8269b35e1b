import { db } from '@/db';
import { users, userReputationHistory } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

// Reputation point values for different actions
export const REPUTATION_POINTS = {
  COMMENT_UPVOTED: 2,
  COMMENT_DOWNVOTED: -1,
  QUESTION_ANSWERED_GOOD: 5,
  QUESTION_ANSWERED_EASY: 5,
  DAILY_STREAK_7: 10,
  FIRST_COMMENT: 1,
  COMMENT_DELETED_BY_MOD: -10,
} as const;

export type ReputationReason =
  | 'comment_upvoted'
  | 'comment_downvoted'
  | 'question_answered_good'
  | 'question_answered_easy'
  | 'daily_streak_7'
  | 'first_comment'
  | 'comment_deleted_by_mod';

export interface ReputationUpdate {
  userId: string;
  change: number;
  reason: ReputationReason;
  relatedId?: string;
  relatedType?: string;
}

/**
 * Calculate reputation change based on action
 */
export function calculateReputationChange(reason: ReputationReason): number {
  switch (reason) {
    case 'comment_upvoted':
      return REPUTATION_POINTS.COMMENT_UPVOTED;
    case 'comment_downvoted':
      return REPUTATION_POINTS.COMMENT_DOWNVOTED;
    case 'question_answered_good':
      return REPUTATION_POINTS.QUESTION_ANSWERED_GOOD;
    case 'question_answered_easy':
      return REPUTATION_POINTS.QUESTION_ANSWERED_EASY;
    case 'daily_streak_7':
      return REPUTATION_POINTS.DAILY_STREAK_7;
    case 'first_comment':
      return REPUTATION_POINTS.FIRST_COMMENT;
    case 'comment_deleted_by_mod':
      return REPUTATION_POINTS.COMMENT_DELETED_BY_MOD;
    default:
      return 0;
  }
}

/**
 * Update user reputation and log the change
 */
export async function updateUserReputation(
  update: ReputationUpdate,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  tx?: PostgresJsDatabase<any>
): Promise<void> {
  if (!tx) {
    await db.transaction(async (transaction) => {
      await updateUserReputationInTransaction(update, transaction);
    });
  } else {
    await updateUserReputationInTransaction(update, tx);
  }
}

async function updateUserReputationInTransaction(
  update: ReputationUpdate,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  tx: PostgresJsDatabase<any>
): Promise<void> {
  const { userId, change, reason, relatedId, relatedType } = update;

  // Update user reputation
  await tx
    .update(users)
    .set({
      reputation: sql`${users.reputation} + ${change}`,
    })
    .where(eq(users.id, userId));

  // Log the reputation change
  await tx.insert(userReputationHistory).values({
    userId,
    change,
    reason,
    relatedId,
    relatedType,
  });
}

/**
 * Get user's current reputation
 */
export async function getUserReputation(userId: string): Promise<number> {
  const [user] = await db
    .select({ reputation: users.reputation })
    .from(users)
    .where(eq(users.id, userId))
    .limit(1);

  return user?.reputation ?? 0;
}

/**
 * Get user's reputation history
 */
export async function getUserReputationHistory(userId: string) {
  return await db
    .select()
    .from(userReputationHistory)
    .where(eq(userReputationHistory.userId, userId))
    .orderBy(sql`${userReputationHistory.createdAt} DESC`);
}
