import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { checkUsageLimit, incrementUsage } from '@/lib/auth/rbac';
import { FEATURES } from '@/lib/auth/features';

export type UsageResource =
  | 'questionsAnswered'
  | 'simuladosCreated'
  | 'videosWatched'
  | 'pdfsDownloaded'
  | 'aiGeneration'
  | 'aiEmbedding';
export type UsageLimitResource =
  | 'questionsPerDay'
  | 'simuladosPerMonth'
  | 'videosPerMonth'
  | 'pdfsPerMonth'
  | 'aiGenerationsPerDay'
  | 'aiEmbeddingsPerDay';

/**
 * Middleware to track and limit usage for freemium features
 */
export function trackUsage(resource: UsageResource, limitResource: UsageLimitResource) {
  return async (req: NextRequest, handler: () => Promise<NextResponse>) => {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has unlimited access
    const hasUnlimitedAccess =
      session.user.features?.includes(FEATURES.UNLIMITED_QUESTIONS) || session.user.tier !== 'free';

    if (!hasUnlimitedAccess) {
      // Check usage limit
      const { allowed, current, limit } = await checkUsageLimit(session.user.id, limitResource);

      if (!allowed) {
        return NextResponse.json(
          {
            error: 'Usage limit exceeded',
            message: `You have reached your ${limitResource} limit (${current}/${limit})`,
            current,
            limit,
            resource: limitResource,
          },
          { status: 429 }
        );
      }
    }

    // Execute the handler
    const response = await handler();

    // If successful, increment usage
    if (response.status >= 200 && response.status < 300) {
      await incrementUsage(session.user.id, resource);
    }

    return response;
  };
}

/**
 * Middleware to check if user has access to a premium resource
 */
export function requirePremiumResource(feature: keyof typeof FEATURES) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return async (_req: NextRequest) => {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const hasFeature = session.user.features?.includes(FEATURES[feature]);

    if (!hasFeature) {
      return NextResponse.json(
        {
          error: 'Premium feature required',
          message: `This feature requires ${feature} access`,
          requiredFeature: FEATURES[feature],
          userTier: session.user.tier,
        },
        { status: 403 }
      );
    }

    return null; // Continue to handler
  };
}

/**
 * Get usage statistics for the current period
 */
export async function getUserUsageStats(userId: string) {
  const resources: UsageLimitResource[] = [
    'questionsPerDay',
    'simuladosPerMonth',
    'videosPerMonth',
    'pdfsPerMonth',
    'aiGenerationsPerDay',
    'aiEmbeddingsPerDay',
  ];

  const stats: Record<
    string,
    {
      current: number;
      limit: number | null;
      remaining: number | null;
      unlimited: boolean;
    }
  > = {};

  for (const resource of resources) {
    const { current, limit } = await checkUsageLimit(userId, resource);
    stats[resource] = {
      current,
      limit,
      remaining: limit ? limit - current : null,
      unlimited: limit === null,
    };
  }

  return stats;
}

/**
 * Example usage in API routes:
 *
 * // For answering questions
 * export async function POST(req: NextRequest) {
 *   return trackUsage('questionsAnswered', 'questionsPerDay')(req, async () => {
 *     // Your question answering logic here
 *     return NextResponse.json({ success: true });
 *   });
 * }
 *
 * // For accessing premium features
 * export async function GET(req: NextRequest) {
 *   const check = await requirePremiumResource('VIDEO_EXPLANATIONS')(req);
 *   if (check) return check;
 *
 *   // Your video serving logic here
 *   return NextResponse.json({ videoUrl: '...' });
 * }
 */
