# FSRS Implementation for Next.js

This is a unified implementation of the FSRS (Free Spaced Repetition Scheduler) algorithm for the memo app.

## Architecture

### TypeScript Solution (ts-fsrs)

- Uses `ts-fsrs` for scheduling
- Pure TypeScript implementation
- Works in both Node.js and browser environments
- No native dependencies
- Implements FSRS6 algorithm with 21 parameters

## Key Design Decision

The FSRS implementation in this app is **question-centric** rather than user-centric:

- Each `fsrsCard` is linked to a `question` via `questionId`
- Questions are linked to users through the `questions` table
- This allows questions to be shared across users while maintaining individual progress

## Setup

### Install Dependencies

```bash
# Install the FSRS dependency
pnpm install ts-fsrs
```

## Database Schema

The implementation uses three main tables:

1. **fsrs_cards** - Current state of each flashcard (linked to questions)
2. **fsrs_review_logs** - History of all reviews (linked to cards)
3. **fsrs_parameters** - User-specific FSRS parameters (linked to users)

## API Endpoints

- `POST /api/fsrs/review` - Review a card with a rating
- `POST /api/fsrs/schedule` - Get scheduling options for a card
- `GET /api/study/due-cards` - Get cards due for review

## Usage Example

```typescript
// Review a card
const response = await fetch('/api/fsrs/review', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    cardId,
    rating, // 1-4 (Again, Hard, Good, Easy)
    seed, // Optional: for consistent fuzz
  }),
});

// Get scheduling options
const response = await fetch('/api/fsrs/schedule', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    cardId,
    seed, // Optional: for consistent fuzz
  }),
});
```

## Service Architecture

The `FSRSService` handles all FSRS operations:

- **Scheduling**: Calculate next review dates based on ratings
- **State Management**: Track card states (New, Learning, Review, Relearning)
- **Database Integration**: Automatic persistence of all data
- **Fuzz Factor**: Prevents cards from clustering on the same days
- **Learning Steps**: Configurable short-term intervals (default: 1m, 10m)

## Key Features

1. **Automatic Parameter Loading**: The scheduler automatically loads user-specific parameters from the database
2. **Fallback to Defaults**: If no custom parameters exist, uses FSRS6 defaults
3. **Review Logging**: All reviews are logged for future optimization
4. **State Management**: Properly tracks card states (New, Learning, Review, Relearning)
5. **Interval Preview**: Shows upcoming intervals for all ratings before the user selects one
6. **Seed-based Consistency**: Preview intervals match what gets stored using deterministic seeding

## FSRS6 Parameters

The system uses 21 parameters for the FSRS6 algorithm:

- w0-w20: Weights for different aspects of the algorithm
- requestRetention: Target retention rate (default: 0.9)
- maximumInterval: Maximum days between reviews (default: 36500)
- learningSteps: Steps in minutes for new cards (default: [1, 10])
- enableFuzz: Add randomization to prevent clustering (default: true)
- enableShortTerm: Enable short-term memory simulation (default: true)
