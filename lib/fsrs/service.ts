import {
  fsrs,
  Card,
  createEmptyCard,
  generatorParameters,
  FSRSParameters,
  Rating as FSRSRating,
  State,
  Grade,
} from 'ts-fsrs';
import { db } from '@/db';
import {
  fsrsCards,
  fsrsReviewLogs,
  fsrsParameters,
  fsrsDecks,
  fsrsDeckCards,
  questions,
} from '@/db/schema';
import { and, eq, lte, inArray } from 'drizzle-orm';
import { Rating as CustomRating, CardState } from './types';

export class FSRSService {
  private f: ReturnType<typeof fsrs>;
  private userId: string;
  private isInitialized = false;
  private params: FSRSParameters;

  constructor(userId: string) {
    this.userId = userId;
    // Initialize with FSRS6 default parameters including learning steps
    this.params = generatorParameters({
      enable_fuzz: true, // Re-enabled now that we have seed-based preview matching
      enable_short_term: true,
      maximum_interval: 36500,
      w: [
        0.212, 1.2931, 2.3065, 8.2956, 6.4133, 0.8334, 3.0194, 0.001, 1.8722, 0.1666, 0.796, 1.4835,
        0.0614, 0.2629, 1.6483, 0.6014, 1.8729, 0.5425, 0.0912, 0.0658, 0.1542,
      ],
    });
    this.f = fsrs(this.params);
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      // Load user-specific parameters
      const userParams = await db
        .select()
        .from(fsrsParameters)
        .where(eq(fsrsParameters.userId, this.userId))
        .limit(1);

      if (userParams.length > 0) {
        const p = userParams[0];
        const weights = [
          p.w0,
          p.w1,
          p.w2,
          p.w3,
          p.w4,
          p.w5,
          p.w6,
          p.w7,
          p.w8,
          p.w9,
          p.w10,
          p.w11,
          p.w12,
          p.w13,
          p.w14,
          p.w15,
          p.w16,
        ];

        // Update parameters with user weights and settings
        this.params = generatorParameters({
          ...this.params,
          w: weights,
          enable_fuzz: p.enableFuzz === 1,
          enable_short_term: p.enableShortTerm === 1,
          maximum_interval: p.maximumInterval,
          request_retention: p.requestRetention,
        });
        this.f = fsrs(this.params);
      }

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize FSRS parameters:', error);
      // Continue with defaults
      this.isInitialized = true;
    }

    // Ensure user has parameters (will create defaults if missing)
    await this.ensureUserParameters();
  }

  async getCard(cardId: string) {
    const cards = await db.select().from(fsrsCards).limit(1).where(eq(fsrsCards.id, cardId));

    if (cards.length === 0) return null;
    return cards[0];
  }

  async getCardByQuestionId(questionId: string) {
    const cards = await db
      .select()
      .from(fsrsCards)
      .where(eq(fsrsCards.questionId, questionId))
      .limit(1);

    if (cards.length === 0) return null;
    return cards[0];
  }

  async reviewCard(
    cardId: string,
    rating: CustomRating,
    seed?: string
  ): Promise<{ success: boolean; nextReview?: Date }> {
    await this.initialize();

    // Get current card state from database
    const dbCard = await this.getCard(cardId);
    if (!dbCard) {
      throw new Error('Card not found');
    }

    // Convert database card to ts-fsrs Card
    let fsrsCard: Card;
    if (dbCard.reps === 0) {
      fsrsCard = createEmptyCard();
    } else {
      fsrsCard = {
        due: dbCard.due,
        stability: dbCard.stability,
        difficulty: dbCard.difficulty,
        elapsed_days: dbCard.elapsedDays,
        scheduled_days: dbCard.scheduledDays,
        reps: dbCard.reps,
        lapses: dbCard.lapses,
        state: dbCard.state as State,
        last_review: dbCard.lastReview || undefined,
        learning_steps: dbCard.learningSteps,
      };
    }

    // Schedule card with rating
    const now = new Date();

    // Use the same seed logic as getSchedulingOptions for consistency
    const minuteTimestamp = Math.floor(now.getTime() / 60000) * 60000;
    const seedString = seed || `${cardId}-${minuteTimestamp}`;

    // Create a simple hash from the seed to get a consistent offset
    let hash = 0;
    for (let i = 0; i < seedString.length; i++) {
      const char = seedString.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }

    // Use the hash to create a consistent time (using minute timestamp + offset)
    // This ensures preview and review use the exact same time when within the same minute
    const timeOffset = Math.abs(hash) % 60000; // offset within the minute
    const reviewTime = new Date(minuteTimestamp + timeOffset);

    const schedulingCards = this.f.repeat(fsrsCard, reviewTime);

    // Map rating to ts-fsrs format
    const ratingMap = {
      [CustomRating.Again]: FSRSRating.Again,
      [CustomRating.Hard]: FSRSRating.Hard,
      [CustomRating.Good]: FSRSRating.Good,
      [CustomRating.Easy]: FSRSRating.Easy,
    };

    const fsrsRating = ratingMap[rating];
    const recordLog = schedulingCards[fsrsRating as Grade];
    const { card: newCard, log } = recordLog;

    // Update card in database
    await db
      .update(fsrsCards)
      .set({
        state: newCard.state,
        due: newCard.due,
        stability: newCard.stability,
        difficulty: newCard.difficulty,
        elapsedDays: newCard.elapsed_days,
        scheduledDays: newCard.scheduled_days,
        reps: newCard.reps,
        lapses: newCard.lapses,
        lastReview: now,
        updatedAt: now,
      })
      .where(eq(fsrsCards.id, cardId));

    // Log the review
    await db.insert(fsrsReviewLogs).values({
      cardId,
      rating: log.rating,
      state: newCard.state, // Use the new state, not the log state
      due: newCard.due,
      stability: newCard.stability,
      difficulty: newCard.difficulty,
      elapsedDays: log.elapsed_days,
      lastElapsedDays: dbCard.elapsedDays,
      scheduledDays: newCard.scheduled_days, // Use new scheduled days
      learningSteps: 0,
      review: log.review,
    });

    // Check if this is the user's first review and create parameters if needed
    if (dbCard.reps === 0) {
      await this.ensureUserParameters();
    }

    return {
      success: true,
      nextReview: newCard.due,
    };
  }

  async createCard(questionId: string): Promise<string> {
    // Create a new card for a question
    const result = await db
      .insert(fsrsCards)
      .values({
        questionId,
        state: CardState.New,
        due: new Date(),
        stability: 0,
        difficulty: 0,
        elapsedDays: 0,
        scheduledDays: 0,
        learningSteps: 0,
        reps: 0,
        lapses: 0,
      })
      .returning({ id: fsrsCards.id });

    return result[0].id;
  }

  async getDueCards(limit: number = 20) {
    const now = new Date();

    // Get user's decks
    const userDecks = await db
      .select({ id: fsrsDecks.id })
      .from(fsrsDecks)
      .where(and(eq(fsrsDecks.userId, this.userId), eq(fsrsDecks.isActive, true)));

    if (userDecks.length === 0) {
      return [];
    }

    const deckIds = userDecks.map((d) => d.id);

    // Get questions in user's decks
    const deckQuestions = await db
      .select({ questionId: fsrsDeckCards.questionId })
      .from(fsrsDeckCards)
      .where(inArray(fsrsDeckCards.deckId, deckIds));

    if (deckQuestions.length === 0) {
      return [];
    }

    const questionIds = deckQuestions.map((q) => q.questionId);

    // Get due cards for user's questions
    const dueCards = await db
      .select({
        card: fsrsCards,
        question: questions,
      })
      .from(fsrsCards)
      .innerJoin(questions, eq(fsrsCards.questionId, questions.id))
      .where(and(inArray(fsrsCards.questionId, questionIds), lte(fsrsCards.due, now)))
      .limit(limit);

    return dueCards.map((result) => ({
      ...result.card,
      question: result.question,
    }));
  }

  async getSchedulingOptions(cardId: string, seed?: string) {
    await this.initialize();

    const dbCard = await this.getCard(cardId);
    if (!dbCard) {
      throw new Error('Card not found');
    }

    // Convert database card to ts-fsrs Card
    let fsrsCard: Card;
    if (dbCard.reps === 0) {
      fsrsCard = createEmptyCard();
    } else {
      fsrsCard = {
        due: dbCard.due,
        stability: dbCard.stability,
        difficulty: dbCard.difficulty,
        elapsed_days: dbCard.elapsedDays,
        scheduled_days: dbCard.scheduledDays,
        reps: dbCard.reps,
        lapses: dbCard.lapses,
        state: dbCard.state as State,
        last_review: dbCard.lastReview || undefined,
        learning_steps: dbCard.learningSteps,
      };
    }

    // Get scheduling info for all ratings
    const now = new Date();

    // Use a deterministic seed based on cardId and current time (rounded to nearest minute)
    // This ensures the preview matches what will be stored while still allowing some variation
    const minuteTimestamp = Math.floor(now.getTime() / 60000) * 60000;
    const seedString = seed || `${cardId}-${minuteTimestamp}`;

    // Create a simple hash from the seed to get a consistent offset
    let hash = 0;
    for (let i = 0; i < seedString.length; i++) {
      const char = seedString.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }

    // Use the hash to create a consistent time (using minute timestamp + offset)
    // This ensures preview and review use the exact same time when within the same minute
    const timeOffset = Math.abs(hash) % 60000; // offset within the minute
    const schedulingTime = new Date(minuteTimestamp + timeOffset);

    // Calculate with the seeded time
    const schedulingCards = this.f.repeat(fsrsCard, schedulingTime);

    // Debug logging
    console.log('[FSRS Debug] Scheduling card with seed:', {
      cardId: cardId,
      seed: seedString,
      timeOffset: timeOffset,
      currentState: dbCard.state,
      reps: dbCard.reps,
      fuzzEnabled: this.params.enable_fuzz,
      intervals: {
        again: schedulingCards[FSRSRating.Again].card.scheduled_days,
        hard: schedulingCards[FSRSRating.Hard].card.scheduled_days,
        good: schedulingCards[FSRSRating.Good].card.scheduled_days,
        easy: schedulingCards[FSRSRating.Easy].card.scheduled_days,
      },
    });

    // Convert to API format with intervals in days
    // For learning steps (state 1), calculate interval from due dates
    // For review cards (state 2+), use scheduled_days
    const calculateInterval = (card: Card, now: Date) => {
      if (card.state === State.Learning) {
        const intervalMs = card.due.getTime() - now.getTime();
        return intervalMs / (1000 * 60 * 60 * 24);
      }
      return card.scheduled_days;
    };

    return {
      again: {
        interval: calculateInterval(schedulingCards[FSRSRating.Again].card, schedulingTime),
        due: schedulingCards[FSRSRating.Again].card.due,
        state: schedulingCards[FSRSRating.Again].card.state,
      },
      hard: {
        interval: calculateInterval(schedulingCards[FSRSRating.Hard].card, schedulingTime),
        due: schedulingCards[FSRSRating.Hard].card.due,
        state: schedulingCards[FSRSRating.Hard].card.state,
      },
      good: {
        interval: calculateInterval(schedulingCards[FSRSRating.Good].card, schedulingTime),
        due: schedulingCards[FSRSRating.Good].card.due,
        state: schedulingCards[FSRSRating.Good].card.state,
      },
      easy: {
        interval: calculateInterval(schedulingCards[FSRSRating.Easy].card, schedulingTime),
        due: schedulingCards[FSRSRating.Easy].card.due,
        state: schedulingCards[FSRSRating.Easy].card.state,
      },
    };
  }

  async getSchedulingOptionsForNewCard(questionId: string, seed?: string) {
    await this.initialize();

    // Create empty card for new questions
    const fsrsCard = createEmptyCard();

    // Get scheduling info for all ratings
    const now = new Date();

    // Use a deterministic seed based on questionId and current time (rounded to nearest minute)
    // This ensures the preview matches what will be stored while still allowing some variation
    const minuteTimestamp = Math.floor(now.getTime() / 60000) * 60000;
    const seedString = seed || `${questionId}-${minuteTimestamp}`;

    // Create a simple hash from the seed to get a consistent offset
    let hash = 0;
    for (let i = 0; i < seedString.length; i++) {
      const char = seedString.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32bit integer
    }

    // Use the hash to create a consistent time (using minute timestamp + offset)
    // This ensures preview and review use the exact same time when within the same minute
    const timeOffset = Math.abs(hash) % 60000; // offset within the minute
    const schedulingTime = new Date(minuteTimestamp + timeOffset);

    // Calculate with the seeded time
    const schedulingCards = this.f.repeat(fsrsCard, schedulingTime);

    // Debug logging
    console.log('[FSRS Debug] Scheduling new card with seed:', {
      questionId: questionId,
      seed: seedString,
      timeOffset: timeOffset,
      fuzzEnabled: this.params.enable_fuzz,
      intervals: {
        again: schedulingCards[FSRSRating.Again].card.scheduled_days,
        hard: schedulingCards[FSRSRating.Hard].card.scheduled_days,
        good: schedulingCards[FSRSRating.Good].card.scheduled_days,
        easy: schedulingCards[FSRSRating.Easy].card.scheduled_days,
      },
    });

    // Convert to API format with intervals in days
    // For learning steps (state 1), calculate interval from due dates
    // For review cards (state 2+), use scheduled_days
    const calculateInterval = (card: Card, now: Date) => {
      if (card.state === State.Learning || card.state === State.New) {
        const intervalMs = card.due.getTime() - now.getTime();
        return intervalMs / (1000 * 60 * 60 * 24);
      }
      return card.scheduled_days;
    };

    const result = {
      again: {
        interval: calculateInterval(schedulingCards[FSRSRating.Again].card, schedulingTime),
        due: schedulingCards[FSRSRating.Again].card.due,
        state: schedulingCards[FSRSRating.Again].card.state,
      },
      hard: {
        interval: calculateInterval(schedulingCards[FSRSRating.Hard].card, schedulingTime),
        due: schedulingCards[FSRSRating.Hard].card.due,
        state: schedulingCards[FSRSRating.Hard].card.state,
      },
      good: {
        interval: calculateInterval(schedulingCards[FSRSRating.Good].card, schedulingTime),
        due: schedulingCards[FSRSRating.Good].card.due,
        state: schedulingCards[FSRSRating.Good].card.state,
      },
      easy: {
        interval: calculateInterval(schedulingCards[FSRSRating.Easy].card, schedulingTime),
        due: schedulingCards[FSRSRating.Easy].card.due,
        state: schedulingCards[FSRSRating.Easy].card.state,
      },
    };

    console.log('[FSRS Debug] Final intervals for new card:', {
      questionId,
      again: result.again.interval,
      hard: result.hard.interval,
      good: result.good.interval,
      easy: result.easy.interval,
    });

    return result;
  }

  private calculateInterval(from: Date, to: Date): number {
    // Calculate interval in days
    const diffMs = to.getTime() - from.getTime();
    return diffMs / (1000 * 60 * 60 * 24);
  }

  // TODO: Implement parameter optimization with ts-fsrs
  // The ts-fsrs library has a different optimization API that needs to be integrated
  async optimizeParameters() {
    // Parameter optimization not available in ts-fsrs
    console.warn('Parameter optimization not yet implemented with ts-fsrs');
    return null;
  }

  private async ensureUserParameters() {
    // Check if user already has parameters
    const existingParams = await db
      .select()
      .from(fsrsParameters)
      .where(eq(fsrsParameters.userId, this.userId))
      .limit(1);

    if (existingParams.length === 0) {
      // Create default FSRS6 parameters for the user
      await db.insert(fsrsParameters).values({
        userId: this.userId,
        w0: 0.212,
        w1: 1.2931,
        w2: 2.3065,
        w3: 8.2956,
        w4: 6.4133,
        w5: 0.8334,
        w6: 3.0194,
        w7: 0.001,
        w8: 1.8722,
        w9: 0.1666,
        w10: 0.796,
        w11: 1.4835,
        w12: 0.0614,
        w13: 0.2629,
        w14: 1.6483,
        w15: 0.6014,
        w16: 1.8729,
        w17: 0.5425,
        w18: 0.0912,
        w19: 0.0658,
        w20: 0.1542,
        requestRetention: 0.9,
        maximumInterval: 36500,
        learningSteps: '[1, 10]', // 1 minute, 10 minutes
        enableFuzz: 1, // Enable fuzz with seed-based consistency
        enableShortTerm: 1,
      });

      console.log(`Created default FSRS parameters for user ${this.userId}`);
    }
  }
}
