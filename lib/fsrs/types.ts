/**
 * FSRS Types and Enums
 * Replaces ts-fsrs types with consistent definitions
 */

// Rating values (1-4) for ts-fsrs
export enum Rating {
  Again = 1,
  Hard = 2,
  Good = 3,
  Easy = 4,
}

// Card states (0-3) to match database schema
export enum CardState {
  New = 0,
  Learning = 1,
  Review = 2,
  Relearning = 3,
}

// Card interface matching database schema
export interface Card {
  id: string;
  questionId: string;
  due: Date;
  stability: number;
  difficulty: number;
  elapsedDays: number;
  scheduledDays: number;
  learningSteps: number;
  reps: number;
  lapses: number;
  state: CardState;
  lastReview: Date | null;
}

// Scheduling info for each rating option
export interface SchedulingInfo {
  rating: Rating;
  due: Date;
  interval: number;
  stability: number;
  difficulty: number;
  state: CardState;
}

// Review log interface
export interface ReviewLog {
  cardId: string;
  rating: Rating;
  state: CardState;
  due: Date;
  stability: number;
  difficulty: number;
  elapsedDays: number;
  lastElapsedDays: number;
  scheduledDays: number;
  learningSteps: number;
  review: Date;
}

// FSRS Parameters (21 weights)
export interface FSRSParameters {
  w: number[]; // Array of 21 weights
  requestRetention: number;
  maximumInterval: number;
  enableFuzz: boolean;
  enableShortTerm: boolean;
  learningSteps: number[]; // in minutes
}
