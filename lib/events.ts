// Custom event system for realtime updates
export const ConversationEvents = {
  CONVERSATION_UPDATED: 'conversation:updated',
  CONVERSATION_CREATED: 'conversation:created',
  CONVERSATION_DELETED: 'conversation:deleted',
  MESSAGE_ADDED: 'message:added',
} as const;

export type ConversationEventType = (typeof ConversationEvents)[keyof typeof ConversationEvents];

export interface ConversationEventDetail {
  conversationId: string;
  conversation?: unknown;
}

// Helper functions to dispatch and listen to events
export function dispatchConversationEvent(
  type: ConversationEventType,
  detail: ConversationEventDetail
) {
  window.dispatchEvent(
    new CustomEvent(type, {
      detail,
    })
  );
}

export function addConversationEventListener(
  type: ConversationEventType,
  handler: (event: CustomEvent<ConversationEventDetail>) => void
) {
  const eventHandler = handler as EventListener;
  window.addEventListener(type, eventHandler);
  return () => window.removeEventListener(type, eventHandler);
}
