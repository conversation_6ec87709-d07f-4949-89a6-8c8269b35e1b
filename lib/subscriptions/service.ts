import { db } from '@/db';
import { userSubscriptions, userFeatures, userRoles } from '@/db/schema';
import { eq, and, or, gte, lte, sql, inArray } from 'drizzle-orm';
import { SubscriptionTier, TIER_FEATURES, ROLES } from '@/lib/auth/features';

/**
 * Create a new subscription for a user
 */
export async function createSubscription(
  userId: string,
  tier: SubscriptionTier,
  trialDays: number = 0
): Promise<string> {
  const now = new Date();
  const trialEndsAt =
    trialDays > 0 ? new Date(now.getTime() + trialDays * 24 * 60 * 60 * 1000) : null;

  const [subscription] = await db
    .insert(userSubscriptions)
    .values({
      userId,
      tier,
      status: trialDays > 0 ? 'trial' : 'active',
      trialEndsAt,
    })
    .returning();

  // Apply tier features
  await applyTierFeatures(userId, tier);

  // Update user role based on tier
  if (tier !== 'free') {
    await updateUserRoleForTier(userId, tier);
  }

  return subscription.id;
}

/**
 * Upgrade a user's subscription
 */
export async function upgradeSubscription(
  userId: string,
  newTier: SubscriptionTier
): Promise<void> {
  // Cancel existing subscription
  await db
    .update(userSubscriptions)
    .set({
      status: 'cancelled',
      updatedAt: new Date(),
    })
    .where(
      and(
        eq(userSubscriptions.userId, userId),
        or(eq(userSubscriptions.status, 'active'), eq(userSubscriptions.status, 'trial'))
      )
    );

  // Create new subscription
  await createSubscription(userId, newTier);
}

/**
 * Cancel a user's subscription
 */
export async function cancelSubscription(userId: string): Promise<void> {
  const now = new Date();

  // Update subscription status
  await db
    .update(userSubscriptions)
    .set({
      status: 'cancelled',
      updatedAt: now,
    })
    .where(
      and(
        eq(userSubscriptions.userId, userId),
        or(eq(userSubscriptions.status, 'active'), eq(userSubscriptions.status, 'trial'))
      )
    );

  // Remove premium features
  await removeTierFeatures(userId);

  // Revert to free tier role
  await db
    .update(userRoles)
    .set({
      role: ROLES.STUDENT_FREE,
      updatedAt: now,
    })
    .where(eq(userRoles.userId, userId));
}

/**
 * Check and update trial status
 */
export async function checkTrialStatus(userId: string): Promise<boolean> {
  const now = new Date();

  const [subscription] = await db
    .select()
    .from(userSubscriptions)
    .where(
      and(
        eq(userSubscriptions.userId, userId),
        eq(userSubscriptions.status, 'trial'),
        lte(userSubscriptions.trialEndsAt, now)
      )
    )
    .limit(1);

  if (subscription) {
    // Trial expired, mark as expired
    await db
      .update(userSubscriptions)
      .set({
        status: 'expired',
        updatedAt: now,
      })
      .where(eq(userSubscriptions.id, subscription.id));

    // Remove features
    await removeTierFeatures(userId);

    // Revert to free tier role
    await db
      .update(userRoles)
      .set({
        role: ROLES.STUDENT_FREE,
        updatedAt: now,
      })
      .where(eq(userRoles.userId, userId));

    return true; // Trial expired
  }

  return false; // Trial still active or no trial
}

/**
 * Apply features for a subscription tier
 */
export async function applyTierFeatures(userId: string, tier: SubscriptionTier): Promise<void> {
  const features = TIER_FEATURES[tier] || [];

  // Remove all existing tier-based features
  await removeTierFeatures(userId);

  // Add new tier features
  for (const feature of features) {
    await db
      .insert(userFeatures)
      .values({
        userId,
        feature,
        grantedBy: userId, // System grant
        metadata: { source: 'subscription', tier },
      })
      .onConflictDoNothing();
  }
}

/**
 * Remove tier-based features
 */
async function removeTierFeatures(userId: string): Promise<void> {
  // Get all tier features
  const allTierFeatures = Object.values(TIER_FEATURES).flat();

  // Remove features that come from subscriptions
  await db.delete(userFeatures).where(
    and(
      eq(userFeatures.userId, userId),
      // Only remove features that are part of tier features
      // This preserves manually granted features
      inArray(userFeatures.feature, allTierFeatures)
    )
  );
}

/**
 * Update user role based on subscription tier
 */
async function updateUserRoleForTier(userId: string, tier: SubscriptionTier): Promise<void> {
  const role = tier === 'free' ? ROLES.STUDENT_FREE : ROLES.STUDENT_PREMIUM;

  await db
    .update(userRoles)
    .set({
      role,
      updatedAt: new Date(),
    })
    .where(eq(userRoles.userId, userId));
}

/**
 * Get active subscriptions expiring soon
 */
export async function getExpiringSubscriptions(days: number = 7) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);

  return await db
    .select()
    .from(userSubscriptions)
    .where(
      and(
        eq(userSubscriptions.status, 'active'),
        lte(userSubscriptions.expiresAt, futureDate),
        gte(userSubscriptions.expiresAt, new Date())
      )
    );
}

/**
 * Process expired subscriptions
 */
export async function processExpiredSubscriptions(): Promise<number> {
  const now = new Date();

  // Find expired active subscriptions
  const expired = await db
    .select()
    .from(userSubscriptions)
    .where(and(eq(userSubscriptions.status, 'active'), lte(userSubscriptions.expiresAt, now)));

  let processed = 0;

  for (const subscription of expired) {
    // Mark as expired
    await db
      .update(userSubscriptions)
      .set({
        status: 'expired',
        updatedAt: now,
      })
      .where(eq(userSubscriptions.id, subscription.id));

    // Remove features and update role
    await removeTierFeatures(subscription.userId);
    await updateUserRoleForTier(subscription.userId, 'free');

    processed++;
  }

  return processed;
}

/**
 * Get subscription statistics
 */
export async function getSubscriptionStats() {
  const stats = await db
    .select({
      tier: userSubscriptions.tier,
      status: userSubscriptions.status,
      count: sql<number>`count(*)::int`,
    })
    .from(userSubscriptions)
    .groupBy(userSubscriptions.tier, userSubscriptions.status);

  return stats;
}
