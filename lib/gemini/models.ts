export interface RateLimits {
  rpm: number; // Requests Per Minute
  tpm: number; // Tokens Per Minute
  tpd?: number; // Tokens Per Day (not all models have this)
  rpd?: number; // Requests Per Day (not all models have this)
}

export interface TierLimits {
  free: RateLimits;
  tier1: RateLimits;
  tier2?: RateLimits;
  tier3?: RateLimits;
}

export interface ModelCapabilities {
  supports_vision?: boolean;
  supports_audio?: boolean;
  supports_video?: boolean;
  supports_function_calling?: boolean;
  supports_code_execution?: boolean;
  supports_search_grounding?: boolean;
  supports_structured_output?: boolean;
  supports_native_audio?: boolean;
  supports_native_image_generation?: boolean;
  supports_tts?: boolean;
}

export interface ModelParameters {
  temperature_range?: [number, number];
  top_p_range?: [number, number];
  top_k_range?: [number, number];
  thinking_budget_range?: [number, number];
}

export interface GeminiModelConfig {
  name: string;
  description: string;
  input_token_limit: number;
  output_token_limit: number;
  rate_limits: TierLimits;
  capabilities: ModelCapabilities;
  parameters: ModelParameters;
  best_for: string[];
}

export interface ImageGenerationModelConfig {
  name: string;
  description: string;
  supported_aspect_ratios: string[];
  max_images_per_request: number;
  supports_negative_prompt: boolean;
  supports_person_generation: boolean;
  supports_watermark: boolean;
  supports_seed: boolean;
  supports_mask?: boolean;
  supports_guidance_scale?: boolean;
  supports_upscaling?: boolean;
  upscale_factors?: number[];
  supported_edit_modes?: string[];
  best_for: string[];
}

export interface VideoGenerationModelConfig {
  name: string;
  description: string;
  max_duration_seconds: number;
  supported_aspect_ratios: string[];
  supported_resolutions: string[];
  supports_motion_control: boolean;
  supports_style_transfer: boolean;
  best_for: string[];
}

export interface EmbeddingModelConfig {
  name: string;
  description: string;
  default_dimensions: number;
  max_dimensions: number;
  supports_task_type: boolean;
  task_types: string[];
  best_for: string[];
}

// Gemini Language Models
export const GEMINI_MODELS: Record<string, GeminiModelConfig> = {
  // Gemini 2.5 Series - Latest Generation
  'gemini-2.5-pro': {
    name: 'gemini-2.5-pro',
    description: 'State-of-the-art thinking model with maximum response accuracy',
    input_token_limit: 1048576,
    output_token_limit: 65536,
    rate_limits: {
      free: { rpm: 5, tpm: 250000, rpd: 100 },
      tier1: { rpm: 150, tpm: 2000000, rpd: 1000 },
      tier2: { rpm: 1000, tpm: 5000000, rpd: 50000 },
      tier3: { rpm: 2000, tpm: 8000000 },
    },
    capabilities: {
      supports_vision: true,
      supports_audio: true,
      supports_video: true,
      supports_function_calling: true,
      supports_code_execution: true,
      supports_search_grounding: true,
      supports_structured_output: true,
    },
    parameters: {
      temperature_range: [0.0, 2.0],
      top_p_range: [0.0, 1.0],
      top_k_range: [1, 100],
      thinking_budget_range: [0, 32768], // Max 32K tokens for Pro
    },
    best_for: ['Complex reasoning', 'Multimodal understanding', 'Long context tasks'],
  },
  'gemini-2.5-flash': {
    name: 'gemini-2.5-flash',
    description: 'Best model in terms of price-performance ratio',
    input_token_limit: 1048576,
    output_token_limit: 65536,
    rate_limits: {
      free: { rpm: 10, tpm: 250000, rpd: 250 },
      tier1: { rpm: 1000, tpm: 1000000, rpd: 10000 },
      tier2: { rpm: 2000, tpm: 3000000, rpd: 100000 },
      tier3: { rpm: 10000, tpm: 8000000 },
    },
    capabilities: {
      supports_vision: true,
      supports_audio: true,
      supports_video: true,
      supports_function_calling: true,
      supports_code_execution: true,
      supports_search_grounding: true,
      supports_structured_output: true,
    },
    parameters: {
      temperature_range: [0.0, 2.0],
      top_p_range: [0.0, 1.0],
      top_k_range: [1, 100],
      thinking_budget_range: [0, 24576], // Max 24K tokens for Flash
    },
    best_for: ['Large-scale processing', 'Low-latency tasks', 'Cost-effective applications'],
  },
  'gemini-2.5-flash-lite-preview-06-17': {
    name: 'gemini-2.5-flash-lite-preview-06-17',
    description: 'Optimized for cost efficiency and low latency',
    input_token_limit: 1000000,
    output_token_limit: 65536,
    rate_limits: {
      free: { rpm: 15, tpm: 300000, rpd: 300 },
      tier1: { rpm: 1500, tpm: 1500000, rpd: 15000 },
      tier2: { rpm: 3000, tpm: 4000000, rpd: 150000 },
      tier3: { rpm: 15000, tpm: 10000000 },
    },
    capabilities: {
      supports_vision: true,
      supports_audio: false,
      supports_video: false,
      supports_function_calling: true,
      supports_code_execution: true,
      supports_search_grounding: false,
      supports_structured_output: true,
    },
    parameters: {
      temperature_range: [0.0, 2.0],
      top_p_range: [0.0, 1.0],
      top_k_range: [1, 100],
      thinking_budget_range: [0, 24576], // Max 24K tokens for flash-lite
    },
    best_for: ['High-throughput tasks', 'Cost-sensitive applications', 'Simple queries'],
  },

  // Gemini 2.0 Series
  'gemini-2.0-flash-001': {
    name: 'gemini-2.0-flash-001',
    description: 'Fast multimodal model with native image generation',
    input_token_limit: 1048576,
    output_token_limit: 8192,
    rate_limits: {
      free: { rpm: 15, tpm: 1000000, rpd: 200 },
      tier1: { rpm: 2000, tpm: 4000000 },
      tier2: { rpm: 4000, tpm: 8000000 },
      tier3: { rpm: 20000, tpm: 16000000 },
    },
    capabilities: {
      supports_vision: true,
      supports_audio: true,
      supports_video: true,
      supports_function_calling: true,
      supports_code_execution: true,
      supports_search_grounding: true,
      supports_structured_output: true,
      supports_native_image_generation: true,
    },
    parameters: {
      temperature_range: [0.0, 2.0],
      top_p_range: [0.0, 1.0],
      top_k_range: [1, 40],
    },
    best_for: ['Multimodal tasks', 'Real-time applications', 'Image generation'],
  },
  'gemini-2.0-flash-exp': {
    name: 'gemini-2.0-flash-exp',
    description: 'Experimental version of Gemini 2.0 Flash',
    input_token_limit: 1048576,
    output_token_limit: 8192,
    rate_limits: {
      free: { rpm: 10, tpm: 500000, rpd: 100 },
      tier1: { rpm: 1000, tpm: 2000000 },
      tier2: { rpm: 2000, tpm: 4000000 },
      tier3: { rpm: 10000, tpm: 8000000 },
    },
    capabilities: {
      supports_vision: true,
      supports_audio: true,
      supports_video: true,
      supports_function_calling: true,
      supports_code_execution: true,
      supports_search_grounding: true,
      supports_structured_output: true,
    },
    parameters: {
      temperature_range: [0.0, 2.0],
      top_p_range: [0.0, 1.0],
      top_k_range: [1, 40],
    },
    best_for: ['Testing new features', 'Experimental workflows'],
  },

  // Specialized Models
  'gemini-2.5-flash-native-audio': {
    name: 'gemini-2.5-flash-native-audio',
    description: 'Flash model with native audio understanding capabilities',
    input_token_limit: 1048576,
    output_token_limit: 65536,
    rate_limits: {
      free: { rpm: 8, tpm: 200000, rpd: 150 },
      tier1: { rpm: 800, tpm: 1000000, rpd: 8000 },
      tier2: { rpm: 1600, tpm: 2500000, rpd: 80000 },
      tier3: { rpm: 8000, tpm: 6000000 },
    },
    capabilities: {
      supports_vision: true,
      supports_audio: true,
      supports_native_audio: true,
      supports_video: true,
      supports_function_calling: true,
      supports_code_execution: true,
      supports_search_grounding: true,
      supports_structured_output: true,
    },
    parameters: {
      temperature_range: [0.0, 2.0],
      top_p_range: [0.0, 1.0],
      top_k_range: [1, 100],
    },
    best_for: ['Audio processing', 'Voice analysis', 'Multimodal with audio focus'],
  },
  'gemini-2.5-flash-preview-tts': {
    name: 'gemini-2.5-flash-preview-tts',
    description: 'Flash model with text-to-speech capabilities',
    input_token_limit: 1048576,
    output_token_limit: 65536,
    rate_limits: {
      free: { rpm: 5, tpm: 150000, rpd: 100 },
      tier1: { rpm: 500, tpm: 800000, rpd: 5000 },
      tier2: { rpm: 1000, tpm: 2000000, rpd: 50000 },
      tier3: { rpm: 5000, tpm: 5000000 },
    },
    capabilities: {
      supports_vision: true,
      supports_audio: true,
      supports_tts: true,
      supports_video: true,
      supports_function_calling: true,
      supports_code_execution: true,
      supports_search_grounding: true,
      supports_structured_output: true,
    },
    parameters: {
      temperature_range: [0.0, 2.0],
      top_p_range: [0.0, 1.0],
      top_k_range: [1, 100],
    },
    best_for: ['Voice generation', 'Audio content creation', 'Accessibility features'],
  },
  'gemini-2.5-pro-preview-tts': {
    name: 'gemini-2.5-pro-preview-tts',
    description: 'Pro model with text-to-speech capabilities',
    input_token_limit: 1048576,
    output_token_limit: 65536,
    rate_limits: {
      free: { rpm: 3, tpm: 100000, rpd: 50 },
      tier1: { rpm: 100, tpm: 1500000, rpd: 800 },
      tier2: { rpm: 800, tpm: 4000000, rpd: 40000 },
      tier3: { rpm: 1600, tpm: 7000000 },
    },
    capabilities: {
      supports_vision: true,
      supports_audio: true,
      supports_tts: true,
      supports_video: true,
      supports_function_calling: true,
      supports_code_execution: true,
      supports_search_grounding: true,
      supports_structured_output: true,
    },
    parameters: {
      temperature_range: [0.0, 2.0],
      top_p_range: [0.0, 1.0],
      top_k_range: [1, 100],
    },
    best_for: ['High-quality voice synthesis', 'Professional audio content', 'Complex audio tasks'],
  },
};

// Image Generation Models
export const IMAGE_GENERATION_MODELS: Record<string, ImageGenerationModelConfig> = {
  'imagen-3.0-generate-001': {
    name: 'imagen-3.0-generate-001',
    description: 'Latest Imagen model for high-quality image generation',
    supported_aspect_ratios: ['1:1', '16:9', '9:16', '4:3', '3:4'],
    max_images_per_request: 8,
    supports_negative_prompt: true,
    supports_person_generation: true,
    supports_watermark: true,
    supports_seed: true,
    best_for: ['Photorealistic images', 'Creative artwork', 'Product visualization'],
  },
  'imagen-3.0-capability-001': {
    name: 'imagen-3.0-capability-001',
    description: 'Imagen model for image editing and enhancement',
    supported_edit_modes: ['inpaint-insert', 'inpaint-remove', 'outpaint'],
    supported_aspect_ratios: ['1:1', '16:9', '9:16', '4:3', '3:4'],
    max_images_per_request: 1,
    supports_mask: true,
    supports_negative_prompt: true,
    supports_guidance_scale: true,
    supports_upscaling: true,
    upscale_factors: [2, 4],
    supports_person_generation: false,
    supports_watermark: true,
    supports_seed: true,
    best_for: ['Image editing', 'Object removal', 'Image extension', 'Resolution enhancement'],
  },
  'imagen-4': {
    name: 'imagen-4',
    description: 'Next-generation image generation model',
    supported_aspect_ratios: ['1:1', '16:9', '9:16', '4:3', '3:4'],
    max_images_per_request: 8,
    supports_negative_prompt: true,
    supports_person_generation: true,
    supports_watermark: true,
    supports_seed: true,
    best_for: ['Ultra-high quality images', 'Advanced creative tasks'],
  },
};

// Video Generation Models
export const VIDEO_GENERATION_MODELS: Record<string, VideoGenerationModelConfig> = {
  'veo-2': {
    name: 'veo-2',
    description: 'Advanced video generation model',
    max_duration_seconds: 60,
    supported_aspect_ratios: ['16:9', '9:16', '1:1'],
    supported_resolutions: ['720p', '1080p', '4K'],
    supports_motion_control: true,
    supports_style_transfer: true,
    best_for: ['Short video creation', 'Animation', 'Video effects'],
  },
};

// Embedding Models
export const EMBEDDING_MODELS: Record<string, EmbeddingModelConfig> = {
  'text-embedding-preview-0815': {
    name: 'text-embedding-preview-0815',
    description: 'Latest text embedding model',
    default_dimensions: 768,
    max_dimensions: 768,
    supports_task_type: true,
    task_types: [
      'RETRIEVAL_QUERY',
      'RETRIEVAL_DOCUMENT',
      'SEMANTIC_SIMILARITY',
      'CLASSIFICATION',
      'CLUSTERING',
    ],
    best_for: ['Semantic search', 'Document similarity', 'Text classification'],
  },
  'text-embedding-004': {
    name: 'text-embedding-004',
    description: 'Stable text embedding model',
    default_dimensions: 768,
    max_dimensions: 768,
    supports_task_type: true,
    task_types: [
      'RETRIEVAL_QUERY',
      'RETRIEVAL_DOCUMENT',
      'SEMANTIC_SIMILARITY',
      'CLASSIFICATION',
      'CLUSTERING',
    ],
    best_for: ['Production deployments', 'Semantic search', 'RAG applications'],
  },
};

// Model Aliases for Convenience
export const MODEL_ALIASES: Record<string, string> = {
  flash: 'gemini-2.0-flash-001',
  pro: 'gemini-2.5-pro',
  'flash-latest': 'gemini-2.5-flash',
  'flash-lite': 'gemini-2.5-flash-lite-preview-06-17',
  'pro-latest': 'gemini-2.5-pro',
  imagen: 'imagen-3.0-generate-001',
  'imagen-edit': 'imagen-3.0-capability-001',
  embedding: 'text-embedding-preview-0815',
  'embedding-stable': 'text-embedding-004',
};

// Batch Mode Limits
export const BATCH_MODE_LIMITS = {
  concurrent_requests: 100,
  input_file_size_limit: '2GB',
  file_storage_limit: '20GB',
  enqueued_tokens_by_tier: {
    free: 0, // Batch mode not available in free tier
    tier1: 1000000000, // 1 billion tokens
    tier2: 2500000000, // 2.5 billion tokens
    tier3: 5000000000, // 5 billion tokens
  },
};

// Utility functions
export function getModelByName(name: string): GeminiModelConfig | null {
  // Check if it's an alias
  const actualName = MODEL_ALIASES[name] || name;

  // Look in GEMINI_MODELS
  if (GEMINI_MODELS[actualName]) {
    return GEMINI_MODELS[actualName];
  }

  return null;
}

export function getModelsByCapability(capability: keyof ModelCapabilities): string[] {
  return Object.entries(GEMINI_MODELS)
    .filter(([, config]) => config.capabilities[capability] === true)
    .map(([name]) => name);
}

export function getRateLimitsForTier(
  modelName: string,
  tier: 'free' | 'tier1' | 'tier2' | 'tier3'
): RateLimits | null {
  const model = getModelByName(modelName);
  if (!model) return null;

  return model.rate_limits[tier] || null;
}

export function getModelsByBestUse(useCase: string): string[] {
  return Object.entries(GEMINI_MODELS)
    .filter(([, config]) =>
      config.best_for.some((use) => use.toLowerCase().includes(useCase.toLowerCase()))
    )
    .map(([name]) => name);
}

export function isModelAvailableForTier(
  modelName: string,
  tier: 'free' | 'tier1' | 'tier2' | 'tier3'
): boolean {
  const model = getModelByName(modelName);
  if (!model) return false;

  return model.rate_limits[tier] !== undefined;
}

export function getModelThinkingBudgetRange(modelName: string): [number, number] | null {
  const model = getModelByName(modelName);
  if (!model || !model.parameters.thinking_budget_range) return null;

  return model.parameters.thinking_budget_range;
}
