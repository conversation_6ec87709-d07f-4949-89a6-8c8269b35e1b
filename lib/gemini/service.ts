import { GoogleGenAI, HarmCategory, HarmBlockThreshold } from '@google/genai';
import type {
  GeminiConfig,
  GeminiGenerateRequest,
  GeminiGenerateResponse,
  GeminiEmbedRequest,
  GeminiEmbedResponse,
  GeminiError,
  GeminiService,
  ModelInfo,
  RateLimitInfo,
  UserTier,
  FileMetadata,
  UploadFileRequest,
} from './types';
import {
  GEMINI_MODELS,
  MODEL_ALIASES,
  getModelByName,
  getRateLimitsForTier,
  EMBEDDING_MODELS,
} from './models';

class GeminiServiceImpl implements GeminiService {
  private genAI: GoogleGenAI;
  private config: GeminiConfig;

  constructor(config: GeminiConfig) {
    if (!config.apiKey) {
      throw new Error('Google Generative AI API key is required');
    }

    this.config = config;
    this.genAI = new GoogleGenAI({ apiKey: config.apiKey });

    // Store model names for later use
    this.config.model = config.model || 'gemini-2.5-flash';
    this.config.embeddingModel = config.embeddingModel || 'text-embedding-preview-0815';
  }

  async generateContent(request: GeminiGenerateRequest): Promise<GeminiGenerateResponse> {
    try {
      const generationConfig = {
        temperature: request.temperature ?? 0.7,
        topK: request.topK ?? 40,
        topP: request.topP ?? 0.95,
        maxOutputTokens: request.maxOutputTokens ?? 8192,
      };

      const modelName = request.model || this.config.model || 'gemini-2.5-flash';

      // Build config with thinking support for 2.5 models
      const config: {
        systemInstruction?: string;
        temperature?: number;
        maxOutputTokens?: number;
        topP?: number;
        topK?: number;
        safetySettings: Array<{ category: HarmCategory; threshold: HarmBlockThreshold }>;
        thinkingConfig?: {
          includeThoughts: boolean;
          thinkingBudget: number;
        };
      } = {
        systemInstruction: request.systemInstruction,
        ...generationConfig,
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
          },
          {
            category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
          },
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
          },
        ],
      };

      // Enable thinking for models that support it
      const modelConfig = getModelByName(modelName);
      if (modelConfig?.parameters?.thinking_budget_range && !config.thinkingConfig) {
        config.thinkingConfig = {
          includeThoughts: false,
          thinkingBudget: modelConfig.parameters.thinking_budget_range[1], // Use max by default
        };
      }

      const result = await this.genAI.models.generateContent({
        model: modelName,
        contents: request.prompt,
        config,
      });

      return {
        text: result.text || '',
        finishReason: result.candidates?.[0]?.finishReason,
        tokenCount: result.usageMetadata?.totalTokenCount,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async generateContentStream(request: GeminiGenerateRequest): Promise<GeminiGenerateResponse> {
    try {
      const generationConfig = {
        temperature: request.temperature ?? 0.7,
        topK: request.topK ?? 40,
        topP: request.topP ?? 0.95,
        maxOutputTokens: request.maxOutputTokens ?? 8192,
      };

      const modelName = request.model || this.config.model || 'gemini-2.5-flash';

      const stream = await this.genAI.models.generateContentStream({
        model: modelName,
        contents: request.prompt,
        config: {
          systemInstruction: request.systemInstruction,
          ...generationConfig,
        },
      });

      let fullText = '';
      let finishReason: string | undefined;
      let totalTokens: number | undefined;

      for await (const chunk of stream) {
        fullText += chunk.text || '';

        if (chunk.candidates?.[0]?.finishReason) {
          finishReason = chunk.candidates[0].finishReason;
        }

        if (chunk.usageMetadata?.totalTokenCount) {
          totalTokens = chunk.usageMetadata.totalTokenCount;
        }
      }

      return {
        text: fullText,
        finishReason,
        tokenCount: totalTokens,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async generateEmbedding(request: GeminiEmbedRequest): Promise<GeminiEmbedResponse> {
    try {
      const modelName =
        request.model || this.config.embeddingModel || 'text-embedding-preview-0815';

      const result = await this.genAI.models.embedContent({
        model: modelName,
        contents: request.text,
      });

      return {
        embedding: result.embeddings?.[0]?.values || [],
        tokenCount: undefined, // EmbedContentResponse doesn't include usage metadata
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  getModel(): string {
    return this.config.model || 'gemini-2.5-flash';
  }

  getModelInfo(modelName?: string): ModelInfo | null {
    const name = modelName || this.config.model || 'gemini-2.5-flash';
    const actualName = MODEL_ALIASES[name] || name;
    const modelConfig = getModelByName(actualName);

    if (!modelConfig) {
      // Check if it's an embedding model
      const embeddingModel = EMBEDDING_MODELS[actualName];
      if (embeddingModel) {
        return {
          name: embeddingModel.name,
          description: embeddingModel.description,
          inputTokenLimit: 3072, // Standard limit for embeddings
          outputTokenLimit: 0, // Embeddings don't have output tokens
          rateLimits: {
            free: { rpm: 60, tpm: 1000000 },
            tier1: { rpm: 1000, tpm: 5000000 },
            tier2: { rpm: 2000, tpm: 10000000 },
            tier3: { rpm: 5000, tpm: 20000000 },
          },
          capabilities: {
            supportsVision: false,
            supportsAudio: false,
            supportsVideo: false,
            supportsFunctionCalling: false,
            supportsCodeExecution: false,
            supportsSearchGrounding: false,
            supportsStructuredOutput: false,
          },
        };
      }
      return null;
    }

    return {
      name: modelConfig.name,
      description: modelConfig.description,
      inputTokenLimit: modelConfig.input_token_limit,
      outputTokenLimit: modelConfig.output_token_limit,
      rateLimits: {
        free: modelConfig.rate_limits.free,
        tier1: modelConfig.rate_limits.tier1,
        tier2: modelConfig.rate_limits.tier2,
        tier3: modelConfig.rate_limits.tier3,
      },
      capabilities: {
        supportsVision: modelConfig.capabilities.supports_vision,
        supportsAudio: modelConfig.capabilities.supports_audio,
        supportsVideo: modelConfig.capabilities.supports_video,
        supportsFunctionCalling: modelConfig.capabilities.supports_function_calling,
        supportsCodeExecution: modelConfig.capabilities.supports_code_execution,
        supportsSearchGrounding: modelConfig.capabilities.supports_search_grounding,
        supportsStructuredOutput: modelConfig.capabilities.supports_structured_output,
      },
    };
  }

  checkRateLimit(modelName: string, tier: UserTier): RateLimitInfo | null {
    return getRateLimitsForTier(modelName, tier);
  }

  async uploadFile(request: UploadFileRequest): Promise<FileMetadata> {
    try {
      // Convert Buffer to Blob if necessary
      let fileToUpload: Blob | string;
      if (Buffer.isBuffer(request.file)) {
        fileToUpload = new Blob([request.file], { type: request.mimeType });
      } else {
        fileToUpload = request.file;
      }

      const uploadResponse = await this.genAI.files.upload({
        file: fileToUpload,
        config: {
          mimeType: request.mimeType,
          displayName: request.displayName,
        },
      });

      // Wait for file to be processed
      const fileName = uploadResponse.name;
      if (!fileName) {
        throw new Error('No file name returned from upload');
      }

      let file = await this.genAI.files.get({ name: fileName });
      while (file.state === 'PROCESSING') {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        file = await this.genAI.files.get({ name: fileName });
      }

      if (file.state === 'FAILED') {
        throw new Error(`File processing failed: ${file.error?.message || 'Unknown error'}`);
      }

      return {
        uri: file.uri || file.name || '',
        mimeType: file.mimeType || '',
        displayName: file.displayName || '',
        sizeBytes: file.sizeBytes || '',
        createTime: file.createTime || '',
        updateTime: file.updateTime || '',
        expirationTime: file.expirationTime || '',
        state: (file.state as FileMetadata['state']) || 'PROCESSING',
        error: file.error
          ? {
              message: file.error.message || '',
              code: Number(file.error.code) || 0,
            }
          : undefined,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getFile(fileUri: string): Promise<FileMetadata> {
    try {
      const file = await this.genAI.files.get({ name: fileUri });
      return {
        uri: file.uri || file.name || '',
        mimeType: file.mimeType || '',
        displayName: file.displayName || '',
        sizeBytes: file.sizeBytes || '',
        createTime: file.createTime || '',
        updateTime: file.updateTime || '',
        expirationTime: file.expirationTime || '',
        state: (file.state as FileMetadata['state']) || 'PROCESSING',
        error: file.error
          ? {
              message: file.error.message || '',
              code: Number(file.error.code) || 0,
            }
          : undefined,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async deleteFile(fileUri: string): Promise<void> {
    try {
      await this.genAI.files.delete({ name: fileUri });
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async listFiles(): Promise<FileMetadata[]> {
    try {
      const response = await this.genAI.files.list({});
      const files: FileMetadata[] = [];
      for await (const file of response) {
        files.push({
          uri: file.uri || file.name || '',
          mimeType: file.mimeType || '',
          displayName: file.displayName || '',
          sizeBytes: file.sizeBytes || '',
          createTime: file.createTime || '',
          updateTime: file.updateTime || '',
          expirationTime: file.expirationTime || '',
          state: (file.state as FileMetadata['state']) || 'PROCESSING',
          error: file.error
            ? {
                message: file.error.message || '',
                code: Number(file.error.code) || 0,
              }
            : undefined,
        });
      }
      return files;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async generateContentWithFile(
    fileUri: string,
    prompt: string,
    systemInstruction?: string,
    modelName?: string,
    params?: {
      temperature?: number;
      topP?: number;
      topK?: number;
      thinkingBudget?: number;
    }
  ): Promise<GeminiGenerateResponse> {
    try {
      const model = modelName || this.config.model || 'gemini-2.5-flash';

      // Build generation config with custom parameters
      const config: {
        systemInstruction?: string;
        temperature: number;
        topP: number;
        topK: number;
        maxOutputTokens: number;
        thinkingConfig?: {
          includeThoughts: boolean;
          thinkingBudget: number;
        };
      } = {
        systemInstruction,
        temperature: params?.temperature ?? 0.7,
        topP: params?.topP ?? 0.95,
        topK: params?.topK ?? 40,
        maxOutputTokens: 65536,
      };

      // Enable thinking for models that support it
      const modelConfig = getModelByName(model);
      if (
        modelConfig?.parameters?.thinking_budget_range &&
        params?.thinkingBudget !== undefined &&
        params.thinkingBudget > 0
      ) {
        config.thinkingConfig = {
          includeThoughts: false, // Don't include thoughts in response
          thinkingBudget: Math.min(
            params.thinkingBudget,
            modelConfig.parameters.thinking_budget_range[1]
          ),
        };
      }

      const result = await this.genAI.models.generateContent({
        model,
        contents: [
          {
            fileData: {
              mimeType: 'application/pdf',
              fileUri: fileUri,
            },
          },
          { text: prompt },
        ],
        config,
      });

      return {
        text: result.text || '',
        finishReason: result.candidates?.[0]?.finishReason,
        tokenCount: result.usageMetadata?.totalTokenCount,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async generateContentWithFiles(
    fileUris: string[],
    prompt: string,
    systemInstruction?: string,
    modelName?: string,
    params?: {
      temperature?: number;
      topP?: number;
      topK?: number;
      thinkingBudget?: number;
    }
  ): Promise<GeminiGenerateResponse> {
    try {
      const model = modelName || this.config.model || 'gemini-2.5-flash';

      // Build generation config with custom parameters
      const config: {
        systemInstruction?: string;
        temperature: number;
        topP: number;
        topK: number;
        maxOutputTokens: number;
        thinkingConfig?: {
          includeThoughts: boolean;
          thinkingBudget: number;
        };
      } = {
        systemInstruction,
        temperature: params?.temperature ?? 0.7,
        topP: params?.topP ?? 0.95,
        topK: params?.topK ?? 40,
        maxOutputTokens: 65536,
      };

      // Enable thinking for models that support it
      const modelConfig = getModelByName(model);
      if (
        modelConfig?.parameters?.thinking_budget_range &&
        params?.thinkingBudget !== undefined &&
        params.thinkingBudget > 0
      ) {
        config.thinkingConfig = {
          includeThoughts: false,
          thinkingBudget: Math.min(
            params.thinkingBudget,
            modelConfig.parameters.thinking_budget_range[1]
          ),
        };
      }

      // Create file parts for all uploaded files
      const fileParts = fileUris.map((uri) => ({
        fileData: {
          mimeType: 'application/pdf',
          fileUri: uri,
        },
      }));

      // Generate content with all files
      const result = await this.genAI.models.generateContent({
        model,
        contents: [prompt, ...fileParts],
        config,
      });

      // Handle thinking models
      // TODO: Enable when thinking property is available in the SDK
      // if (config.thinkingConfig && result.candidates?.[0]?.content?.thinking?.text) {
      //   console.log('[Gemini] Thinking used:', result.candidates[0].content.thinking.text.length, 'characters');
      // }

      return {
        text: result.text || '',
        finishReason: result.candidates?.[0]?.finishReason,
        tokenCount: result.usageMetadata?.totalTokenCount,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  private handleError(error: unknown): GeminiError {
    const errorObj = error as Error & { code?: string; status?: number };

    if (errorObj.message?.includes('API key')) {
      return {
        message: 'Invalid or missing API key',
        code: 'INVALID_API_KEY',
        status: 401,
      };
    }

    if (errorObj.message?.includes('rate limit')) {
      return {
        message: 'Rate limit exceeded',
        code: 'RATE_LIMIT_EXCEEDED',
        status: 429,
      };
    }

    if (errorObj.message?.includes('quota')) {
      return {
        message: 'Quota exceeded',
        code: 'QUOTA_EXCEEDED',
        status: 429,
      };
    }

    return {
      message: errorObj.message || 'An unknown error occurred',
      code: errorObj.code || 'UNKNOWN_ERROR',
      status: errorObj.status || 500,
    };
  }
}

// Factory function to create a GeminiService instance
// Re-export commonly used items
export { GEMINI_MODELS, MODEL_ALIASES, getModelByName, getRateLimitsForTier };

export function getGeminiService(config?: GeminiConfig): GeminiService {
  const apiKey = config?.apiKey || process.env.GOOGLE_GENAI_API_KEY;
  if (!apiKey) {
    throw new Error(
      'Google Generative AI API key is required. Set GOOGLE_GENAI_API_KEY environment variable or provide it in the config.'
    );
  }

  return new GeminiServiceImpl({
    ...config,
    apiKey,
  });
}
