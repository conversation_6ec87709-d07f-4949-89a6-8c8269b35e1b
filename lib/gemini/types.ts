// Remove old unused import

export interface GeminiConfig {
  apiKey: string;
  model?: string;
  embeddingModel?: string;
  generationConfig?: GenerationConfig;
}

export interface GenerationConfig {
  temperature?: number;
  topK?: number;
  topP?: number;
  maxOutputTokens?: number;
  stopSequences?: string[];
  thinkingConfig?: {
    includeThoughts?: boolean;
    thinkingBudget?: number;
  };
}

export interface GeminiGenerateRequest {
  prompt: string;
  systemInstruction?: string;
  temperature?: number;
  maxOutputTokens?: number;
  topK?: number;
  topP?: number;
  model?: string;
}

export interface GeminiEmbedRequest {
  text: string;
  model?: string;
}

export interface GeminiGenerateResponse {
  text: string;
  finishReason?: string;
  tokenCount?: number;
}

export interface GeminiEmbedResponse {
  embedding: number[];
  tokenCount?: number;
}

export interface SafetyRating {
  category: string;
  probability: string;
}

export interface GeminiError {
  message: string;
  code?: string;
  status?: number;
}

export type GeminiModelName =
  | 'gemini-2.5-pro'
  | 'gemini-2.5-flash'
  | 'gemini-2.5-flash-lite-preview-06-17'
  | 'gemini-2.0-flash-001'
  | 'gemini-2.0-flash-exp'
  | 'gemini-2.5-flash-native-audio'
  | 'gemini-2.5-flash-preview-tts'
  | 'gemini-2.5-pro-preview-tts'
  | 'text-embedding-preview-0815'
  | 'text-embedding-004';

export type ImageGenerationModelName =
  | 'imagen-3.0-generate-001'
  | 'imagen-3.0-capability-001'
  | 'imagen-4';

export type VideoGenerationModelName = 'veo-2';

export type UserTier = 'free' | 'tier1' | 'tier2' | 'tier3';

export interface RateLimitInfo {
  rpm: number;
  tpm: number;
  tpd?: number;
  rpd?: number;
}

export interface ModelInfo {
  name: string;
  description: string;
  inputTokenLimit: number;
  outputTokenLimit: number;
  rateLimits: {
    free: RateLimitInfo;
    tier1: RateLimitInfo;
    tier2?: RateLimitInfo;
    tier3?: RateLimitInfo;
  };
  capabilities: {
    supportsVision?: boolean;
    supportsAudio?: boolean;
    supportsVideo?: boolean;
    supportsFunctionCalling?: boolean;
    supportsCodeExecution?: boolean;
    supportsSearchGrounding?: boolean;
    supportsStructuredOutput?: boolean;
  };
}

export interface FileMetadata {
  uri: string;
  mimeType: string;
  displayName?: string;
  sizeBytes?: string;
  createTime?: string;
  updateTime?: string;
  expirationTime?: string;
  state?: 'PROCESSING' | 'ACTIVE' | 'FAILED';
  error?: {
    message: string;
    code: number;
  };
}

export interface UploadFileRequest {
  file: Buffer | Blob | string;
  mimeType: string;
  displayName?: string;
}

export interface GeminiService {
  generateContent(request: GeminiGenerateRequest): Promise<GeminiGenerateResponse>;
  generateEmbedding(request: GeminiEmbedRequest): Promise<GeminiEmbedResponse>;
  getModel(): string;
  getModelInfo(modelName?: string): ModelInfo | null;
  checkRateLimit(modelName: string, tier: UserTier): RateLimitInfo | null;
  uploadFile(request: UploadFileRequest): Promise<FileMetadata>;
  getFile(fileUri: string): Promise<FileMetadata>;
  deleteFile(fileUri: string): Promise<void>;
  listFiles(): Promise<FileMetadata[]>;
  generateContentWithFile(
    fileUri: string,
    prompt: string,
    systemInstruction?: string,
    modelName?: string,
    params?: {
      temperature?: number;
      topP?: number;
      topK?: number;
      thinkingBudget?: number;
    }
  ): Promise<GeminiGenerateResponse>;
  generateContentWithFiles(
    fileUris: string[],
    prompt: string,
    systemInstruction?: string,
    modelName?: string,
    params?: {
      temperature?: number;
      topP?: number;
      topK?: number;
      thinkingBudget?: number;
    }
  ): Promise<GeminiGenerateResponse>;
}
