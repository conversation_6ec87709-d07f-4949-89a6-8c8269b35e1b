// Example usage of the new Gemini models configuration

import { getGeminiService, GEMINI_MODELS } from './service';
import {
  getModelByName,
  getModelsByCapability,
  getRateLimitsForTier,
  getModelsByBestUse,
  isModelAvailableForTier,
} from './models';

// Example 1: Get all models that support vision
const visionModels = getModelsByCapability('supports_vision');
console.log('Models that support vision:', visionModels);

// Example 2: Get rate limits for a specific model and tier
const flashRateLimits = getRateLimitsForTier('gemini-2.5-flash', 'tier1');
console.log('Gemini 2.5 Flash Tier 1 limits:', flashRateLimits);
// Output: { rpm: 1000, tpm: 1000000, rpd: 10000 }

// Example 3: Find models best for a specific use case
const audioModels = getModelsByBestUse('audio');
console.log('Models best for audio tasks:', audioModels);

// Example 4: Check if a model is available for free tier
const isFlashFree = isModelAvailableForTier('gemini-2.5-flash', 'free');
console.log('Is Gemini 2.5 Flash available in free tier?', isFlashFree);

// Example 5: Use model aliases
const proModel = getModelByName('pro');
console.log('Pro model alias resolves to:', proModel?.name);
// Output: gemini-2.5-pro

// Example 6: Get model info from service
const service = getGeminiService();
const modelInfo = service.getModelInfo('flash-latest');
console.log('Flash latest model info:', modelInfo);

// Example 7: Check rate limits before making a request
const userTier = 'tier1'; // This would come from user's subscription
const modelName = 'gemini-2.5-pro';
const limits = service.checkRateLimit(modelName, userTier);

if (limits) {
  console.log(`Rate limits for ${modelName} on ${userTier}:`);
  console.log(`- Requests per minute: ${limits.rpm}`);
  console.log(`- Tokens per minute: ${limits.tpm}`);
  if (limits.rpd) console.log(`- Requests per day: ${limits.rpd}`);
  if (limits.tpd) console.log(`- Tokens per day: ${limits.tpd}`);
}

// Example 8: List all available models with their key features
console.log('\nAvailable Gemini Models:');
Object.entries(GEMINI_MODELS).forEach(([, model]) => {
  console.log(`\n${model.name}:`);
  console.log(`  Description: ${model.description}`);
  console.log(`  Input limit: ${model.input_token_limit.toLocaleString()} tokens`);
  console.log(`  Output limit: ${model.output_token_limit.toLocaleString()} tokens`);
  console.log(`  Free tier: ${model.rate_limits.free.rpm} RPM`);
  console.log(`  Best for: ${model.best_for.join(', ')}`);
});

// Example 9: Model selection based on requirements
function selectModelForTask(requirements: {
  needsVision?: boolean;
  needsAudio?: boolean;
  needsLongContext?: boolean;
  needsSpeed?: boolean;
  tier: 'free' | 'tier1' | 'tier2' | 'tier3';
}) {
  let candidates = Object.entries(GEMINI_MODELS);

  if (requirements.needsVision) {
    candidates = candidates.filter(([, model]) => model.capabilities.supports_vision);
  }

  if (requirements.needsAudio) {
    candidates = candidates.filter(([, model]) => model.capabilities.supports_audio);
  }

  if (requirements.needsLongContext) {
    candidates = candidates.filter(([, model]) => model.input_token_limit >= 1000000);
  }

  // Filter by tier availability
  candidates = candidates.filter(([name]) => isModelAvailableForTier(name, requirements.tier));

  // Sort by speed (RPM) if speed is needed
  if (requirements.needsSpeed) {
    candidates.sort((a, b) => {
      const aRpm = a[1].rate_limits[requirements.tier]?.rpm || 0;
      const bRpm = b[1].rate_limits[requirements.tier]?.rpm || 0;
      return bRpm - aRpm;
    });
  }

  return candidates[0] ? candidates[0][1] : null;
}

// Use the model selection function
const bestModel = selectModelForTask({
  needsVision: true,
  needsSpeed: true,
  tier: 'tier1',
});
console.log('\nBest model for vision + speed on tier1:', bestModel?.name);
