// Feature constants for role-based access control
export const FEATURES = {
  // Free tier features
  LIMITED_QUESTIONS_DAILY: 'limited_questions_daily',
  BASIC_STATS: 'basic_stats',
  PUBLIC_COMMENTS: 'public_comments',
  MOBILE_ACCESS: 'mobile_access',
  BASIC_FILTERS: 'basic_filters',

  // Premium features
  UNLIMITED_QUESTIONS: 'unlimited_questions',
  ADVANCED_ANALYTICS: 'advanced_analytics',
  PDF_DOWNLOADS: 'pdf_downloads',
  VIDEO_EXPLANATIONS: 'video_explanations',
  CUSTOM_STUDY_PLANS: 'custom_study_plans',
  NO_ADS: 'no_ads',
  OFFLINE_MODE: 'offline_mode',
  SIMULATION_EXAMS: 'simulation_exams',
  PERFORMANCE_PREDICTIONS: 'performance_predictions',
  WEAKNESS_ANALYSIS: 'weakness_analysis',
  STUDY_GROUPS: 'study_groups',
  ADVANCED_FILTERS: 'advanced_filters',
  BOOKMARK_UNLIMITED: 'bookmark_unlimited',
  QUESTION_HISTORY: 'question_history',
  CUSTOM_LISTS: 'custom_lists',

  // Content filtering features
  BANCA_FILTER: 'banca_filter',
  YEAR_FILTER: 'year_filter',
  CARGO_FILTER: 'cargo_filter',
  COMMENTED_QUESTIONS: 'commented_questions',
  LEI_SECA: 'lei_seca',
  QUESTION_STATISTICS: 'question_statistics',
  DIFFICULTY_RATING: 'difficulty_rating',

  // Study features
  CUSTOM_SIMULADOS: 'custom_simulados',
  REVISION_MODE: 'revision_mode',
  FLASH_CARDS: 'flash_cards',
  STUDY_CALENDAR: 'study_calendar',
  POMODORO_TIMER: 'pomodoro_timer',
  NOTES_SYSTEM: 'notes_system',
  HIGHLIGHT_TEXT: 'highlight_text',

  // Social/Competitive features
  RANKING_NACIONAL: 'ranking_nacional',
  RANKING_REGIONAL: 'ranking_regional',
  STUDY_STREAK: 'study_streak',
  ACHIEVEMENT_BADGES: 'achievement_badges',
  SHARE_PROGRESS: 'share_progress',
  CHALLENGE_FRIENDS: 'challenge_friends',

  // Special access
  PRIORITY_SUPPORT: 'priority_support',
  API_ACCESS: 'api_access',
  BULK_IMPORT: 'bulk_import',
  CONTENT_MODERATION: 'content_moderation',
  WHITE_LABEL: 'white_label',
  SSO_INTEGRATION: 'sso_integration',
  COMPLIANCE_REPORTS: 'compliance_reports',
  LMS_INTEGRATION: 'lms_integration',

  // Growth features
  REFERRAL_PROGRAM: 'referral_program',
  TRIAL_PREMIUM_7DAYS: 'trial_premium_7days',
  SEASONAL_DISCOUNTS: 'seasonal_discounts',
  COMBO_PACKAGES: 'combo_packages',
  EARLY_BIRD_PRICING: 'early_bird_pricing',
  STUDENT_VERIFICATION: 'student_verification',
} as const;

// Type for features
export type Feature = (typeof FEATURES)[keyof typeof FEATURES];

// Tier configurations
export const TIER_FEATURES: Record<string, Feature[]> = {
  free: [
    FEATURES.LIMITED_QUESTIONS_DAILY,
    FEATURES.BASIC_STATS,
    FEATURES.PUBLIC_COMMENTS,
    FEATURES.MOBILE_ACCESS,
    FEATURES.BASIC_FILTERS,
  ],
  basic: [
    FEATURES.UNLIMITED_QUESTIONS,
    FEATURES.BASIC_STATS,
    FEATURES.ADVANCED_ANALYTICS,
    FEATURES.MOBILE_ACCESS,
    FEATURES.OFFLINE_MODE,
    FEATURES.BASIC_FILTERS,
    FEATURES.YEAR_FILTER,
    FEATURES.BANCA_FILTER,
    FEATURES.REVISION_MODE,
    FEATURES.STUDY_CALENDAR,
  ],
  pro: [
    FEATURES.UNLIMITED_QUESTIONS,
    FEATURES.ADVANCED_ANALYTICS,
    FEATURES.VIDEO_EXPLANATIONS,
    FEATURES.PDF_DOWNLOADS,
    FEATURES.CUSTOM_SIMULADOS,
    FEATURES.NO_ADS,
    FEATURES.MOBILE_ACCESS,
    FEATURES.OFFLINE_MODE,
    FEATURES.ADVANCED_FILTERS,
    FEATURES.YEAR_FILTER,
    FEATURES.BANCA_FILTER,
    FEATURES.CARGO_FILTER,
    FEATURES.COMMENTED_QUESTIONS,
    FEATURES.QUESTION_STATISTICS,
    FEATURES.WEAKNESS_ANALYSIS,
    FEATURES.STUDY_GROUPS,
    FEATURES.CUSTOM_LISTS,
    FEATURES.NOTES_SYSTEM,
    FEATURES.STUDY_CALENDAR,
    FEATURES.POMODORO_TIMER,
    FEATURES.FLASH_CARDS,
    FEATURES.REVISION_MODE,
    FEATURES.BOOKMARK_UNLIMITED,
    FEATURES.QUESTION_HISTORY,
  ],
  master: [
    // Includes all features
    ...Object.values(FEATURES),
  ],
};

// Usage limits per tier
export const USAGE_LIMITS = {
  free: {
    questionsPerDay: 15,
    simuladosPerMonth: 2,
    videosPerMonth: 5,
    pdfsPerMonth: 0,
    aiGenerationsPerDay: 10,
    aiEmbeddingsPerDay: 50,
  },
  basic: {
    questionsPerDay: null, // unlimited
    simuladosPerMonth: 10,
    videosPerMonth: 50,
    pdfsPerMonth: 20,
    aiGenerationsPerDay: 100,
    aiEmbeddingsPerDay: 500,
  },
  pro: {
    questionsPerDay: null,
    simuladosPerMonth: null,
    videosPerMonth: null,
    pdfsPerMonth: 100,
    aiGenerationsPerDay: 1000,
    aiEmbeddingsPerDay: 5000,
  },
  master: {
    questionsPerDay: null,
    simuladosPerMonth: null,
    videosPerMonth: null,
    pdfsPerMonth: null,
    aiGenerationsPerDay: null,
    aiEmbeddingsPerDay: null,
  },
} as const;

// Role definitions
export const ROLES = {
  STUDENT_FREE: 'student_free',
  STUDENT_PREMIUM: 'student_premium',
  TEACHER: 'teacher',
  CONTENT_CURATOR: 'content_curator',
  ADMIN: 'admin',
  SUPPORT: 'support',
} as const;

export type Role = (typeof ROLES)[keyof typeof ROLES];

// Subscription tiers
export const SUBSCRIPTION_TIERS = {
  FREE: 'free',
  BASIC: 'basic',
  PRO: 'pro',
  MASTER: 'master',
} as const;

export type SubscriptionTier = (typeof SUBSCRIPTION_TIERS)[keyof typeof SUBSCRIPTION_TIERS];

// Subscription status
export const SUBSCRIPTION_STATUS = {
  ACTIVE: 'active',
  CANCELLED: 'cancelled',
  EXPIRED: 'expired',
  TRIAL: 'trial',
} as const;

export type SubscriptionStatus = (typeof SUBSCRIPTION_STATUS)[keyof typeof SUBSCRIPTION_STATUS];

// Helper function to get features for a tier
export function getFeaturesForTier(tier: SubscriptionTier): Feature[] {
  return TIER_FEATURES[tier] || TIER_FEATURES.free;
}

// Helper function to check if a tier has a specific feature
export function tierHasFeature(tier: SubscriptionTier, feature: Feature): boolean {
  const features = getFeaturesForTier(tier);
  return features.includes(feature);
}

// Helper function to get usage limit for a resource
export function getUsageLimit(
  tier: SubscriptionTier,
  resource: keyof typeof USAGE_LIMITS.free
): number | null {
  const limits = USAGE_LIMITS[tier] || USAGE_LIMITS.free;
  return limits[resource];
}
