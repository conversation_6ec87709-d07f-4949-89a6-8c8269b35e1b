import { auth } from '@/auth';
import { NextResponse } from 'next/server';

/**
 * Validates CSRF token for API routes
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export async function validateCSRFToken(_request: Request): Promise<boolean> {
  // In production, implement proper CSRF validation
  // For now, we'll check if the request has a valid session
  const session = await auth();
  return !!session;
}

/**
 * Adds security headers to responses
 */
export function addSecurityHeaders(response: NextResponse): NextResponse {
  // Security headers for production
  if (process.env.NODE_ENV === 'production') {
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    response.headers.set(
      'Permissions-Policy',
      'camera=(), microphone=(), geolocation=(), interest-cohort=()'
    );

    // Strict Transport Security (HSTS)
    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }

  return response;
}

/**
 * Middleware for protected API routes
 */
export async function withAuth<T = unknown>(
  handler: (request: Request, context?: T) => Promise<Response>
) {
  return async (request: Request, context?: T) => {
    const session = await auth();

    if (!session) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Add session to request context
    const requestWithAuth = request as Request & { auth: typeof session };
    requestWithAuth.auth = session;

    // Execute the handler
    const response = await handler(requestWithAuth, context);

    // Add security headers
    if (response instanceof NextResponse) {
      return addSecurityHeaders(response);
    }

    return response;
  };
}

/**
 * Get CSRF token for forms
 */
export async function getCSRFToken(): Promise<string> {
  // In a real implementation, generate and store CSRF tokens
  // For now, return a placeholder
  return crypto.randomUUID();
}
