import { db } from '@/db';
import { userUsage } from '@/db/schema/auth';
import { eq, and, sql } from 'drizzle-orm';
import type { Session } from 'next-auth';

export type UsageType =
  | 'questionsAnswered'
  | 'simuladosCreated'
  | 'videosWatched'
  | 'pdfsDownloaded'
  | 'aiGenerations'
  | 'aiEmbeddings';

export async function requireUsageLimit(session: Session, usageType: UsageType): Promise<boolean> {
  if (!session.user?.id) {
    return false;
  }

  const today = new Date().toISOString().split('T')[0];

  try {
    // Get current usage for today
    const currentUsage = await db
      .select()
      .from(userUsage)
      .where(and(eq(userUsage.userId, session.user.id), eq(userUsage.date, today)))
      .limit(1);

    // Create usage record if it doesn't exist
    if (currentUsage.length === 0) {
      await db.insert(userUsage).values({
        userId: session.user.id,
        date: today,
      });
    }

    // Get limits based on user tier
    const tier = session.user.tier || 'FREE';
    const limits = getUsageLimits(tier);
    const currentCount = currentUsage[0]?.[usageType] || 0;

    // Check if user has exceeded limit
    const limit = limits[usageType];
    if (limit !== -1 && currentCount >= limit) {
      return false;
    }

    // Increment usage
    await db
      .update(userUsage)
      .set({
        [usageType]: sql`${userUsage[usageType]} + 1`,
      })
      .where(and(eq(userUsage.userId, session.user.id), eq(userUsage.date, today)));

    return true;
  } catch (error) {
    console.error('Error checking usage limit:', error);
    return false;
  }
}

function getUsageLimits(tier: string): Record<UsageType, number> {
  switch (tier) {
    case 'PREMIUM':
      return {
        questionsAnswered: -1, // unlimited
        simuladosCreated: -1,
        videosWatched: -1,
        pdfsDownloaded: -1,
        aiGenerations: 1000,
        aiEmbeddings: 1000,
      };
    case 'PRO':
      return {
        questionsAnswered: -1,
        simuladosCreated: -1,
        videosWatched: -1,
        pdfsDownloaded: -1,
        aiGenerations: 500,
        aiEmbeddings: 500,
      };
    case 'FREE':
    default:
      return {
        questionsAnswered: 50,
        simuladosCreated: 5,
        videosWatched: 10,
        pdfsDownloaded: 3,
        aiGenerations: 10,
        aiEmbeddings: 20,
      };
  }
}
