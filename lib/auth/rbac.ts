import { auth } from '@/auth';
import { NextRequest, NextResponse } from 'next/server';
import type { Role, Feature } from '@/lib/auth/features';
import type { Session } from 'next-auth';
import { db } from '@/db';
import { userRoles, userFeatures, userSubscriptions } from '@/db/schema/auth';
import { eq } from 'drizzle-orm';

export async function requireRole(
  request: NextRequest,
  role: Role
): Promise<Session | NextResponse> {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }

  const userRole = session.user.role;
  if (!userRole) {
    return NextResponse.json({ error: 'User role not found' }, { status: 403 });
  }

  // Admin has access to everything
  if (userRole === 'admin') {
    return session;
  }

  // Check if user has the required role
  if (userRole !== role) {
    return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
  }

  return session;
}

export async function requireFeature(
  request: NextRequest,
  feature: Feature
): Promise<Session | NextResponse> {
  const session = await auth();

  if (!session?.user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }

  const userFeatures = session.user.features || [];

  if (!userFeatures.includes(feature)) {
    return NextResponse.json({ error: 'Feature not available' }, { status: 403 });
  }

  return session;
}

// Additional functions that might be used in the codebase
export async function assignRole(userId: string, role: Role) {
  await db
    .insert(userRoles)
    .values({ userId, role })
    .onConflictDoUpdate({
      target: userRoles.userId,
      set: { role, updatedAt: new Date() },
    });
}

export async function getUserRole(userId: string): Promise<Role | null> {
  const result = await db
    .select({ role: userRoles.role })
    .from(userRoles)
    .where(eq(userRoles.userId, userId))
    .limit(1);

  return (result[0]?.role as Role) || null;
}

export async function grantFeature(
  userId: string,
  feature: Feature,
  grantedBy?: string,
  expiresAt?: Date
) {
  await db
    .insert(userFeatures)
    .values({ userId, feature, grantedBy, expiresAt })
    .onConflictDoNothing();
}

export async function revokeFeature(userId: string, feature: Feature) {
  await db
    .delete(userFeatures)
    .where(eq(userFeatures.userId, userId) && eq(userFeatures.feature, feature));
}

export async function getUserSubscription(userId: string) {
  const result = await db
    .select()
    .from(userSubscriptions)
    .where(eq(userSubscriptions.userId, userId))
    .limit(1);

  return result[0];
}

// Usage tracking functions
/* eslint-disable @typescript-eslint/no-unused-vars */
export async function checkUsageLimit(
  _userId: string,
  _resource: string
): Promise<{ allowed: boolean; current: number; limit: number | null }> {
  // This should be implemented based on your usage tracking logic
  // For now, return default values to allow access
  return {
    allowed: true,
    current: 0,
    limit: null, // null means unlimited
  };
}

export async function incrementUsage(_userId: string, _resource: string): Promise<void> {
  // This should be implemented based on your usage tracking logic
  // For now, just a placeholder
}
/* eslint-enable @typescript-eslint/no-unused-vars */
