services:
  postgres:
    image: postgres:17-alpine
    container_name: memo-postgres
    restart: unless-stopped
    ports:
      - '5432:5432'
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-memo_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-memo_password}
      POSTGRES_DB: ${POSTGRES_DB:-memo_db}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./db/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${POSTGRES_USER:-memo_user} -d ${POSTGRES_DB:-memo_db}']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

volumes:
  postgres_data:
    name: memo_postgres_data
