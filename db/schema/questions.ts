import {
  pgTable,
  uuid,
  timestamp,
  integer,
  boolean,
  unique,
  index,
  check,
  varchar,
  text,
  jsonb,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';
import { civilServiceTests, subjects, topics } from './exams';
import { users } from './auth';
import { questionAssociatedTexts } from './question-texts';

export const questions = pgTable(
  'questions',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    civilServiceTestId: uuid('civil_service_test_id')
      .notNull()
      .references(() => civilServiceTests.id, { onDelete: 'cascade' }),
    subjectId: uuid('subject_id').references(() => subjects.id, { onDelete: 'set null' }),
    topicId: uuid('topic_id').references(() => topics.id, { onDelete: 'set null' }),
    number: integer('number').notNull(),
    associatedTextId: uuid('associated_text_id').references(() => questionAssociatedTexts.id, {
      onDelete: 'set null',
    }),
    associatedTextReferences: jsonb('associated_text_references').$type<
      Array<{ snippet: string; location_pointer: string }>
    >(),
    stem: text('stem').notNull(),
    commentary: text('commentary'),
    commentaryAuthor: varchar('commentary_author', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    unique('unique_test_question_number').on(table.civilServiceTestId, table.number),
    index('idx_questions_test').on(table.civilServiceTestId),
    index('idx_questions_subject').on(table.subjectId),
    index('idx_questions_topic').on(table.topicId),
    index('idx_questions_number').on(table.number),
    index('idx_questions_associated_text').on(table.associatedTextId),
    check('questions_number_positive', sql`${table.number} > 0`),
    check('questions_stem_not_empty', sql`char_length(${table.stem}) > 0`),
  ]
);

export const questionOptions = pgTable(
  'question_options',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    questionId: uuid('question_id')
      .notNull()
      .references(() => questions.id, { onDelete: 'cascade' }),
    order: integer('order').notNull(),
    text: text('text').notNull(),
    commentary: text('commentary'),
    commentaryAuthor: varchar('commentary_author', { length: 255 }),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    unique('unique_question_option_order').on(table.questionId, table.order),
    index('idx_options_question').on(table.questionId),
    check('options_order_positive', sql`${table.order} > 0`),
    check('options_text_not_empty', sql`char_length(${table.text}) > 0`),
  ]
);

export const questionKeys = pgTable(
  'question_keys',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    questionId: uuid('question_id')
      .notNull()
      .references(() => questions.id, { onDelete: 'cascade' }),
    correctAnswerId: uuid('correct_answer_id'),
    type: varchar('type', { length: 20 }).notNull(),
    isNull: boolean('is_null').notNull().default(false),
    changeReason: text('change_reason'),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    index('idx_keys_question').on(table.questionId),
    index('idx_keys_answer').on(table.correctAnswerId),
    check(
      'keys_answer_or_null',
      sql`(${table.correctAnswerId} IS NOT NULL AND ${table.isNull} = false) OR (${table.correctAnswerId} IS NULL AND ${table.isNull} = true)`
    ),
    check('keys_type_valid', sql`${table.type} IN ('MULTIPLE_CHOICE', 'TRUE_FALSE', 'ESSAY')`),
  ]
);

export const images = pgTable(
  'images',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    url: varchar('url', { length: 2048 }).unique().notNull(),
    altText: varchar('alt_text', { length: 500 }),

    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    check('images_url_not_empty', sql`char_length(${table.url}) > 0`),
    check('images_url_valid', sql`${table.url} ~ '^https?://.*$'`),
  ]
);

export const questionImages = pgTable(
  'question_images',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    questionId: uuid('question_id')
      .notNull()
      .references(() => questions.id, { onDelete: 'cascade' }),
    imageId: uuid('image_id')
      .notNull()
      .references(() => images.id, { onDelete: 'cascade' }),
    order: integer('order').notNull().default(0),

    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    unique('unique_question_image').on(table.questionId, table.imageId),
    index('idx_question_images_question').on(table.questionId),
    index('idx_question_images_image').on(table.imageId),
    check('question_images_order_positive', sql`${table.order} >= 0`),
  ]
);

export const questionAnswers = pgTable(
  'question_answers',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    questionId: uuid('question_id')
      .notNull()
      .references(() => questions.id, { onDelete: 'cascade' }),
    userId: varchar('user_id', { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    selectedAnswer: varchar('selected_answer', { length: 255 }).notNull(),
    isCorrect: boolean('is_correct').notNull(),
    responseTimeMs: integer('response_time_ms'),
    answeredAt: timestamp('answered_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    index('idx_question_answers_question').on(table.questionId),
    index('idx_question_answers_user').on(table.userId),
    index('idx_question_answers_answered_at').on(table.answeredAt),
    index('idx_question_answers_user_question').on(table.userId, table.questionId),
  ]
);

// Relations
export const questionsRelations = relations(questions, ({ one, many }) => ({
  civilServiceTest: one(civilServiceTests, {
    fields: [questions.civilServiceTestId],
    references: [civilServiceTests.id],
  }),
  subject: one(subjects, {
    fields: [questions.subjectId],
    references: [subjects.id],
  }),
  topic: one(topics, {
    fields: [questions.topicId],
    references: [topics.id],
  }),
  associatedText: one(questionAssociatedTexts, {
    fields: [questions.associatedTextId],
    references: [questionAssociatedTexts.id],
  }),
  questionOptions: many(questionOptions),
  questionKeys: many(questionKeys),
  questionImages: many(questionImages),
  questionAnswers: many(questionAnswers),
}));

export const questionOptionsRelations = relations(questionOptions, ({ one }) => ({
  question: one(questions, {
    fields: [questionOptions.questionId],
    references: [questions.id],
  }),
}));

export const questionKeysRelations = relations(questionKeys, ({ one }) => ({
  question: one(questions, {
    fields: [questionKeys.questionId],
    references: [questions.id],
  }),
  correctAnswer: one(questionOptions, {
    fields: [questionKeys.correctAnswerId],
    references: [questionOptions.id],
  }),
}));

export const imagesRelations = relations(images, ({ many }) => ({
  questionImages: many(questionImages),
}));

export const questionImagesRelations = relations(questionImages, ({ one }) => ({
  question: one(questions, {
    fields: [questionImages.questionId],
    references: [questions.id],
  }),
  image: one(images, {
    fields: [questionImages.imageId],
    references: [images.id],
  }),
}));

export const questionAnswersRelations = relations(questionAnswers, ({ one }) => ({
  question: one(questions, {
    fields: [questionAnswers.questionId],
    references: [questions.id],
  }),
  user: one(users, {
    fields: [questionAnswers.userId],
    references: [users.id],
  }),
}));
