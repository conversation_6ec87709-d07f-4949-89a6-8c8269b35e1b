import {
  pgTable,
  uuid,
  varchar,
  text,
  timestamp,
  integer,
  index,
  unique,
  primaryKey,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';
import { questions } from './questions';
import { users } from './auth';

export const questionComments = pgTable(
  'question_comments',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    questionId: uuid('question_id')
      .notNull()
      .references(() => questions.id, { onDelete: 'cascade' }),
    userId: varchar('user_id', { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    parentId: uuid('parent_id').references((): any => questionComments.id, { onDelete: 'cascade' }),
    content: text('content').notNull(),
    votes: integer('votes').notNull().default(0),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
    deletedAt: timestamp('deleted_at', { withTimezone: true }),
    editHistory: text('edit_history').default('[]').notNull(), // JSON array of edit timestamps
  },
  (table) => [
    index('idx_question_comments_question').on(table.questionId),
    index('idx_question_comments_user').on(table.userId),
    index('idx_question_comments_parent').on(table.parentId),
    index('idx_question_comments_created').on(table.createdAt),
  ]
);

export const commentEditNotifications = pgTable(
  'comment_edit_notifications',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    commentId: uuid('comment_id')
      .notNull()
      .references(() => questionComments.id, { onDelete: 'cascade' }),
    replyId: uuid('reply_id')
      .notNull()
      .references(() => questionComments.id, { onDelete: 'cascade' }),
    editTimestamp: timestamp('edit_timestamp', { withTimezone: true }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    index('idx_edit_notifications_comment').on(table.commentId),
    index('idx_edit_notifications_reply').on(table.replyId),
  ]
);

export const questionCommentVotes = pgTable(
  'question_comment_votes',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    commentId: uuid('comment_id')
      .notNull()
      .references(() => questionComments.id, { onDelete: 'cascade' }),
    userId: varchar('user_id', { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    voteType: varchar('vote_type', { length: 10 }).notNull(), // 'up' or 'down'
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    unique('unique_user_comment_vote').on(table.userId, table.commentId),
    index('idx_comment_votes_comment').on(table.commentId),
    index('idx_comment_votes_user').on(table.userId),
  ]
);

// Relations
export const commentEditNotificationsRelations = relations(commentEditNotifications, ({ one }) => ({
  comment: one(questionComments, {
    fields: [commentEditNotifications.commentId],
    references: [questionComments.id],
    relationName: 'editNotificationComment',
  }),
  reply: one(questionComments, {
    fields: [commentEditNotifications.replyId],
    references: [questionComments.id],
    relationName: 'editNotificationReply',
  }),
}));

export const questionCommentsRelations = relations(questionComments, ({ one, many }) => ({
  question: one(questions, {
    fields: [questionComments.questionId],
    references: [questions.id],
  }),
  user: one(users, {
    fields: [questionComments.userId],
    references: [users.id],
  }),
  parent: one(questionComments, {
    fields: [questionComments.parentId],
    references: [questionComments.id],
    relationName: 'parentComment',
  }),
  replies: many(questionComments, {
    relationName: 'parentComment',
  }),
  votes: many(questionCommentVotes),
  editNotificationsAsComment: many(commentEditNotifications, {
    relationName: 'editNotificationComment',
  }),
  editNotificationsAsReply: many(commentEditNotifications, {
    relationName: 'editNotificationReply',
  }),
}));

export const questionCommentVotesRelations = relations(questionCommentVotes, ({ one }) => ({
  comment: one(questionComments, {
    fields: [questionCommentVotes.commentId],
    references: [questionComments.id],
  }),
  user: one(users, {
    fields: [questionCommentVotes.userId],
    references: [users.id],
  }),
}));

// Type exports
export type QuestionComment = typeof questionComments.$inferSelect;
export type NewQuestionComment = typeof questionComments.$inferInsert;
export type QuestionCommentVote = typeof questionCommentVotes.$inferSelect;
export type NewQuestionCommentVote = typeof questionCommentVotes.$inferInsert;
export type CommentEditNotification = typeof commentEditNotifications.$inferSelect;
export type NewCommentEditNotification = typeof commentEditNotifications.$inferInsert;
