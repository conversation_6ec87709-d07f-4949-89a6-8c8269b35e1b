import {
  pgTable,
  uuid,
  timestamp,
  varchar,
  text,
  boolean,
  integer,
  jsonb,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './auth';

// Prompt Templates table (unified for all prompt types)
export const promptTemplates = pgTable('prompt_templates', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  feature: varchar('feature', { length: 50 }).notNull().default('ocr'),
  promptType: varchar('prompt_type', { length: 50 }).notNull(),
  promptContent: text('prompt_content').notNull(),
  modelName: varchar('model_name', { length: 100 }).notNull().default('gemini-2.5-flash'),
  isDefault: boolean('is_default').notNull().default(false),
  isSystem: boolean('is_system').notNull().default(false),
  isEnabled: boolean('is_enabled').notNull().default(true),
  icon: varchar('icon', { length: 50 }),
  displayOrder: integer('display_order').default(0),
  metadata: jsonb('metadata'),
  createdBy: varchar('created_by', { length: 255 }).references(() => users.id, {
    onDelete: 'set null',
  }),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
});

// OCR Processing History table (for tracking OCR operations)
export const ocrProcessingHistory = pgTable('ocr_processing_history', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: varchar('user_id', { length: 255 })
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  templateId: uuid('template_id').references(() => promptTemplates.id, { onDelete: 'set null' }),
  fileName: varchar('file_name', { length: 255 }).notNull(),
  fileSize: integer('file_size').notNull(),
  mimeType: varchar('mime_type', { length: 100 }).notNull(),
  modelUsed: varchar('model_used', { length: 100 }).notNull(),
  status: varchar('status', { length: 50 }).notNull().default('processing'),
  extractedData: jsonb('extracted_data'),
  errorMessage: text('error_message'),
  processingTimeMs: integer('processing_time_ms'),
  tokensUsed: integer('tokens_used'),
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
});

// Relations
export const promptTemplatesRelations = relations(promptTemplates, ({ one, many }) => ({
  createdByUser: one(users, {
    fields: [promptTemplates.createdBy],
    references: [users.id],
  }),
  processingHistory: many(ocrProcessingHistory),
}));

export const ocrProcessingHistoryRelations = relations(ocrProcessingHistory, ({ one }) => ({
  user: one(users, {
    fields: [ocrProcessingHistory.userId],
    references: [users.id],
  }),
  template: one(promptTemplates, {
    fields: [ocrProcessingHistory.templateId],
    references: [promptTemplates.id],
  }),
}));
