import { pgTable, uuid, text, varchar, timestamp, index, unique } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { questions } from './questions';

export const questionAssociatedTexts = pgTable(
  'question_associated_texts',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    content: text('content').notNull(),
    hash: varchar('hash', { length: 64 }).notNull(), // SHA-256 hash for deduplication
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    unique('unique_content_hash').on(table.hash),
    index('idx_associated_texts_hash').on(table.hash),
  ]
);

// Relations
export const questionAssociatedTextsRelations = relations(questionAssociatedTexts, ({ many }) => ({
  questions: many(questions),
}));
