import {
  pgTable,
  integer,
  real,
  timestamp,
  uuid,
  text,
  varchar,
  boolean,
  unique,
  index,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { questions } from './questions';
import { users } from './auth';

// Card states based on FSRS (stored as integers)
// 0 = New, 1 = Learning, 2 = Review, 3 = Relearning
export const CardState = {
  New: 0,
  Learning: 1,
  Review: 2,
  Relearning: 3,
} as const;

// Rating values for reviews (stored as integers)
// 1 = Again, 2 = Hard, 3 = Good, 4 = Easy
export const Rating = {
  Again: 1,
  Hard: 2,
  Good: 3,
  Easy: 4,
} as const;

export type CardStateType = (typeof CardState)[keyof typeof CardState];
export type RatingType = (typeof Rating)[keyof typeof Rating];

// FSRS Cards table - represents the current state of each flashcard
export const fsrsCards = pgTable('fsrs_cards', {
  id: uuid('id').defaultRandom().primary<PERSON>ey(),
  questionId: uuid('question_id')
    .notNull()
    .references(() => questions.id, { onDelete: 'cascade' }),

  // FSRS Card properties
  due: timestamp('due').notNull().defaultNow(),
  stability: real('stability').notNull().default(0),
  difficulty: real('difficulty').notNull().default(0),
  elapsedDays: integer('elapsed_days').notNull().default(0),
  scheduledDays: integer('scheduled_days').notNull().default(0),
  learningSteps: integer('learning_steps').notNull().default(0),
  reps: integer('reps').notNull().default(0),
  lapses: integer('lapses').notNull().default(0),
  state: integer('state').notNull().default(CardState.New),
  lastReview: timestamp('last_review'),

  // Metadata
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// FSRS Review logs - tracks history of all reviews
export const fsrsReviewLogs = pgTable('fsrs_review_logs', {
  id: uuid('id').defaultRandom().primaryKey(),
  cardId: uuid('card_id')
    .notNull()
    .references(() => fsrsCards.id, { onDelete: 'cascade' }),

  // Review information
  rating: integer('rating').notNull(),
  state: integer('state').notNull(),
  due: timestamp('due').notNull(),
  stability: real('stability').notNull(),
  difficulty: real('difficulty').notNull(),
  elapsedDays: integer('elapsed_days').notNull(),
  lastElapsedDays: integer('last_elapsed_days').notNull(),
  scheduledDays: integer('scheduled_days').notNull(),
  learningSteps: integer('learning_steps').notNull(),
  review: timestamp('review').notNull(),

  // Metadata
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// FSRS Parameters table - stores user-specific or global FSRS parameters
export const fsrsParameters = pgTable('fsrs_parameters', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: varchar('user_id', { length: 255 })
    .references(() => users.id, { onDelete: 'cascade' })
    .unique(), // null for global parameters

  // FSRS V6 Parameters (21 weights: w0-w20)
  // Default values based on FSRS V6 standard
  w0: real('w0').notNull().default(0.2172),
  w1: real('w1').notNull().default(1.1771),
  w2: real('w2').notNull().default(3.2602),
  w3: real('w3').notNull().default(16.1507),
  w4: real('w4').notNull().default(7.0114),
  w5: real('w5').notNull().default(0.57),
  w6: real('w6').notNull().default(2.0966),
  w7: real('w7').notNull().default(0.0069),
  w8: real('w8').notNull().default(1.5261),
  w9: real('w9').notNull().default(0.112),
  w10: real('w10').notNull().default(1.0178),
  w11: real('w11').notNull().default(1.849),
  w12: real('w12').notNull().default(0.1133),
  w13: real('w13').notNull().default(0.3127),
  w14: real('w14').notNull().default(2.2934),
  w15: real('w15').notNull().default(0.2191),
  w16: real('w16').notNull().default(3.0004),
  w17: real('w17').notNull().default(0.7536),
  w18: real('w18').notNull().default(0.3332),
  w19: real('w19').notNull().default(0.1437),
  w20: real('w20').notNull().default(0.2), // Personalizable parameter (0.1-0.8)

  // Other FSRS settings
  requestRetention: real('request_retention').notNull().default(0.9), // Desired retention (0.7-0.97)
  maximumInterval: integer('maximum_interval').notNull().default(36500), // 100 years default

  // Learning steps in minutes (stored as JSON array)
  learningSteps: text('learning_steps').notNull().default('[1, 10]'), // Default: 1 minute, 10 minutes

  // Additional settings
  enableFuzz: integer('enable_fuzz').notNull().default(1), // Boolean as integer
  enableShortTerm: integer('enable_short_term').notNull().default(0), // Boolean as integer

  // Metadata
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// User's decks - links users to questions they want to study
export const fsrsDecks = pgTable(
  'fsrs_decks',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: varchar('user_id', { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    title: varchar('title', { length: 255 }).notNull(),
    description: text('description'),
    isActive: boolean('is_active').notNull().default(true),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [index('idx_fsrs_decks_user').on(table.userId)]
);

// Link questions to user's decks
export const fsrsDeckCards = pgTable(
  'fsrs_deck_cards',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    deckId: uuid('deck_id')
      .notNull()
      .references(() => fsrsDecks.id, { onDelete: 'cascade' }),
    questionId: uuid('question_id')
      .notNull()
      .references(() => questions.id, { onDelete: 'cascade' }),
    addedAt: timestamp('added_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    unique('unique_deck_card').on(table.deckId, table.questionId),
    index('idx_fsrs_deck_cards_deck').on(table.deckId),
    index('idx_fsrs_deck_cards_question').on(table.questionId),
  ]
);

// Relations
export const fsrsCardsRelations = relations(fsrsCards, ({ one, many }) => ({
  question: one(questions, {
    fields: [fsrsCards.questionId],
    references: [questions.id],
  }),
  reviewLogs: many(fsrsReviewLogs),
}));

export const fsrsReviewLogsRelations = relations(fsrsReviewLogs, ({ one }) => ({
  card: one(fsrsCards, {
    fields: [fsrsReviewLogs.cardId],
    references: [fsrsCards.id],
  }),
}));

export const fsrsParametersRelations = relations(fsrsParameters, ({ one }) => ({
  user: one(users, {
    fields: [fsrsParameters.userId],
    references: [users.id],
  }),
}));

export const fsrsDecksRelations = relations(fsrsDecks, ({ one, many }) => ({
  user: one(users, {
    fields: [fsrsDecks.userId],
    references: [users.id],
  }),
  cards: many(fsrsDeckCards),
}));

export const fsrsDeckCardsRelations = relations(fsrsDeckCards, ({ one }) => ({
  deck: one(fsrsDecks, {
    fields: [fsrsDeckCards.deckId],
    references: [fsrsDecks.id],
  }),
  question: one(questions, {
    fields: [fsrsDeckCards.questionId],
    references: [questions.id],
  }),
}));
