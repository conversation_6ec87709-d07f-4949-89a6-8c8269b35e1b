import { pgTable, uuid, text, timestamp, jsonb, integer, boolean } from 'drizzle-orm/pg-core';
import { users } from './auth';

/**
 * Conversations table
 * Stores chat conversations/threads for each user
 */
export const conversations = pgTable('conversations', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: text('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  title: text('title').notNull().default('New Chat'),
  model: text('model').notNull().default('openai/gpt-4o-mini'),
  systemPrompt: text('system_prompt'),
  archived: boolean('archived').notNull().default(false),
  metadata: jsonb('metadata').$type<{
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
    reasoning?: {
      enabled?: boolean;
      effort?: 'low' | 'medium' | 'high';
      maxTokens?: number;
    };
  }>(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

/**
 * Messages table
 * Stores individual messages within conversations
 */
export const messages = pgTable('messages', {
  id: uuid('id').defaultRandom().primaryKey(),
  conversationId: uuid('conversation_id')
    .notNull()
    .references(() => conversations.id, { onDelete: 'cascade' }),
  role: text('role', { enum: ['user', 'assistant', 'system'] }).notNull(),
  content: text('content').notNull(),
  reasoning: text('reasoning'), // For models that support reasoning/thinking
  model: text('model'), // Track which model generated this response
  metadata: jsonb('metadata').$type<{
    usage?: {
      promptTokens?: number;
      completionTokens?: number;
      totalTokens?: number;
      cost?: number;
      reasoningTokens?: number;
      cachedTokens?: number;
    };
    latency?: {
      timeToFirstToken?: number;
      totalTime?: number;
    };
    performance?: {
      tokensPerSecond?: number;
    };
    generationId?: string;
  }>(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

/**
 * Model Settings table
 * Stores global model settings (admin only)
 */
export const modelSettings = pgTable('model_settings', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: text('name').notNull().unique(), // e.g., 'default', 'study-assistant', etc.
  systemPrompt: text('system_prompt').notNull(),
  defaultModel: text('default_model').notNull().default('openai/gpt-4o-mini'),
  temperature: integer('temperature').notNull().default(70), // Stored as int (0-200) for precision
  maxTokens: integer('max_tokens').notNull().default(1000),
  topP: integer('top_p').notNull().default(100), // Stored as int (0-100)
  frequencyPenalty: integer('frequency_penalty').notNull().default(0), // Stored as int (-200 to 200)
  presencePenalty: integer('presence_penalty').notNull().default(0), // Stored as int (-200 to 200)
  reasoning: jsonb('reasoning').$type<{
    enabled?: boolean;
    effort?: 'low' | 'medium' | 'high';
    maxTokens?: number;
  }>(),
  availableModels: jsonb('available_models')
    .$type<
      Array<{
        value: string;
        label: string;
        category?: string;
        enabled: boolean;
        customLimits?: {
          temperature?: { min: number; max: number; default: number };
          maxTokens?: { min: number; max: number; default: number };
          topP?: { min: number; max: number; default: number };
          frequencyPenalty?: { min: number; max: number; default: number };
          presencePenalty?: { min: number; max: number; default: number };
          contextLength?: number;
          maxCompletionTokens?: number;
          reasoningMaxTokens?: { min: number; max: number; default: number };
        };
      }>
    >()
    .default([]),
  isActive: boolean('is_active').notNull().default(false), // Only one can be active
  createdBy: text('created_by')
    .notNull()
    .references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Export types for TypeScript
export type Conversation = typeof conversations.$inferSelect;
export type NewConversation = typeof conversations.$inferInsert;
export type Message = typeof messages.$inferSelect;
export type NewMessage = typeof messages.$inferInsert;
export type ModelSettings = typeof modelSettings.$inferSelect;
export type NewModelSettings = typeof modelSettings.$inferInsert;
