import {
  pgTable,
  uuid,
  timestamp,
  integer,
  date,
  unique,
  index,
  check,
  varchar,
  foreignKey,
} from 'drizzle-orm/pg-core';
import { sql, relations } from 'drizzle-orm';

export const institutions = pgTable(
  'institutions',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    code: varchar('code', { length: 50 }),
    name: varchar('name', { length: 255 }).notNull().unique(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    index('idx_institutions_code').on(table.code),
    check('institutions_name_not_empty', sql`char_length(${table.name}) > 0`),
  ]
);

export const examBoards = pgTable(
  'exam_boards',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    code: varchar('code', { length: 50 }),
    name: varchar('name', { length: 255 }).notNull().unique(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    index('idx_exam_boards_code').on(table.code),
    check('exam_boards_name_not_empty', sql`char_length(${table.name}) > 0`),
  ]
);

export const positions = pgTable(
  'positions',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    code: varchar('code', { length: 50 }),
    name: varchar('name', { length: 255 }).notNull().unique(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    index('idx_positions_code').on(table.code),
    check('positions_name_not_empty', sql`char_length(${table.name}) > 0`),
  ]
);

export const specializations = pgTable(
  'specializations',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    positionId: uuid('position_id')
      .notNull()
      .references(() => positions.id, { onDelete: 'cascade' }),
    code: varchar('code', { length: 50 }),
    name: varchar('name', { length: 255 }).notNull(),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    unique('unique_position_specialization').on(table.positionId, table.name),
    index('idx_specializations_position').on(table.positionId),
    check('specializations_name_not_empty', sql`char_length(${table.name}) > 0`),
  ]
);

export const civilServiceExams = pgTable(
  'civil_service_exams',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    institutionId: uuid('institution_id')
      .notNull()
      .references(() => institutions.id, { onDelete: 'restrict' }),
    examBoardId: uuid('exam_board_id')
      .notNull()
      .references(() => examBoards.id, { onDelete: 'restrict' }),
    positionId: uuid('position_id')
      .notNull()
      .references(() => positions.id, { onDelete: 'restrict' }),
    specializationId: uuid('specialization_id').references(() => specializations.id, {
      onDelete: 'restrict',
    }),
    year: integer('year').notNull(),
    status: varchar('status', { length: 20 }).notNull().default('INATIVO'),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    unique('unique_exam_combination').on(
      table.institutionId,
      table.examBoardId,
      table.positionId,
      table.specializationId,
      table.year
    ),
    index('idx_exams_institution').on(table.institutionId),
    index('idx_exams_board').on(table.examBoardId),
    index('idx_exams_position').on(table.positionId),
    index('idx_exams_year_status').on(table.year, table.status),
    check(
      'exams_year_valid',
      sql`${table.year} >= 1900 AND ${table.year} <= extract(year from CURRENT_DATE) + 5`
    ),
    check(
      'exams_status_valid',
      sql`${table.status} IN ('ATIVO', 'INATIVO', 'CANCELADO', 'SUSPENSO')`
    ),
  ]
);

export const civilServiceTests = pgTable(
  'civil_service_tests',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    civilServiceExamId: uuid('civil_service_exam_id').notNull(),
    type: varchar('type', { length: 20 }).notNull(),
    booklet: varchar('booklet', { length: 50 }),
    applicationDate: date('application_date'),

    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    foreignKey({
      columns: [table.civilServiceExamId],
      foreignColumns: [civilServiceExams.id],
      name: 'cst_exam_fk', // Short name under 63 characters
    }).onDelete('cascade'),
    unique('unique_exam_test').on(table.civilServiceExamId, table.type, table.booklet),
    index('idx_tests_exam').on(table.civilServiceExamId),
    index('idx_tests_date').on(table.applicationDate),
    check(
      'tests_date_reasonable',
      sql`${table.applicationDate} >= '1900-01-01' AND ${table.applicationDate} <= CURRENT_DATE + INTERVAL '10 years'`
    ),
    check(
      'tests_type_valid',
      sql`${table.type} IN ('MULTIPLA_ESCOLHA', 'CERTO_ERRADO', 'DISCURSIVA')`
    ),
  ]
);

export const subjects = pgTable(
  'subjects',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    name: varchar('name', { length: 255 }).notNull().unique(),

    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [check('subjects_name_not_empty', sql`char_length(${table.name}) > 0`)]
);

export const topics: any = pgTable(
  'topics',
  {
    id: uuid('id').primaryKey().defaultRandom(),
    subjectId: uuid('subject_id')
      .notNull()
      .references(() => subjects.id, { onDelete: 'cascade' }),
    parentTopicId: uuid('parent_topic_id').references((): any => topics.id, {
      onDelete: 'cascade',
    }),
    name: varchar('name', { length: 255 }).notNull(),
    level: integer('level').notNull().default(0),
    path: varchar('path', { length: 1000 }),
    createdAt: timestamp('created_at', { withTimezone: true }).defaultNow().notNull(),
    updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow().notNull(),
  },
  (table) => [
    unique('unique_topic_hierarchy').on(table.subjectId, table.parentTopicId, table.name),
    index('idx_topics_subject').on(table.subjectId),
    index('idx_topics_parent').on(table.parentTopicId),
    index('idx_topics_level').on(table.level),
    check('topics_name_not_empty', sql`char_length(${table.name}) > 0`),
    check('topics_level_positive', sql`${table.level} >= 0`),
  ]
);

// Relations
export const institutionsRelations = relations(institutions, ({ many }) => ({
  civilServiceExams: many(civilServiceExams),
}));

export const examBoardsRelations = relations(examBoards, ({ many }) => ({
  civilServiceExams: many(civilServiceExams),
}));

export const positionsRelations = relations(positions, ({ many }) => ({
  specializations: many(specializations),
  civilServiceExams: many(civilServiceExams),
}));

export const specializationsRelations = relations(specializations, ({ one, many }) => ({
  position: one(positions, {
    fields: [specializations.positionId],
    references: [positions.id],
  }),
  civilServiceExams: many(civilServiceExams),
}));

export const civilServiceExamsRelations = relations(civilServiceExams, ({ one, many }) => ({
  institution: one(institutions, {
    fields: [civilServiceExams.institutionId],
    references: [institutions.id],
  }),
  examBoard: one(examBoards, {
    fields: [civilServiceExams.examBoardId],
    references: [examBoards.id],
  }),
  position: one(positions, {
    fields: [civilServiceExams.positionId],
    references: [positions.id],
  }),
  specialization: one(specializations, {
    fields: [civilServiceExams.specializationId],
    references: [specializations.id],
  }),
  civilServiceTests: many(civilServiceTests),
}));

export const civilServiceTestsRelations = relations(civilServiceTests, ({ one }) => ({
  civilServiceExam: one(civilServiceExams, {
    fields: [civilServiceTests.civilServiceExamId],
    references: [civilServiceExams.id],
  }),
  // Note: questions relation is defined in questions.ts to avoid circular dependency
}));

export const subjectsRelations = relations(subjects, ({ many }) => ({
  topics: many(topics),
}));

export const topicsRelations = relations(topics, ({ one, many }) => ({
  subject: one(subjects, {
    fields: [topics.subjectId],
    references: [subjects.id],
  }),
  parentTopic: one(topics, {
    fields: [topics.parentTopicId],
    references: [topics.id],
  }),
  childTopics: many(topics),
}));
